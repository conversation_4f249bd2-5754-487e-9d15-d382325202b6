<script setup lang="ts">
import { ref, onMounted } from "vue";
import { FolderOpened, Document, Bell } from "@element-plus/icons-vue";
import { ElMessage, ElButton } from "element-plus";

defineOptions({
  name: "Welcome",
});

const isVisible = ref(false);
const showSubtitle = ref(false);
const testingRabbitMQ = ref(false);

onMounted(() => {
  // 延迟显示主标题
  setTimeout(() => {
    isVisible.value = true;
  }, 300);

  // 延迟显示副标题
  setTimeout(() => {
    showSubtitle.value = true;
  }, 1500);
});

// 测试RabbitMQ消息推送
const testRabbitMQNotification = async () => {
  if (!window.ipcRenderer) {
    ElMessage.error('IPC渲染器不可用');
    return;
  }

  testingRabbitMQ.value = true;

  try {
    const result = await window.ipcRenderer.invoke('rabbitmq:test-notification');

    if (result?.success) {
      ElMessage.success('测试通知已发送！请查看系统通知和应用内通知。');
    } else {
      ElMessage.error('发送测试通知失败: ' + (result?.error || '未知错误'));
    }
  } catch (error) {
    console.error('测试RabbitMQ通知失败:', error);
    ElMessage.error('测试通知失败: ' + (error as Error).message);
  } finally {
    testingRabbitMQ.value = false;
  }
};

// 检查RabbitMQ连接状态
const checkRabbitMQStatus = async () => {
  if (!window.ipcRenderer) {
    ElMessage.error('IPC渲染器不可用');
    return;
  }

  try {
    const status = await window.ipcRenderer.invoke('rabbitmq:get-connection-status');

    if (status?.connected) {
      ElMessage.success('RabbitMQ连接正常');
    } else {
      ElMessage.warning('RabbitMQ连接失败: ' + (status?.error || '未知错误'));
    }
  } catch (error) {
    console.error('检查RabbitMQ状态失败:', error);
    ElMessage.error('检查连接状态失败: ' + (error as Error).message);
  }
};

// 测试模型结果通知
const testModelResultNotification = () => {
  // 创建模拟的模型结果数据
  const mockModelResult = {
    taskId: 'test-task-' + Date.now(),
    modelType: 'DecisionTree',
    algorithm: 'CART',
    status: 'completed',
    timestamp: Date.now(),
    duration: 125.5,
    datasetSize: 1000,
    metrics: {
      accuracy: 0.8567,
      precision: 0.8234,
      recall: 0.8901,
      f1_score: 0.8556
    },
    eval: {
      train: {
        metrics: {
          accuracy: 0.8567,
          precision: 0.8234,
          recall: 0.8901,
          f1_score: 0.8556
        },
        y_true: [1, 0, 1, 1, 0, 1, 0, 1, 0, 1],
        y_predict: [1, 0, 1, 0, 0, 1, 0, 1, 1, 1]
      },
      test: {
        metrics: {
          accuracy: 0.8234,
          precision: 0.8012,
          recall: 0.8456,
          f1_score: 0.8229
        },
        y_true: [1, 0, 1, 1, 0, 1, 0, 1],
        y_predict: [1, 0, 1, 0, 0, 1, 0, 1]
      }
    }
  };

  // 模拟通知点击，直接打开结果页面
  const resultQuery = {
    taskId: mockModelResult.taskId,
    modelType: mockModelResult.modelType,
    result: JSON.stringify(mockModelResult)
  };

  const baseUrl = window.location.origin + window.location.pathname;
  const resultUrl = `${baseUrl}#/modelManagement/mlResult?${new URLSearchParams(resultQuery).toString()}`;

  window.open(resultUrl, "_blank");
  ElMessage.success('测试模型结果页面已打开');
};
</script>

<template>
  <div class="welcome-container">
    <!-- 背景装饰元素 -->
    <div class="background-decoration">
      <div class="floating-circle circle-1"></div>
      <div class="floating-circle circle-2"></div>
      <div class="floating-circle circle-3"></div>
      <div class="floating-circle circle-4"></div>
    </div>

    <!-- 主要内容 -->
    <div class="welcome-content">
      <div class="logo-container">
        <!-- 移除图标，只保留空间用于布局 -->
      </div>

      <h1
        :class="{ 'fade-in-up': isVisible }"
        class="main-title"
      >
        欢迎使用
      </h1>

      <p
        :class="{ 'fade-in-up': showSubtitle }"
        class="subtitle typewriter"
      >
        请使用菜单栏的"文件"菜单来打开文件或导入项目
      </p>

      <!-- RabbitMQ测试按钮 -->
      <div class="test-buttons" v-if="showSubtitle">
        <el-button
          type="primary"
          :icon="Bell"
          :loading="testingRabbitMQ"
          @click="testRabbitMQNotification"
          class="test-btn"
        >
          测试消息推送
        </el-button>

        <el-button
          type="info"
          @click="checkRabbitMQStatus"
          class="test-btn"
        >
          检查连接状态
        </el-button>

        <el-button
          type="success"
          @click="testModelResultNotification"
          class="test-btn"
        >
          测试模型结果页面
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.welcome-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #f5faff 0%, #dff1ff 100%);
}

/* 深色模式适配 */
html.dark .welcome-container {
  background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
}

/* 背景装饰动画 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(116, 185, 255, 0.05));
  animation: background-bubble-float 8s ease-in-out infinite;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.circle-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 30%;
  animation-delay: 1s;
}

/* 背景气泡上浮动画 */
@keyframes background-bubble-float {
  0% {
    transform: translateY(100vh) scale(0.5);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100px) scale(1.2);
    opacity: 0;
  }
}

/* 主要内容样式 */
.welcome-content {
  text-align: center;
  position: relative;
  z-index: 2;
  max-width: 600px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* 深色模式适配 */
html.dark .welcome-content {
  background: rgba(0, 0, 0, 0.7);
  color: var(--el-text-color-primary);
}

/* Logo 容器 */
.logo-container {
  margin-bottom: 30px;
}

/* Logo容器保留用于布局间距 */

/* 主标题样式 */
.main-title {
  font-size: 3em;
  margin-bottom: 20px;
  color: #333;
  font-weight: bold;
  background: linear-gradient(45deg, #409eff, #67c23a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

/* 深色模式适配 */
html.dark .main-title {
  background: linear-gradient(45deg, #409eff, #67c23a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.main-title.fade-in-up {
  opacity: 1;
  transform: translateY(0);
}

/* 副标题样式 */
.subtitle {
  font-size: 1.3em;
  color: #666;
  margin-bottom: 30px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s ease-out 0.3s;
}

/* 深色模式适配 */
html.dark .subtitle {
  color: var(--el-text-color-regular);
}

.subtitle.fade-in-up {
  opacity: 1;
  transform: translateY(0);
}

/* 打字机效果 */
.typewriter {
  overflow: hidden;
  border-right: 2px solid #409eff;
  white-space: nowrap;
  animation: typing 3s steps(40, end) 1.5s forwards, blink-caret 0.75s step-end infinite;
  width: 0;
}

@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: #409eff;
  }
}

/* 测试按钮样式 */
.test-buttons {
  margin-top: 30px;
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
  opacity: 0;
  transform: translateY(20px);
  animation: fade-in-up 0.8s ease-out 2.5s forwards;
}

.test-btn {
  border-radius: 25px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.test-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

@keyframes fade-in-up {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-content {
    margin: 20px;
    padding: 30px 20px;
  }

  .main-title {
    font-size: 2.5em;
  }

  .subtitle {
    font-size: 1.1em;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .floating-circle {
    display: none;
  }

  .test-buttons {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .test-btn {
    width: 200px;
  }
}
</style>
