<template>
  <div style="width: 96%; height: 100vh">
    <el-card style="margin-top: 30px">
      <div style="display: flex; justify-content: center; margin-bottom: 20px">
        <el-select
          v-model="selectedColumn"
          placeholder="请选择分析列"
          style="margin-bottom: 20px; width: 300px"
          popper-class="step-one"
        >
          <el-option
            v-for="(col, index) in header_table"
            :key="index"
            :label="col"
            :value="index"
            :disabled="disabledReasons[index] !== null"
            :placeholder="disabledReasons[index] || ''"
          />
        </el-select>
      </div>
      <div ref="chartRef" style="width: 100%; height: 60vh" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { useDark, useECharts } from "@pureadmin/utils";
import { exportChartInstance } from "@/utils/chartExport";

const { data: data_table, header: header_table } = defineProps([
  "data",
  "header"
]);
const { isDark } = useDark();
const theme = computed(() => (isDark.value ? "dark" : "default"));
const chartRef = ref();
// getInstance用于获取echarts实例
const { setOptions, getInstance } = useECharts(chartRef, { theme });
const selectedColumn = ref<number | null>(null);
const disabledReasons = ref<string[]>([]); // 存储每列禁用原因

// 计算每列是否适合饼图
const computeDisabledReasons = () => {
  if (!header_table?.length || !data_table) return;

  disabledReasons.value = header_table.map((_, index) => {
    const columnData = data_table
      .map(row => row[index])
      .filter(value => value !== null && value !== undefined && value !== "");

    if (columnData.length === 0) return "该列无有效数据";

    const uniqueCount = new Set(columnData).size;
    const totalCount = columnData.length;

    return uniqueCount > 5 && uniqueCount / totalCount > 0.05
      ? "该列数据不适合饼图展示"
      : null;
  });
};

// 数据变化时重新计算禁用状态
watch([data_table, header_table], computeDisabledReasons, { immediate: true });

// 处理列选择变化
const handleColumnChange = (columnIndex: number) => {
  const columnData = data_table
    .map(row => row[columnIndex])
    .filter(value => value !== null && value !== undefined && value !== "");

  const uniqueValues = new Set(columnData);
  updatePieChart(columnData, uniqueValues);
};

// 更新饼图
const updatePieChart = (data: any[], uniqueValues: Set<any>) => {
  const seriesData = Array.from(uniqueValues).map(value => ({
    value: data.filter(d => d === value).length,
    name: value.toString()
  }));

  setOptions({
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b} : {c} ({d}%)",
      backgroundColor:
        theme.value === "dark" ? "rgba(0,0,0,0.7)" : "rgba(255,255,255,0.9)"
    },
    legend: {
      top: "5%",
      left: "center",
      textStyle: {
        color: theme.value === "dark" ? "#fff" : "#333"
      },
      itemHeight: 14,
      itemGap: 20
    },
    series: [
      {
        name: "数据分布",
        type: "pie",
        radius: ["30%", "65%"], // 调整半径比例
        center: ["50%", "55%"], // 调整中心位置
        itemStyle: {
          borderRadius: 6,
          borderColor: theme.value === "dark" ? "#1a1a1a" : "#fff",
          borderWidth: 3,
          shadowBlur: 10,
          shadowColor:
            theme.value === "dark" ? "rgba(255,255,255,0.2)" : "rgba(0,0,0,0.1)"
        },
        label: {
          color: theme.value === "dark" ? "#fff" : "#333",
          fontSize: 14,
          formatter: "{b}\n{d}%",
          lineHeight: 20
        },
        emphasis: {
          scale: true,
          scaleSize: 8,
          label: {
            show: true,
            fontSize: 16
          }
        },
        data: seriesData
      }
    ]
  });
};

// 暴露给父组件的方法-用于按钮点击时的下载
function exportChartImage(type: "png" | "svg" = "png") {
  const chart = getInstance();
  exportChartInstance(chart, type);
}
defineExpose({ exportChartImage });

// 初始化默认选中第一个可用列
onMounted(() => {
  computeDisabledReasons();
  const availableIndex = disabledReasons.value.findIndex(r => !r);
  if (availableIndex !== -1) selectedColumn.value = availableIndex;
});

// 监视选择列的变化
watch(selectedColumn, newVal => {
  if (newVal !== null) handleColumnChange(newVal);
});
</script>
