<template>
  <div class="workspace-container">
    <!-- Workspace header -->
    <div class="workspace-header">
      <div class="breadcrumb-container">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            {{ workspaceStore.singleFileMode ? '文件' : '工作区' }}
          </el-breadcrumb-item>
          <el-breadcrumb-item v-if="workspacePath && !workspaceStore.singleFileMode">
            {{ getWorkspaceName(workspacePath) }}
          </el-breadcrumb-item>
          <el-breadcrumb-item v-if="currentFilePath">
            {{ getFileName(currentFilePath) }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      
      <div class="workspace-actions">
        <el-button
          v-if="!workspaceStore.singleFileMode"
          link
          :icon="FolderOpened"
          @click="openWorkspaceDirectory"
          title="打开工作区目录"
        >
          打开目录
        </el-button>
        <el-button
          v-if="!workspaceStore.singleFileMode"
          link
          :icon="DocumentAdd"
          @click="createNewFile"
          :disabled="!workspacePath"
          title="新建文件"
        >
          新建文件
        </el-button>
        <el-button
          v-if="workspaceStore.singleFileMode"
          link
          :icon="FolderOpened"
          @click="openWorkspaceDirectory"
          title="打开包含此文件的目录"
        >
          打开目录
        </el-button>
      </div>
    </div>

    <!-- Main content area -->
    <div class="workspace-content">
      <div v-if="!workspacePath && !workspaceStore.singleFileMode" class="empty-workspace">
        <div class="empty-content">
          <el-icon :size="64" color="#909399">
            <Folder />
          </el-icon>
          <h3>欢迎使用工作区</h3>
          <p>选择一个目录开始工作</p>
          <el-button type="primary" @click="openWorkspaceDirectory">
            选择工作区目录
          </el-button>
        </div>
      </div>

      <div v-else-if="currentFilePath" class="file-editor">
        <!-- File editor placeholder -->
        <div class="editor-header">
          <div class="file-info">
            <span v-if="isFileModified" class="modified-indicator">●</span>
            <el-icon :size="16">
              <Document />
            </el-icon>
            <span>{{ getFileName(currentFilePath) }}</span>
            <span class="file-path">{{ currentFilePath }}</span>
          </div>
          <div class="editor-actions">
            <el-button link size="small" @click="saveFile" :disabled="!isFileModified">
              保存
            </el-button>
            <el-button link size="small" @click="closeFile">
              关闭
            </el-button>
          </div>
        </div>
        
        <div class="editor-content">
          <div v-if="fileError" class="file-error">
            <div class="error-icon">
              <el-icon :size="48" color="#f56c6c">
                <WarningFilled />
              </el-icon>
            </div>
            <h3>{{ fileError.title }}</h3>
            <p>{{ fileError.message }}</p>
            <div v-if="fileError.suggestions" class="error-suggestions">
              <p><strong>建议：</strong></p>
              <ul>
                <li v-for="suggestion in fileError.suggestions" :key="suggestion">
                  {{ suggestion }}
                </li>
              </ul>
            </div>
          </div>
          <!-- 图像显示 -->
          <div v-else-if="currentFileInfo?.category === 'image' && imageData" class="image-viewer">
            <div class="image-container">
              <img :src="imageData" :alt="currentFilePath" class="preview-image" />
            </div>
            <div class="image-info">
              <p><strong>文件名：</strong>{{ getFileName(currentFilePath) }}</p>
              <p><strong>文件类型：</strong>{{ currentFileInfo.type.toUpperCase() }}</p>
              <p><strong>文件路径：</strong>{{ currentFilePath }}</p>
            </div>
          </div>
          <!-- 文本编辑器 -->
          <el-input
            v-else
            v-model="fileContent"
            type="textarea"
            :rows="20"
            :placeholder="getPlaceholderText()"
            :readonly="isReadOnly"
            @input="handleFileContentChange"
          />
        </div>
      </div>

      <div v-else-if="!workspaceStore.singleFileMode" class="workspace-welcome">
        <div class="welcome-content">
          <el-icon :size="48" color="#409EFF">
            <FolderOpened />
          </el-icon>
          <h3>工作区已就绪</h3>
          <p>从左侧文件树中选择文件开始</p>
          <p class="workspace-path">{{ workspacePath }}</p>
        </div>
      </div>

      <div v-else class="single-file-welcome">
        <div class="welcome-content">
          <el-icon :size="48" color="#409EFF">
            <Document />
          </el-icon>
          <h3>文件已加载</h3>
          <p>单文件模式 - 点击左侧文件开始</p>
          <p class="file-path">{{ currentFilePath }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import {
  Folder,
  FolderOpened,
  Document,
  DocumentAdd,
  WarningFilled
} from "@element-plus/icons-vue";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";

const route = useRoute();
const router = useRouter();
const workspaceStore = useWorkspaceStoreHook();

const fileContent = ref<string>("");
const originalFileContent = ref<string>("");

// File error state for unsupported files
const fileError = ref<{
  title: string;
  message: string;
  suggestions?: string[];
} | null>(null);

// File type info
const currentFileInfo = ref<{
  type: string;
  category: string;
  supported: boolean;
} | null>(null);

// Image data for image files
const imageData = ref<string | null>(null);

// Use computed properties from store
const workspacePath = computed(() => workspaceStore.getCurrentWorkspacePath);
const currentFilePath = computed(() => workspaceStore.getCurrentFilePath);
const isFileModified = computed(() =>
  currentFilePath.value ? workspaceStore.isFileModified(currentFilePath.value) : false
);

// Computed properties for UI
const isReadOnly = computed(() => {
  return currentFileInfo.value?.category === 'office' ||
         currentFileInfo.value?.category === 'pdf' ||
         !currentFileInfo.value?.supported;
});

const getPlaceholderText = () => {
  if (!currentFilePath.value) return "请选择一个文件...";
  if (fileError.value) return "";
  if (currentFileInfo.value?.category === 'office') {
    return "Office 文档内容（只读）";
  }
  if (currentFileInfo.value?.category === 'pdf') {
    return "PDF 文档内容（只读）";
  }
  return "文件内容...";
};

// Get file name from path
const getFileName = (filePath: string) => {
  if (!filePath) return '';
  return filePath.split(/[/\\]/).pop() || '';
};

// Get suggestions for unsupported file types
const getSuggestionsForFileType = (fileInfo: { type: string; category: string }) => {
  switch (fileInfo.category) {
    case 'image':
      return [
        "使用系统默认图片查看器打开",
        "使用专业图片编辑软件（如 Photoshop、GIMP）",
        "在浏览器中打开查看"
      ];
    case 'archive':
      return [
        "使用解压缩软件（如 WinRAR、7-Zip）解压",
        "解压后查看内部文件",
        "检查压缩包是否损坏"
      ];
    case 'media':
      return [
        "使用媒体播放器打开（如 VLC、Windows Media Player）",
        "使用专业音视频编辑软件",
        "检查文件格式是否受支持"
      ];
    case 'office':
      return [
        "使用 Microsoft Office 或 WPS Office 打开",
        "转换为纯文本格式后重新打开",
        "使用在线文档查看器"
      ];
    default:
      return [
        "检查文件扩展名是否正确",
        "尝试使用其他应用程序打开",
      ];
  }
};

// Computed properties
const getWorkspaceName = (path: string) => {
  return path.split(/[/\\]/).pop() || path;
};

// Watch for route changes
watch(() => route.params.workspacePath, (newPath) => {
  if (newPath && typeof newPath === 'string') {
    const decodedPath = decodeURIComponent(newPath);
    if (decodedPath !== workspaceStore.getCurrentWorkspacePath) {
      workspaceStore.setWorkspacePath(decodedPath);
    }
  }
}, { immediate: true });

// File operations
const openWorkspaceDirectory = async () => {
  try {
    const path = await window.ipcRenderer.invoke("dialog:openDirectory");
    if (path) {
      workspaceStore.setWorkspacePath(path);
      // Navigate to workspace route with the selected path
      router.push(`/workspace/${encodeURIComponent(path)}`);
    }
  } catch (error) {
    console.error("Error opening workspace directory:", error);
    ElMessage.error("打开目录失败");
  }
};

const createNewFile = async () => {
  if (!workspacePath.value) return;
  
  try {
    const { value: fileName } = await ElMessageBox.prompt(
      "请输入文件名",
      "新建文件",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        inputPattern: /^[^\\/:*?"<>|]+$/,
        inputErrorMessage: '文件名不能包含 \\ / : * ? " < > |'
      }
    );

    if (fileName && fileName.trim()) {
      const newFilePath = `${workspacePath.value}/${fileName.trim()}`;
      await window.ipcRenderer.invoke("fs:createFile", newFilePath);
      ElMessage.success("文件创建成功");
      
      // Open the newly created file
      openFile(newFilePath);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("Error creating file:", error);
      ElMessage.error("创建文件失败");
    }
  }
};

const openFile = async (filePath: string) => {
  // 检查是否为Excel文件，如果是则直接跳转
  if (workspaceStore.isExcelFile(filePath)) {
    // 显示文件打开加载动画
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: `正在打开文件: ${filePath.split(/[/\\]/).pop()}`,
      background: "rgba(0, 0, 0, 0.7)",
    });

    try {
      // 设置当前文件但不读取内容
      workspaceStore.setCurrentFile(filePath);
      workspaceStore.markDataAsSaved(filePath);

      // 跳转到dataImandEx页面
      await router.push('/dataManagement/imandex');
    } catch (error) {
      console.error("Error opening Excel file:", error);
      ElMessage.error("打开Excel文件失败");
    } finally {
      // 延迟关闭加载动画，确保用户能看到切换效果
      setTimeout(() => {
        loadingInstance.close();
      }, 300);
    }
    return;
  }

  // 对于非Excel文件，正常读取内容
  const loadingInstance = ElLoading.service({
    fullscreen: true,
    text: `正在打开文件: ${filePath.split(/[/\\]/).pop()}`,
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    // Use new file type detection and reading
    const result = await window.ipcRenderer.invoke("fs:readFileWithType", filePath);

    if (!result.success) {
      // Handle unsupported file types
      fileError.value = {
        title: "不支持的文件类型",
        message: result.message,
        suggestions: getSuggestionsForFileType(result.fileInfo)
      };
      currentFileInfo.value = result.fileInfo;
      fileContent.value = "";
      originalFileContent.value = "";
    } else {
      // File is supported, display content
      fileError.value = null;
      currentFileInfo.value = result.fileInfo;
      workspaceStore.setCurrentFile(filePath);
      workspaceStore.setFileContent(filePath, result.content);
      fileContent.value = result.content;
      originalFileContent.value = result.content;

      // Handle image data
      if (result.imageData) {
        imageData.value = result.imageData;
      } else {
        imageData.value = null;
      }

      // Only clear modification status if the content matches what's stored
      const storedContent = workspaceStore.getFileContent(filePath);
      if (storedContent === result.content) {
        workspaceStore.markFileAsSaved(filePath);
      }
    }
  } catch (error) {
    console.error("Error opening file:", error);
    fileError.value = {
      title: "文件读取错误",
      message: "读取文件时发生错误，请检查文件是否存在或是否有权限访问。",
      suggestions: ["检查文件路径是否正确", "确认文件未被其他程序占用", "检查文件权限设置"]
    };
    ElMessage.error("打开文件失败");
  } finally {
    // 延迟关闭加载动画，确保用户能看到切换效果
    setTimeout(() => {
      loadingInstance.close();
    }, 300);
  }
};

const saveFile = async () => {
  if (!currentFilePath.value) return;

  try {
    await window.ipcRenderer.invoke("fs:writeFile", currentFilePath.value, fileContent.value);
    workspaceStore.setFileContent(currentFilePath.value, fileContent.value);
    workspaceStore.markFileAsSaved(currentFilePath.value);
    originalFileContent.value = fileContent.value;
    ElMessage.success("文件保存成功");
  } catch (error) {
    console.error("Error saving file:", error);
    ElMessage.error("保存文件失败");
  }
};

const closeFile = () => {
  if (isFileModified.value) {
    ElMessageBox.confirm(
      "文件已修改，是否保存？",
      "确认关闭",
      {
        confirmButtonText: "保存并关闭",
        cancelButtonText: "不保存",
        distinguishCancelAndClose: true,
        type: "warning"
      }
    ).then(() => {
      saveFile().then(() => {
        currentFilePath.value = "";
        fileContent.value = "";
        isFileModified.value = false;
      });
    }).catch((action) => {
      if (action === "cancel") {
        currentFilePath.value = "";
        fileContent.value = "";
        isFileModified.value = false;
      }
    });
  } else {
    currentFilePath.value = "";
    fileContent.value = "";
    isFileModified.value = false;
  }
};

const handleFileContentChange = () => {
  if (currentFilePath.value) {
    const isModified = fileContent.value !== originalFileContent.value;
    if (isModified) {
      workspaceStore.markFileAsModified(currentFilePath.value);
    } else {
      // If content is back to original, mark as saved
      workspaceStore.markFileAsSaved(currentFilePath.value);
    }
  }
};

// Event handlers
const handleCustomFileSelection = (event: CustomEvent) => {
  console.log('Received workspace-open-file custom event:', event.detail.filePath);
  openFile(event.detail.filePath);
};

const handleIpcFileSelection = (_, filePath: string) => {
  console.log('Received workspace-file-selected IPC event:', filePath);
  openFile(filePath);
};

// Watch for changes in current file path from store
watch(currentFilePath, async (newFilePath, oldFilePath) => {
  console.log('Current file path changed:', { newFilePath, oldFilePath });
  if (newFilePath && newFilePath !== oldFilePath) {
    console.log('Opening file due to store change:', newFilePath);
    // Add a small delay to ensure all state is properly set
    await nextTick();
    openFile(newFilePath);
  }
}, { immediate: true });

// Handle single file mode setup
const handleSingleFileModeSetup = (_, filePath: string) => {
  console.log('Setting up single file mode for:', filePath);
  // Force clear any existing cache first
  workspaceStore.forceClearCache();
  // Then set single file mode
  workspaceStore.setSingleFileMode(filePath);
  // Force refresh the file tree and open file with longer delay
  setTimeout(async () => {
    await nextTick();
    openFile(filePath);
  }, 150);
};

// Listen for file selection from sidebar
onMounted(async () => {
  console.log('Workspace view mounted');
  console.log('Current file:', currentFilePath.value);
  console.log('Single file mode:', workspaceStore.singleFileMode);
  console.log('Workspace path:', workspacePath.value);

  // Listen for file selection events from the workspace file tree (IPC)
  window.ipcRenderer.on('workspace-file-selected', handleIpcFileSelection);

  // Listen for single file mode setup
  window.ipcRenderer.on('set-single-file-mode', handleSingleFileModeSetup);

  // Listen for custom DOM events (direct file selection)
  window.addEventListener('workspace-open-file', handleCustomFileSelection);

  // Wait for next tick to ensure all reactive state is properly initialized
  await nextTick();

  // If there's already a current file path from store, open it
  if (currentFilePath.value) {
    console.log('Opening existing file from store on mount:', currentFilePath.value);
    // Add a small delay to ensure the component is fully mounted
    setTimeout(() => {
      openFile(currentFilePath.value);
    }, 100);
  }
});

// Cleanup listeners
onBeforeUnmount(() => {
  window.ipcRenderer.removeAllListeners('workspace-file-selected');
  window.ipcRenderer.removeAllListeners('set-single-file-mode');
  window.removeEventListener('workspace-open-file', handleCustomFileSelection);
});

// Expose methods for external use
defineExpose({
  openFile,
  openWorkspaceDirectory
});
</script>

<style scoped>
.workspace-container {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  overflow: hidden;
}

/* 深色模式适配 */
html.dark .workspace-container {
  background: var(--el-bg-color);
}

.workspace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
}

/* 深色模式适配 */
html.dark .workspace-header {
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--pure-border-color);
}

.breadcrumb-container {
  flex: 1;
}

.workspace-actions {
  display: flex;
  gap: 8px;
}

.workspace-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.empty-workspace,
.workspace-welcome {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content,
.welcome-content {
  text-align: center;
  max-width: 400px;
}

.empty-content h3,
.welcome-content h3 {
  margin: 16px 0 8px 0;
  color: #303133;
}

/* 深色模式适配 */
html.dark .empty-content h3,
html.dark .welcome-content h3 {
  color: var(--el-text-color-primary);
}

.empty-content p,
.welcome-content p {
  margin: 8px 0;
  color: #606266;
}

/* 深色模式适配 */
html.dark .empty-content p,
html.dark .welcome-content p {
  color: var(--el-text-color-regular);
}

.workspace-path {
  font-family: monospace;
  font-size: 12px;
  color: #909399;
  word-break: break-all;
}

/* 深色模式适配 */
html.dark .workspace-path {
  color: var(--el-text-color-regular);
}

.file-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f5f7fa;
}

/* 深色模式适配 */
html.dark .editor-header {
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--pure-border-color);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

/* 深色模式适配 */
html.dark .file-info {
  color: var(--el-text-color-primary);
}

.modified-indicator {
  color: #f56c6c;
  font-size: 12px;
  font-weight: bold;
}

.file-path {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

/* 深色模式适配 */
html.dark .file-path {
  color: var(--el-text-color-regular);
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.editor-content {
  flex: 1;
  padding: 16px;
}

.editor-content .el-textarea {
  height: 100%;
}

:deep(.el-textarea__inner) {
  height: 100% !important;
  resize: none;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* File error display styles */
.file-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
  color: #606266;
}

.error-icon {
  margin-bottom: 1rem;
}

.file-error h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  color: #f56c6c;
}

.file-error p {
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
  max-width: 500px;
}

.error-suggestions {
  text-align: left;
  max-width: 600px;
}

.error-suggestions p {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.error-suggestions ul {
  margin: 0;
  padding-left: 1.5rem;
}

.error-suggestions li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

/* Image viewer styles */
.image-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1rem;
  background-color: #f8f9fa;
}

.image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 1rem;
  overflow: hidden;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.image-info {
  background-color: #fff;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.image-info p {
  margin: 0.5rem 0;
  font-size: 14px;
  color: #606266;
}

.image-info strong {
  color: #303133;
}
</style>
