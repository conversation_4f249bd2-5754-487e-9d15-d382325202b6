// 模型结果组件导出文件
export { default as ModelInfoCard } from './ModelInfoCard.vue';
export { default as MetricsTable } from './MetricsTable.vue';
export { default as ModelCharts } from './ModelCharts.vue';
export { default as PredictionTable } from './PredictionTable.vue';
export { default as ModelResultContainer } from './ModelResultContainer.vue';

// 重新导出，为了向后兼容
export * from './ModelInfoCard.vue';
export * from './MetricsTable.vue';
export * from './ModelCharts.vue';
export * from './PredictionTable.vue';
export * from './ModelResultContainer.vue';

// 类型定义
export interface CustomField {
  key: string;
  label: string;
  formatter?: (value: any) => string;
}

export interface CustomTab {
  name: string;
  label: string;
  component: any;
  props?: Record<string, any>;
}

export interface DatasetConfig {
  order: readonly string[];
  titles: Record<string, string>;
}

// 默认数据集配置
export const defaultDatasetConfig: DatasetConfig = {
  order: ["train", "test", "cv", "validation"] as const,
  titles: {
    train: "训练集",
    test: "测试集",
    cv: "交叉验证",
    validation: "验证集"
  }
};

// 线性模型数据集配置
export const linearModelDatasetConfig: DatasetConfig = {
  order: ["train", "test", "cv"] as const,
  titles: {
    train: "训练集",
    test: "测试集",
    cv: "交叉验证"
  }
};

// 机器学习模型数据集配置
export const mlModelDatasetConfig: DatasetConfig = {
  order: ["train", "test", "validation"] as const,
  titles: {
    train: "训练集",
    test: "测试集",
    validation: "验证集"
  }
};
