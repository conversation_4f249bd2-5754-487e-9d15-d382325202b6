export default {
  path: "/fileManagement",
  // redirect: "/error/403",
  meta: {
    icon: "ri:information-line",
    // showLink: false,
    title: "文件管理",
    rank: 9
  },
  children: [
    {
      path: "/fileManagement/showFile",
      name: "showFile",
      component: () => import("@/views/fileManagement/index.vue"),
      meta: {
        title: "文件查看"
      }
    },
    {
      path: "/fileManagement/test",
      name: "test",
      component: () => import("@/views/data/modification/dataModify/index.vue"),
      meta: {
        title: "测试测试"
      }
    },
    // {
    //   path: "/error/500",
    //   name: "500",
    //   component: () => import("@/views/error/500.vue"),
    //   meta: {
    //     title: "500"
    //   }
    // }
  ]
} satisfies RouteConfigsTable;
