<template>
  <BaseModelDialog
    :model-value="dialogStore.dialogs.mlModel"
    title="构建机器学习模型"
    dialog-class="ml-model-dialog"
    :model-type="modelType"
    :model-display-name="getModelTypeLabel(modelType)"
    :is-building="isBuilding"
    @close="handleClose"
    @confirm="handleConfirm"
    @dialog-opened="handleDialogOpened"
  >
    <template #variable-selection="{ headers, modelType: type, onUpdateSelection }">
      <MLModelSelection
        style="width: 100%"
        :headers="headers"
        :model-type="type"
        @update:selection="onUpdateSelection"
      />
    </template>

    <template #params-setting="{ algorithmName, onUpdateParams }">
      <MLModelParamsSetting
        ref="paramsSettingRef"
        style="width: 100%"
        :algorithm-name="algorithmName"
        @update:params="onUpdateParams"
      />
    </template>
  </BaseModelDialog>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from "vue";
import { useDialogStore } from "@/store/modules/dialog";
import MLModelSelection from "./mlModelSelection.vue";
import MLModelParamsSetting from "./mlModelParamsSetting.vue";
import BaseModelDialog from "../common/BaseModelDialog.vue";
import { ElMessage } from "element-plus";
import type { ModelConfig } from "@/types/models";
import { getModelDisplayName } from "@/utils/dynamicModelLoader";

const emit = defineEmits<{
  (
    e: "confirm",
    config: ModelConfig,
  ): void;
}>();

const props = defineProps<{
  modelType: string;
}>();

const isBuilding = ref(false);

// 获取模型类型显示标签
const getModelTypeLabel = (type: string) => {
  return getModelDisplayName(type);
};

const dialogStore = useDialogStore();

// 子组件引用
const paramsSettingRef = ref();

// 监听dialog关闭，清空参数设置
watch(
  () => dialogStore.dialogs.mlModel,
  async (newValue, oldValue) => {
    if (!newValue && oldValue) {
      // dialog从打开状态变为关闭状态，清空参数设置
      await nextTick();
      if (paramsSettingRef.value?.resetParams) {
        paramsSettingRef.value.resetParams();
      }
    }
  }
);

// 处理dialog打开事件，重新初始化参数
const handleDialogOpened = async () => {
  console.log("ML model dialog opened, force reloading config");
  await nextTick();
  if (paramsSettingRef.value?.forceReloadConfig) {
    paramsSettingRef.value.forceReloadConfig();
  }
};

// 处理关闭
const handleClose = () => {
  dialogStore.hideDialog("mlModel");
};

// 处理确认
const handleConfirm = async (modelConfig: ModelConfig) => {
  // 设置构建状态
  isBuilding.value = true;

  try {
    // 显示异步处理提示
    ElMessage.info({
      message: "模型构建任务已提交，由于处理时间较长，请耐心等待...",
      duration: 3000,
    });

    // 发送给父组件
    emit("confirm", modelConfig);

    handleClose();
  } catch (error) {
    ElMessage.error("模型构建失败");
  } finally {
    isBuilding.value = false;
  }
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.model-intro-form-item {
  margin-bottom: 20px;
}

:deep(.el-form-item__content) {
  flex-wrap: nowrap;
}

:deep(.el-tabs__nav-scroll) {
  width: 50%;
  margin: 0 auto;
}

:deep(.model-intro-form-item .el-form-item__content) {
  width: 100%;
}
</style>