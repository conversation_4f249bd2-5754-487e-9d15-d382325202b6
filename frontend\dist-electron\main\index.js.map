{"version": 3, "file": "index.js", "sources": ["../../node_modules/.store/requires-port@1.0.0/node_modules/requires-port/index.js", "../../node_modules/.store/querystringify@2.2.0/node_modules/querystringify/index.js", "../../node_modules/.store/url-parse@1.5.10/node_modules/url-parse/index.js", "../../node_modules/.store/buffer-more-ints@1.0.0/node_modules/buffer-more-ints/buffer-more-ints.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/codec.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/defs.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/frame.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/mux.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/heartbeat.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/format.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/bitset.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/error.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/connection.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/credentials.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/connect.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/channel.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/api_args.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/lib/channel_model.js", "../../node_modules/.store/amqplib@0.10.8/node_modules/amqplib/channel_api.js", "../../electron/utils/modelConfigLoader.ts", "../../electron/services/rabbitmq.ts", "../../electron/main/index.ts"], "sourcesContent": ["'use strict';\n\n/**\n * Check if we're required to add a port number.\n *\n * @see https://url.spec.whatwg.org/#default-port\n * @param {Number|String} port Port number we need to check\n * @param {String} protocol Protocol we need to check against.\n * @returns {Boolean} Is it a default port for the given protocol\n * @api private\n */\nmodule.exports = function required(port, protocol) {\n  protocol = protocol.split(':')[0];\n  port = +port;\n\n  if (!port) return false;\n\n  switch (protocol) {\n    case 'http':\n    case 'ws':\n    return port !== 80;\n\n    case 'https':\n    case 'wss':\n    return port !== 443;\n\n    case 'ftp':\n    return port !== 21;\n\n    case 'gopher':\n    return port !== 70;\n\n    case 'file':\n    return false;\n  }\n\n  return port !== 0;\n};\n", "'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , undef;\n\n/**\n * Decode a URI encoded string.\n *\n * @param {String} input The URI encoded string.\n * @returns {String|Null} The decoded string.\n * @api private\n */\nfunction decode(input) {\n  try {\n    return decodeURIComponent(input.replace(/\\+/g, ' '));\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\n * Attempts to encode a given input.\n *\n * @param {String} input The string that needs to be encoded.\n * @returns {String|Null} The encoded string.\n * @api private\n */\nfunction encode(input) {\n  try {\n    return encodeURIComponent(input);\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\n * Simple query string parser.\n *\n * @param {String} query The query string that needs to be parsed.\n * @returns {Object}\n * @api public\n */\nfunction querystring(query) {\n  var parser = /([^=?#&]+)=?([^&]*)/g\n    , result = {}\n    , part;\n\n  while (part = parser.exec(query)) {\n    var key = decode(part[1])\n      , value = decode(part[2]);\n\n    //\n    // Prevent overriding of existing properties. This ensures that build-in\n    // methods like `toString` or __proto__ are not overriden by malicious\n    // querystrings.\n    //\n    // In the case if failed decoding, we want to omit the key/value pairs\n    // from the result.\n    //\n    if (key === null || value === null || key in result) continue;\n    result[key] = value;\n  }\n\n  return result;\n}\n\n/**\n * Transform a query string to an object.\n *\n * @param {Object} obj Object that should be transformed.\n * @param {String} prefix Optional prefix.\n * @returns {String}\n * @api public\n */\nfunction querystringify(obj, prefix) {\n  prefix = prefix || '';\n\n  var pairs = []\n    , value\n    , key;\n\n  //\n  // Optionally prefix with a '?' if needed\n  //\n  if ('string' !== typeof prefix) prefix = '?';\n\n  for (key in obj) {\n    if (has.call(obj, key)) {\n      value = obj[key];\n\n      //\n      // Edge cases where we actually want to encode the value to an empty\n      // string instead of the stringified value.\n      //\n      if (!value && (value === null || value === undef || isNaN(value))) {\n        value = '';\n      }\n\n      key = encode(key);\n      value = encode(value);\n\n      //\n      // If we failed to encode the strings, we should bail out as we don't\n      // want to add invalid strings to the query.\n      //\n      if (key === null || value === null) continue;\n      pairs.push(key +'='+ value);\n    }\n  }\n\n  return pairs.length ? prefix + pairs.join('&') : '';\n}\n\n//\n// Expose the module.\n//\nexports.stringify = querystringify;\nexports.parse = querystring;\n", "'use strict';\n\nvar required = require('requires-port')\n  , qs = require('querystringify')\n  , controlOrWhitespace = /^[\\x00-\\x20\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff]+/\n  , CRHTLF = /[\\n\\r\\t]/g\n  , slashes = /^[A-Za-z][A-Za-z0-9+-.]*:\\/\\//\n  , port = /:\\d+$/\n  , protocolre = /^([a-z][a-z0-9.+-]*:)?(\\/\\/)?([\\\\/]+)?([\\S\\s]*)/i\n  , windowsDriveLetter = /^[a-zA-Z]:/;\n\n/**\n * Remove control characters and whitespace from the beginning of a string.\n *\n * @param {Object|String} str String to trim.\n * @returns {String} A new string representing `str` stripped of control\n *     characters and whitespace from its beginning.\n * @public\n */\nfunction trimLeft(str) {\n  return (str ? str : '').toString().replace(controlOrWhitespace, '');\n}\n\n/**\n * These are the parse rules for the URL parser, it informs the parser\n * about:\n *\n * 0. The char it Needs to parse, if it's a string it should be done using\n *    indexOf, RegExp using exec and NaN means set as current value.\n * 1. The property we should set when parsing this value.\n * 2. Indication if it's backwards or forward parsing, when set as number it's\n *    the value of extra chars that should be split off.\n * 3. Inherit from location if non existing in the parser.\n * 4. `toLowerCase` the resulting value.\n */\nvar rules = [\n  ['#', 'hash'],                        // Extract from the back.\n  ['?', 'query'],                       // Extract from the back.\n  function sanitize(address, url) {     // Sanitize what is left of the address\n    return isSpecial(url.protocol) ? address.replace(/\\\\/g, '/') : address;\n  },\n  ['/', 'pathname'],                    // Extract from the back.\n  ['@', 'auth', 1],                     // Extract from the front.\n  [NaN, 'host', undefined, 1, 1],       // Set left over value.\n  [/:(\\d*)$/, 'port', undefined, 1],    // RegExp the back.\n  [NaN, 'hostname', undefined, 1, 1]    // Set left over.\n];\n\n/**\n * These properties should not be copied or inherited from. This is only needed\n * for all non blob URL's as a blob URL does not include a hash, only the\n * origin.\n *\n * @type {Object}\n * @private\n */\nvar ignore = { hash: 1, query: 1 };\n\n/**\n * The location object differs when your code is loaded through a normal page,\n * Worker or through a worker using a blob. And with the blobble begins the\n * trouble as the location object will contain the URL of the blob, not the\n * location of the page where our code is loaded in. The actual origin is\n * encoded in the `pathname` so we can thankfully generate a good \"default\"\n * location from it so we can generate proper relative URL's again.\n *\n * @param {Object|String} loc Optional default location object.\n * @returns {Object} lolcation object.\n * @public\n */\nfunction lolcation(loc) {\n  var globalVar;\n\n  if (typeof window !== 'undefined') globalVar = window;\n  else if (typeof global !== 'undefined') globalVar = global;\n  else if (typeof self !== 'undefined') globalVar = self;\n  else globalVar = {};\n\n  var location = globalVar.location || {};\n  loc = loc || location;\n\n  var finaldestination = {}\n    , type = typeof loc\n    , key;\n\n  if ('blob:' === loc.protocol) {\n    finaldestination = new Url(unescape(loc.pathname), {});\n  } else if ('string' === type) {\n    finaldestination = new Url(loc, {});\n    for (key in ignore) delete finaldestination[key];\n  } else if ('object' === type) {\n    for (key in loc) {\n      if (key in ignore) continue;\n      finaldestination[key] = loc[key];\n    }\n\n    if (finaldestination.slashes === undefined) {\n      finaldestination.slashes = slashes.test(loc.href);\n    }\n  }\n\n  return finaldestination;\n}\n\n/**\n * Check whether a protocol scheme is special.\n *\n * @param {String} The protocol scheme of the URL\n * @return {Boolean} `true` if the protocol scheme is special, else `false`\n * @private\n */\nfunction isSpecial(scheme) {\n  return (\n    scheme === 'file:' ||\n    scheme === 'ftp:' ||\n    scheme === 'http:' ||\n    scheme === 'https:' ||\n    scheme === 'ws:' ||\n    scheme === 'wss:'\n  );\n}\n\n/**\n * @typedef ProtocolExtract\n * @type Object\n * @property {String} protocol Protocol matched in the URL, in lowercase.\n * @property {Boolean} slashes `true` if protocol is followed by \"//\", else `false`.\n * @property {String} rest Rest of the URL that is not part of the protocol.\n */\n\n/**\n * Extract protocol information from a URL with/without double slash (\"//\").\n *\n * @param {String} address URL we want to extract from.\n * @param {Object} location\n * @return {ProtocolExtract} Extracted information.\n * @private\n */\nfunction extractProtocol(address, location) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n  location = location || {};\n\n  var match = protocolre.exec(address);\n  var protocol = match[1] ? match[1].toLowerCase() : '';\n  var forwardSlashes = !!match[2];\n  var otherSlashes = !!match[3];\n  var slashesCount = 0;\n  var rest;\n\n  if (forwardSlashes) {\n    if (otherSlashes) {\n      rest = match[2] + match[3] + match[4];\n      slashesCount = match[2].length + match[3].length;\n    } else {\n      rest = match[2] + match[4];\n      slashesCount = match[2].length;\n    }\n  } else {\n    if (otherSlashes) {\n      rest = match[3] + match[4];\n      slashesCount = match[3].length;\n    } else {\n      rest = match[4]\n    }\n  }\n\n  if (protocol === 'file:') {\n    if (slashesCount >= 2) {\n      rest = rest.slice(2);\n    }\n  } else if (isSpecial(protocol)) {\n    rest = match[4];\n  } else if (protocol) {\n    if (forwardSlashes) {\n      rest = rest.slice(2);\n    }\n  } else if (slashesCount >= 2 && isSpecial(location.protocol)) {\n    rest = match[4];\n  }\n\n  return {\n    protocol: protocol,\n    slashes: forwardSlashes || isSpecial(protocol),\n    slashesCount: slashesCount,\n    rest: rest\n  };\n}\n\n/**\n * Resolve a relative URL pathname against a base URL pathname.\n *\n * @param {String} relative Pathname of the relative URL.\n * @param {String} base Pathname of the base URL.\n * @return {String} Resolved pathname.\n * @private\n */\nfunction resolve(relative, base) {\n  if (relative === '') return base;\n\n  var path = (base || '/').split('/').slice(0, -1).concat(relative.split('/'))\n    , i = path.length\n    , last = path[i - 1]\n    , unshift = false\n    , up = 0;\n\n  while (i--) {\n    if (path[i] === '.') {\n      path.splice(i, 1);\n    } else if (path[i] === '..') {\n      path.splice(i, 1);\n      up++;\n    } else if (up) {\n      if (i === 0) unshift = true;\n      path.splice(i, 1);\n      up--;\n    }\n  }\n\n  if (unshift) path.unshift('');\n  if (last === '.' || last === '..') path.push('');\n\n  return path.join('/');\n}\n\n/**\n * The actual URL instance. Instead of returning an object we've opted-in to\n * create an actual constructor as it's much more memory efficient and\n * faster and it pleases my OCD.\n *\n * It is worth noting that we should not use `URL` as class name to prevent\n * clashes with the global URL instance that got introduced in browsers.\n *\n * @constructor\n * @param {String} address URL we want to parse.\n * @param {Object|String} [location] Location defaults for relative paths.\n * @param {Boolean|Function} [parser] Parser for the query string.\n * @private\n */\nfunction Url(address, location, parser) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n\n  if (!(this instanceof Url)) {\n    return new Url(address, location, parser);\n  }\n\n  var relative, extracted, parse, instruction, index, key\n    , instructions = rules.slice()\n    , type = typeof location\n    , url = this\n    , i = 0;\n\n  //\n  // The following if statements allows this module two have compatibility with\n  // 2 different API:\n  //\n  // 1. Node.js's `url.parse` api which accepts a URL, boolean as arguments\n  //    where the boolean indicates that the query string should also be parsed.\n  //\n  // 2. The `URL` interface of the browser which accepts a URL, object as\n  //    arguments. The supplied object will be used as default values / fall-back\n  //    for relative paths.\n  //\n  if ('object' !== type && 'string' !== type) {\n    parser = location;\n    location = null;\n  }\n\n  if (parser && 'function' !== typeof parser) parser = qs.parse;\n\n  location = lolcation(location);\n\n  //\n  // Extract protocol information before running the instructions.\n  //\n  extracted = extractProtocol(address || '', location);\n  relative = !extracted.protocol && !extracted.slashes;\n  url.slashes = extracted.slashes || relative && location.slashes;\n  url.protocol = extracted.protocol || location.protocol || '';\n  address = extracted.rest;\n\n  //\n  // When the authority component is absent the URL starts with a path\n  // component.\n  //\n  if (\n    extracted.protocol === 'file:' && (\n      extracted.slashesCount !== 2 || windowsDriveLetter.test(address)) ||\n    (!extracted.slashes &&\n      (extracted.protocol ||\n        extracted.slashesCount < 2 ||\n        !isSpecial(url.protocol)))\n  ) {\n    instructions[3] = [/(.*)/, 'pathname'];\n  }\n\n  for (; i < instructions.length; i++) {\n    instruction = instructions[i];\n\n    if (typeof instruction === 'function') {\n      address = instruction(address, url);\n      continue;\n    }\n\n    parse = instruction[0];\n    key = instruction[1];\n\n    if (parse !== parse) {\n      url[key] = address;\n    } else if ('string' === typeof parse) {\n      index = parse === '@'\n        ? address.lastIndexOf(parse)\n        : address.indexOf(parse);\n\n      if (~index) {\n        if ('number' === typeof instruction[2]) {\n          url[key] = address.slice(0, index);\n          address = address.slice(index + instruction[2]);\n        } else {\n          url[key] = address.slice(index);\n          address = address.slice(0, index);\n        }\n      }\n    } else if ((index = parse.exec(address))) {\n      url[key] = index[1];\n      address = address.slice(0, index.index);\n    }\n\n    url[key] = url[key] || (\n      relative && instruction[3] ? location[key] || '' : ''\n    );\n\n    //\n    // Hostname, host and protocol should be lowercased so they can be used to\n    // create a proper `origin`.\n    //\n    if (instruction[4]) url[key] = url[key].toLowerCase();\n  }\n\n  //\n  // Also parse the supplied query string in to an object. If we're supplied\n  // with a custom parser as function use that instead of the default build-in\n  // parser.\n  //\n  if (parser) url.query = parser(url.query);\n\n  //\n  // If the URL is relative, resolve the pathname against the base URL.\n  //\n  if (\n      relative\n    && location.slashes\n    && url.pathname.charAt(0) !== '/'\n    && (url.pathname !== '' || location.pathname !== '')\n  ) {\n    url.pathname = resolve(url.pathname, location.pathname);\n  }\n\n  //\n  // Default to a / for pathname if none exists. This normalizes the URL\n  // to always have a /\n  //\n  if (url.pathname.charAt(0) !== '/' && isSpecial(url.protocol)) {\n    url.pathname = '/' + url.pathname;\n  }\n\n  //\n  // We should not add port numbers if they are already the default port number\n  // for a given protocol. As the host also contains the port number we're going\n  // override it with the hostname which contains no port number.\n  //\n  if (!required(url.port, url.protocol)) {\n    url.host = url.hostname;\n    url.port = '';\n  }\n\n  //\n  // Parse down the `auth` for the username and password.\n  //\n  url.username = url.password = '';\n\n  if (url.auth) {\n    index = url.auth.indexOf(':');\n\n    if (~index) {\n      url.username = url.auth.slice(0, index);\n      url.username = encodeURIComponent(decodeURIComponent(url.username));\n\n      url.password = url.auth.slice(index + 1);\n      url.password = encodeURIComponent(decodeURIComponent(url.password))\n    } else {\n      url.username = encodeURIComponent(decodeURIComponent(url.auth));\n    }\n\n    url.auth = url.password ? url.username +':'+ url.password : url.username;\n  }\n\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host\n    ? url.protocol +'//'+ url.host\n    : 'null';\n\n  //\n  // The href is just the compiled result.\n  //\n  url.href = url.toString();\n}\n\n/**\n * This is convenience method for changing properties in the URL instance to\n * insure that they all propagate correctly.\n *\n * @param {String} part          Property we need to adjust.\n * @param {Mixed} value          The newly assigned value.\n * @param {Boolean|Function} fn  When setting the query, it will be the function\n *                               used to parse the query.\n *                               When setting the protocol, double slash will be\n *                               removed from the final url if it is true.\n * @returns {URL} URL instance for chaining.\n * @public\n */\nfunction set(part, value, fn) {\n  var url = this;\n\n  switch (part) {\n    case 'query':\n      if ('string' === typeof value && value.length) {\n        value = (fn || qs.parse)(value);\n      }\n\n      url[part] = value;\n      break;\n\n    case 'port':\n      url[part] = value;\n\n      if (!required(value, url.protocol)) {\n        url.host = url.hostname;\n        url[part] = '';\n      } else if (value) {\n        url.host = url.hostname +':'+ value;\n      }\n\n      break;\n\n    case 'hostname':\n      url[part] = value;\n\n      if (url.port) value += ':'+ url.port;\n      url.host = value;\n      break;\n\n    case 'host':\n      url[part] = value;\n\n      if (port.test(value)) {\n        value = value.split(':');\n        url.port = value.pop();\n        url.hostname = value.join(':');\n      } else {\n        url.hostname = value;\n        url.port = '';\n      }\n\n      break;\n\n    case 'protocol':\n      url.protocol = value.toLowerCase();\n      url.slashes = !fn;\n      break;\n\n    case 'pathname':\n    case 'hash':\n      if (value) {\n        var char = part === 'pathname' ? '/' : '#';\n        url[part] = value.charAt(0) !== char ? char + value : value;\n      } else {\n        url[part] = value;\n      }\n      break;\n\n    case 'username':\n    case 'password':\n      url[part] = encodeURIComponent(value);\n      break;\n\n    case 'auth':\n      var index = value.indexOf(':');\n\n      if (~index) {\n        url.username = value.slice(0, index);\n        url.username = encodeURIComponent(decodeURIComponent(url.username));\n\n        url.password = value.slice(index + 1);\n        url.password = encodeURIComponent(decodeURIComponent(url.password));\n      } else {\n        url.username = encodeURIComponent(decodeURIComponent(value));\n      }\n  }\n\n  for (var i = 0; i < rules.length; i++) {\n    var ins = rules[i];\n\n    if (ins[4]) url[ins[1]] = url[ins[1]].toLowerCase();\n  }\n\n  url.auth = url.password ? url.username +':'+ url.password : url.username;\n\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host\n    ? url.protocol +'//'+ url.host\n    : 'null';\n\n  url.href = url.toString();\n\n  return url;\n}\n\n/**\n * Transform the properties back in to a valid and full URL string.\n *\n * @param {Function} stringify Optional query stringify function.\n * @returns {String} Compiled version of the URL.\n * @public\n */\nfunction toString(stringify) {\n  if (!stringify || 'function' !== typeof stringify) stringify = qs.stringify;\n\n  var query\n    , url = this\n    , host = url.host\n    , protocol = url.protocol;\n\n  if (protocol && protocol.charAt(protocol.length - 1) !== ':') protocol += ':';\n\n  var result =\n    protocol +\n    ((url.protocol && url.slashes) || isSpecial(url.protocol) ? '//' : '');\n\n  if (url.username) {\n    result += url.username;\n    if (url.password) result += ':'+ url.password;\n    result += '@';\n  } else if (url.password) {\n    result += ':'+ url.password;\n    result += '@';\n  } else if (\n    url.protocol !== 'file:' &&\n    isSpecial(url.protocol) &&\n    !host &&\n    url.pathname !== '/'\n  ) {\n    //\n    // Add back the empty userinfo, otherwise the original invalid URL\n    // might be transformed into a valid one with `url.pathname` as host.\n    //\n    result += '@';\n  }\n\n  //\n  // Trailing colon is removed from `url.host` when it is parsed. If it still\n  // ends with a colon, then add back the trailing colon that was removed. This\n  // prevents an invalid URL from being transformed into a valid one.\n  //\n  if (host[host.length - 1] === ':' || (port.test(url.hostname) && !url.port)) {\n    host += ':';\n  }\n\n  result += host + url.pathname;\n\n  query = 'object' === typeof url.query ? stringify(url.query) : url.query;\n  if (query) result += '?' !== query.charAt(0) ? '?'+ query : query;\n\n  if (url.hash) result += url.hash;\n\n  return result;\n}\n\nUrl.prototype = { set: set, toString: toString };\n\n//\n// Expose the URL parser and some additional properties that might be useful for\n// others or testing.\n//\nUrl.extractProtocol = extractProtocol;\nUrl.location = lolcation;\nUrl.trimLeft = trimLeft;\nUrl.qs = qs;\n\nmodule.exports = Url;\n", "'use strict';\n\n// JavaScript is numerically challenged\nvar SHIFT_LEFT_32 = (1 << 16) * (1 << 16);\nvar SHIFT_RIGHT_32 = 1 / SHIFT_LEFT_32;\n\n// The maximum contiguous integer that can be held in a IEEE754 double\nvar MAX_INT = 0x1fffffffffffff;\n\nfunction isContiguousInt(val) {\n    return val <= MAX_INT && val >= -MAX_INT;\n}\n\nfunction assertContiguousInt(val) {\n    if (!isContiguousInt(val)) {\n        throw new TypeError(\"number cannot be represented as a contiguous integer\");\n    }\n}\n\nmodule.exports.isContiguousInt = isContiguousInt;\nmodule.exports.assertContiguousInt = assertContiguousInt;\n\n// Fill in the regular procedures\n['UInt', 'Int'].forEach(function (sign) {\n  var suffix = sign + '8';\n  module.exports['read' + suffix] =\n    Buffer.prototype['read' + suffix].call;\n  module.exports['write' + suffix] =\n    Buffer.prototype['write' + suffix].call;\n\n  ['16', '32'].forEach(function (size) {\n    ['LE', 'BE'].forEach(function (endian) {\n      var suffix = sign + size + endian;\n      var read = Buffer.prototype['read' + suffix];\n      module.exports['read' + suffix] =\n        function (buf, offset) {\n          return read.call(buf, offset);\n        };\n      var write = Buffer.prototype['write' + suffix];\n      module.exports['write' + suffix] =\n        function (buf, val, offset) {\n          return write.call(buf, val, offset);\n        };\n    });\n  });\n});\n\n// Check that a value is an integer within the given range\nfunction check_value(val, min, max) {\n    val = +val;\n    if (typeof(val) != 'number' || val < min || val > max || Math.floor(val) !== val) {\n        throw new TypeError(\"\\\"value\\\" argument is out of bounds\");\n    }\n    return val;\n}\n\n// Check that something is within the Buffer bounds\nfunction check_bounds(buf, offset, len) {\n    if (offset < 0 || offset + len > buf.length) {\n        throw new RangeError(\"Index out of range\");\n    }\n}\n\nfunction readUInt24BE(buf, offset) {\n  return buf.readUInt8(offset) << 16 | buf.readUInt16BE(offset + 1);\n}\nmodule.exports.readUInt24BE = readUInt24BE;\n\nfunction writeUInt24BE(buf, val, offset) {\n    val = check_value(val, 0, 0xffffff);\n    check_bounds(buf, offset, 3);\n    buf.writeUInt8(val >>> 16, offset);\n    buf.writeUInt16BE(val & 0xffff, offset + 1);\n}\nmodule.exports.writeUInt24BE = writeUInt24BE;\n\nfunction readUInt40BE(buf, offset) {\n    return (buf.readUInt8(offset) || 0) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 1);\n}\nmodule.exports.readUInt40BE = readUInt40BE;\n\nfunction writeUInt40BE(buf, val, offset) {\n    val = check_value(val, 0, 0xffffffffff);\n    check_bounds(buf, offset, 5);\n    buf.writeUInt8(Math.floor(val * SHIFT_RIGHT_32), offset);\n    buf.writeInt32BE(val & -1, offset + 1);\n}\nmodule.exports.writeUInt40BE = writeUInt40BE;\n\nfunction readUInt48BE(buf, offset) {\n    return buf.readUInt16BE(offset) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 2);\n}\nmodule.exports.readUInt48BE = readUInt48BE;\n\nfunction writeUInt48BE(buf, val, offset) {\n    val = check_value(val, 0, 0xffffffffffff);\n    check_bounds(buf, offset, 6);\n    buf.writeUInt16BE(Math.floor(val * SHIFT_RIGHT_32), offset);\n    buf.writeInt32BE(val & -1, offset + 2);\n}\nmodule.exports.writeUInt48BE = writeUInt48BE;\n\nfunction readUInt56BE(buf, offset) {\n    return ((buf.readUInt8(offset) || 0) << 16 | buf.readUInt16BE(offset + 1)) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 3);\n}\nmodule.exports.readUInt56BE = readUInt56BE;\n\nfunction writeUInt56BE(buf, val, offset) {\n    val = check_value(val, 0, 0xffffffffffffff);\n    check_bounds(buf, offset, 7);\n\n    if (val < 0x100000000000000) {\n        var hi = Math.floor(val * SHIFT_RIGHT_32);\n        buf.writeUInt8(hi >>> 16, offset);\n        buf.writeUInt16BE(hi & 0xffff, offset + 1);\n        buf.writeInt32BE(val & -1, offset + 3);\n    } else {\n        // Special case because 2^56-1 gets rounded up to 2^56\n        buf[offset] = 0xff;\n        buf[offset+1] = 0xff;\n        buf[offset+2] = 0xff;\n        buf[offset+3] = 0xff;\n        buf[offset+4] = 0xff;\n        buf[offset+5] = 0xff;\n        buf[offset+6] = 0xff;\n    }\n}\nmodule.exports.writeUInt56BE = writeUInt56BE;\n\nfunction readUInt64BE(buf, offset) {\n    return buf.readUInt32BE(offset) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 4);\n}\nmodule.exports.readUInt64BE = readUInt64BE;\n\nfunction writeUInt64BE(buf, val, offset) {\n    val = check_value(val, 0, 0xffffffffffffffff);\n    check_bounds(buf, offset, 8);\n\n    if (val < 0x10000000000000000) {\n        buf.writeUInt32BE(Math.floor(val * SHIFT_RIGHT_32), offset);\n        buf.writeInt32BE(val & -1, offset + 4);\n    } else {\n        // Special case because 2^64-1 gets rounded up to 2^64\n        buf[offset] = 0xff;\n        buf[offset+1] = 0xff;\n        buf[offset+2] = 0xff;\n        buf[offset+3] = 0xff;\n        buf[offset+4] = 0xff;\n        buf[offset+5] = 0xff;\n        buf[offset+6] = 0xff;\n        buf[offset+7] = 0xff;\n    }\n}\nmodule.exports.writeUInt64BE = writeUInt64BE;\n\nfunction readUInt24LE(buf, offset) {\n    return buf.readUInt8(offset + 2) << 16 | buf.readUInt16LE(offset);\n}\nmodule.exports.readUInt24LE = readUInt24LE;\n\nfunction writeUInt24LE(buf, val, offset) {\n    val = check_value(val, 0, 0xffffff);\n    check_bounds(buf, offset, 3);\n\n    buf.writeUInt16LE(val & 0xffff, offset);\n    buf.writeUInt8(val >>> 16, offset + 2);\n}\nmodule.exports.writeUInt24LE = writeUInt24LE;\n\nfunction readUInt40LE(buf, offset) {\n    return (buf.readUInt8(offset + 4) || 0) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);\n}\nmodule.exports.readUInt40LE = readUInt40LE;\n\nfunction writeUInt40LE(buf, val, offset) {\n    val = check_value(val, 0, 0xffffffffff);\n    check_bounds(buf, offset, 5);\n    buf.writeInt32LE(val & -1, offset);\n    buf.writeUInt8(Math.floor(val * SHIFT_RIGHT_32), offset + 4);\n}\nmodule.exports.writeUInt40LE = writeUInt40LE;\n\nfunction readUInt48LE(buf, offset) {\n    return buf.readUInt16LE(offset + 4) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);\n}\nmodule.exports.readUInt48LE = readUInt48LE;\n\nfunction writeUInt48LE(buf, val, offset) {\n    val = check_value(val, 0, 0xffffffffffff);\n    check_bounds(buf, offset, 6);\n    buf.writeInt32LE(val & -1, offset);\n    buf.writeUInt16LE(Math.floor(val * SHIFT_RIGHT_32), offset + 4);\n}\nmodule.exports.writeUInt48LE = writeUInt48LE;\n\nfunction readUInt56LE(buf, offset) {\n    return ((buf.readUInt8(offset + 6) || 0) << 16 | buf.readUInt16LE(offset + 4)) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);\n}\nmodule.exports.readUInt56LE = readUInt56LE;\n\nfunction writeUInt56LE(buf, val, offset) {\n    val = check_value(val, 0, 0xffffffffffffff);\n    check_bounds(buf, offset, 7);\n\n    if (val < 0x100000000000000) {\n        buf.writeInt32LE(val & -1, offset);\n        var hi = Math.floor(val * SHIFT_RIGHT_32);\n        buf.writeUInt16LE(hi & 0xffff, offset + 4);\n        buf.writeUInt8(hi >>> 16, offset + 6);\n    } else {\n        // Special case because 2^56-1 gets rounded up to 2^56\n        buf[offset] = 0xff;\n        buf[offset+1] = 0xff;\n        buf[offset+2] = 0xff;\n        buf[offset+3] = 0xff;\n        buf[offset+4] = 0xff;\n        buf[offset+5] = 0xff;\n        buf[offset+6] = 0xff;\n    }\n}\nmodule.exports.writeUInt56LE = writeUInt56LE;\n\nfunction readUInt64LE(buf, offset) {\n    return buf.readUInt32LE(offset + 4) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);\n}\nmodule.exports.readUInt64LE = readUInt64LE;\n\nfunction writeUInt64LE(buf, val, offset) {\n    val = check_value(val, 0, 0xffffffffffffffff);\n    check_bounds(buf, offset, 8);\n\n    if (val < 0x10000000000000000) {\n        buf.writeInt32LE(val & -1, offset);\n        buf.writeUInt32LE(Math.floor(val * SHIFT_RIGHT_32), offset + 4);\n    } else {\n        // Special case because 2^64-1 gets rounded up to 2^64\n        buf[offset] = 0xff;\n        buf[offset+1] = 0xff;\n        buf[offset+2] = 0xff;\n        buf[offset+3] = 0xff;\n        buf[offset+4] = 0xff;\n        buf[offset+5] = 0xff;\n        buf[offset+6] = 0xff;\n        buf[offset+7] = 0xff;\n    }\n}\nmodule.exports.writeUInt64LE = writeUInt64LE;\n\n\nfunction readInt24BE(buf, offset) {\n    return (buf.readInt8(offset) << 16) + buf.readUInt16BE(offset + 1);\n}\nmodule.exports.readInt24BE = readInt24BE;\n\nfunction writeInt24BE(buf, val, offset) {\n    val = check_value(val, -0x800000, 0x7fffff);\n    check_bounds(buf, offset, 3);\n    buf.writeInt8(val >> 16, offset);\n    buf.writeUInt16BE(val & 0xffff, offset + 1);\n}\nmodule.exports.writeInt24BE = writeInt24BE;\n\nfunction readInt40BE(buf, offset) {\n    return (buf.readInt8(offset) || 0) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 1);\n}\nmodule.exports.readInt40BE = readInt40BE;\n\nfunction writeInt40BE(buf, val, offset) {\n    val = check_value(val, -0x8000000000, 0x7fffffffff);\n    check_bounds(buf, offset, 5);\n    buf.writeInt8(Math.floor(val * SHIFT_RIGHT_32), offset);\n    buf.writeInt32BE(val & -1, offset + 1);\n}\nmodule.exports.writeInt40BE = writeInt40BE;\n\nfunction readInt48BE(buf, offset) {\n    return buf.readInt16BE(offset) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 2);\n}\nmodule.exports.readInt48BE = readInt48BE;\n\nfunction writeInt48BE(buf, val, offset) {\n    val = check_value(val, -0x800000000000, 0x7fffffffffff);\n    check_bounds(buf, offset, 6);\n    buf.writeInt16BE(Math.floor(val * SHIFT_RIGHT_32), offset);\n    buf.writeInt32BE(val & -1, offset + 2);\n}\nmodule.exports.writeInt48BE = writeInt48BE;\n\nfunction readInt56BE(buf, offset) {\n    return (((buf.readInt8(offset) || 0) << 16) + buf.readUInt16BE(offset + 1)) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 3);\n}\nmodule.exports.readInt56BE = readInt56BE;\n\nfunction writeInt56BE(buf, val, offset) {\n    val = check_value(val, -0x800000000000000, 0x7fffffffffffff);\n    check_bounds(buf, offset, 7);\n\n    if (val < 0x80000000000000) {\n        var hi = Math.floor(val * SHIFT_RIGHT_32);\n        buf.writeInt8(hi >> 16, offset);\n        buf.writeUInt16BE(hi & 0xffff, offset + 1);\n        buf.writeInt32BE(val & -1, offset + 3);\n    } else {\n        // Special case because 2^55-1 gets rounded up to 2^55\n        buf[offset] = 0x7f;\n        buf[offset+1] = 0xff;\n        buf[offset+2] = 0xff;\n        buf[offset+3] = 0xff;\n        buf[offset+4] = 0xff;\n        buf[offset+5] = 0xff;\n        buf[offset+6] = 0xff;\n    }\n}\nmodule.exports.writeInt56BE = writeInt56BE;\n\nfunction readInt64BE(buf, offset) {\n    return buf.readInt32BE(offset) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 4);\n}\nmodule.exports.readInt64BE = readInt64BE;\n\nfunction writeInt64BE(buf, val, offset) {\n    val = check_value(val, -0x800000000000000000, 0x7fffffffffffffff);\n    check_bounds(buf, offset, 8);\n\n    if (val < 0x8000000000000000) {\n        buf.writeInt32BE(Math.floor(val * SHIFT_RIGHT_32), offset);\n        buf.writeInt32BE(val & -1, offset + 4);\n    } else {\n        // Special case because 2^63-1 gets rounded up to 2^63\n        buf[offset] = 0x7f;\n        buf[offset+1] = 0xff;\n        buf[offset+2] = 0xff;\n        buf[offset+3] = 0xff;\n        buf[offset+4] = 0xff;\n        buf[offset+5] = 0xff;\n        buf[offset+6] = 0xff;\n        buf[offset+7] = 0xff;\n    }\n}\nmodule.exports.writeInt64BE = writeInt64BE;\n\nfunction readInt24LE(buf, offset) {\n    return (buf.readInt8(offset + 2) << 16) + buf.readUInt16LE(offset);\n}\nmodule.exports.readInt24LE = readInt24LE;\n\nfunction writeInt24LE(buf, val, offset) {\n    val = check_value(val, -0x800000, 0x7fffff);\n    check_bounds(buf, offset, 3);\n    buf.writeUInt16LE(val & 0xffff, offset);\n    buf.writeInt8(val >> 16, offset + 2);\n}\nmodule.exports.writeInt24LE = writeInt24LE;\n\nfunction readInt40LE(buf, offset) {\n    return (buf.readInt8(offset + 4) || 0) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);\n}\nmodule.exports.readInt40LE = readInt40LE;\n\nfunction writeInt40LE(buf, val, offset) {\n    val = check_value(val, -0x8000000000, 0x7fffffffff);\n    check_bounds(buf, offset, 5);\n    buf.writeInt32LE(val & -1, offset);\n    buf.writeInt8(Math.floor(val * SHIFT_RIGHT_32), offset + 4);\n}\nmodule.exports.writeInt40LE = writeInt40LE;\n\nfunction readInt48LE(buf, offset) {\n    return buf.readInt16LE(offset + 4) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);\n}\nmodule.exports.readInt48LE = readInt48LE;\n\nfunction writeInt48LE(buf, val, offset) {\n    val = check_value(val, -0x800000000000, 0x7fffffffffff);\n    check_bounds(buf, offset, 6);\n    buf.writeInt32LE(val & -1, offset);\n    buf.writeInt16LE(Math.floor(val * SHIFT_RIGHT_32), offset + 4);\n}\nmodule.exports.writeInt48LE = writeInt48LE;\n\nfunction readInt56LE(buf, offset) {\n    return (((buf.readInt8(offset + 6) || 0) << 16) + buf.readUInt16LE(offset + 4)) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);\n}\nmodule.exports.readInt56LE = readInt56LE;\n\nfunction writeInt56LE(buf, val, offset) {\n    val = check_value(val, -0x80000000000000, 0x7fffffffffffff);\n    check_bounds(buf, offset, 7);\n\n    if (val < 0x80000000000000) {\n        buf.writeInt32LE(val & -1, offset);\n        var hi = Math.floor(val * SHIFT_RIGHT_32);\n        buf.writeUInt16LE(hi & 0xffff, offset + 4);\n        buf.writeInt8(hi >> 16, offset + 6);\n    } else {\n        // Special case because 2^55-1 gets rounded up to 2^55\n        buf[offset] = 0xff;\n        buf[offset+1] = 0xff;\n        buf[offset+2] = 0xff;\n        buf[offset+3] = 0xff;\n        buf[offset+4] = 0xff;\n        buf[offset+5] = 0xff;\n        buf[offset+6] = 0x7f;\n    }\n}\nmodule.exports.writeInt56LE = writeInt56LE;\n\nfunction readInt64LE(buf, offset) {\n    return buf.readInt32LE(offset + 4) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);\n}\nmodule.exports.readInt64LE = readInt64LE;\n\nfunction writeInt64LE(buf, val, offset) {\n    val = check_value(val, -0x8000000000000000, 0x7fffffffffffffff);\n    check_bounds(buf, offset, 8);\n\n    if (val < 0x8000000000000000) {\n        buf.writeInt32LE(val & -1, offset);\n        buf.writeInt32LE(Math.floor(val * SHIFT_RIGHT_32), offset + 4);\n    } else {\n        // Special case because 2^55-1 gets rounded up to 2^55\n        buf[offset] = 0xff;\n        buf[offset+1] = 0xff;\n        buf[offset+2] = 0xff;\n        buf[offset+3] = 0xff;\n        buf[offset+4] = 0xff;\n        buf[offset+5] = 0xff;\n        buf[offset+6] = 0xff;\n        buf[offset+7] = 0x7f;\n    }\n}\nmodule.exports.writeInt64LE = writeInt64LE;\n", "//\n//\n//\n\n/*\n\nThe AMQP 0-9-1 is a mess when it comes to the types that can be\nencoded on the wire.\n\nThere are four encoding schemes, and three overlapping sets of types:\nframes, methods, (field-)tables, and properties.\n\nEach *frame type* has a set layout in which values of given types are\nconcatenated along with sections of \"raw binary\" data.\n\nIn frames there are `shortstr`s, that is length-prefixed strings of\nUTF8 chars, 8 bit unsigned integers (called `octet`), unsigned 16 bit\nintegers (called `short` or `short-uint`), unsigned 32 bit integers\n(called `long` or `long-uint`), unsigned 64 bit integers (called\n`longlong` or `longlong-uint`), and flags (called `bit`).\n\nMethods are encoded as a frame giving a method ID and a sequence of\narguments of known types. The encoded method argument values are\nconcatenated (with some fun complications around \"packing\" consecutive\nbit values into bytes).\n\nAlong with the types given in frames, method arguments may be long\nbyte strings (`longstr`, not required to be UTF8) or 64 bit unsigned\nintegers to be interpreted as timestamps (yeah I don't know why\neither), or arbitrary sets of key-value pairs (called `field-table`).\n\nInside a field table the keys are `shortstr` and the values are\nprefixed with a byte tag giving the type. The types are any of the\nabove except for bits (which are replaced by byte-wide `bool`), along\nwith a NULL value `void`, a special fixed-precision number encoding\n(`decimal`), IEEE754 `float`s and `double`s, signed integers,\n`field-array` (a sequence of tagged values), and nested field-tables.\n\nRabbitMQ and QPid use a subset of the field-table types, and different\nvalue tags, established before the AMQP 0-9-1 specification was\npublished. So far as I know, no-one uses the types and tags as\npublished. http://www.rabbitmq.com/amqp-0-9-1-errata.html gives the\nlist of field-table types.\n\nLastly, there are (sets of) properties, only one of which is given in\nAMQP 0-9-1: `BasicProperties`. These are almost the same as methods,\nexcept that they appear in content header frames, which include a\ncontent size, and they carry a set of flags indicating which\nproperties are present. This scheme can save ones of bytes per message\n(messages which take a minimum of three frames each to send).\n\n*/\n\n'use strict';\n\nvar ints = require('buffer-more-ints');\n\n// JavaScript uses only doubles so what I'm testing for is whether\n// it's *better* to encode a number as a float or double. This really\n// just amounts to testing whether there's a fractional part to the\n// number, except that see below. NB I don't use bitwise operations to\n// do this 'efficiently' -- it would mask the number to 32 bits.\n//\n// At 2^50, doubles don't have sufficient precision to distinguish\n// between floating point and integer numbers (`Math.pow(2, 50) + 0.1\n// === Math.pow(2, 50)` (and, above 2^53, doubles cannot represent all\n// integers (`Math.pow(2, 53) + 1 === Math.pow(2, 53)`)). Hence\n// anything with a magnitude at or above 2^50 may as well be encoded\n// as a 64-bit integer. Except that only signed integers are supported\n// by RabbitMQ, so anything above 2^63 - 1 must be a double.\nfunction isFloatingPoint(n) {\n    return n >= 0x8000000000000000 ||\n        (Math.abs(n) < 0x4000000000000\n         && Math.floor(n) !== n);\n}\n\nfunction encodeTable(buffer, val, offset) {\n    var start = offset;\n    offset += 4; // leave room for the table length\n    for (var key in val) {\n        if (val[key] !== undefined) {\n          var len = Buffer.byteLength(key);\n          buffer.writeUInt8(len, offset); offset++;\n          buffer.write(key, offset, 'utf8'); offset += len;\n          offset += encodeFieldValue(buffer, val[key], offset);\n        }\n    }\n    var size = offset - start;\n    buffer.writeUInt32BE(size - 4, start);\n    return size;\n}\n\nfunction encodeArray(buffer, val, offset) {\n    var start = offset;\n    offset += 4;\n    for (var i=0, num=val.length; i < num; i++) {\n        offset += encodeFieldValue(buffer, val[i], offset);\n    }\n    var size = offset - start;\n    buffer.writeUInt32BE(size - 4, start);\n    return size;\n}\n\nfunction encodeFieldValue(buffer, value, offset) {\n    var start = offset;\n    var type = typeof value, val = value;\n    // A trapdoor for specifying a type, e.g., timestamp\n    if (value && type === 'object' && value.hasOwnProperty('!')) {\n        val = value.value;\n        type = value['!'];\n    }\n\n    // If it's a JS number, we'll have to guess what type to encode it\n    // as.\n    if (type == 'number') {\n        // Making assumptions about the kind of number (floating point\n        // v integer, signed, unsigned, size) desired is dangerous in\n        // general; however, in practice RabbitMQ uses only\n        // longstrings and unsigned integers in its arguments, and\n        // other clients generally conflate number types anyway. So\n        // the only distinction we care about is floating point vs\n        // integers, preferring integers since those can be promoted\n        // if necessary. If floating point is required, we may as well\n        // use double precision.\n        if (isFloatingPoint(val)) {\n            type = 'double';\n        }\n        else { // only signed values are used in tables by\n               // RabbitMQ. It *used* to (< v3.3.0) treat the byte 'b'\n               // type as unsigned, but most clients (and the spec)\n               // think it's signed, and now RabbitMQ does too.\n            if (val < 128 && val >= -128) {\n                type = 'byte';\n            }\n            else if (val >= -0x8000 && val < 0x8000) {\n                type = 'short'\n            }\n            else if (val >= -0x80000000 && val < 0x80000000) {\n                type = 'int';\n            }\n            else {\n                type = 'long';\n            }\n        }\n    }\n\n    function tag(t) { buffer.write(t, offset); offset++; }\n\n    switch (type) {\n    case 'string': // no shortstr in field tables\n        var len = Buffer.byteLength(val, 'utf8');\n        tag('S');\n        buffer.writeUInt32BE(len, offset); offset += 4;\n        buffer.write(val, offset, 'utf8'); offset += len;\n        break;\n    case 'object':\n        if (val === null) {\n            tag('V');\n        }\n        else if (Array.isArray(val)) {\n            tag('A');\n            offset += encodeArray(buffer, val, offset);\n        }\n        else if (Buffer.isBuffer(val)) {\n            tag('x');\n            buffer.writeUInt32BE(val.length, offset); offset += 4;\n            val.copy(buffer, offset); offset += val.length;\n        }\n        else {\n            tag('F');\n            offset += encodeTable(buffer, val, offset);\n        }\n        break;\n    case 'boolean':\n        tag('t');\n        buffer.writeUInt8((val) ? 1 : 0, offset); offset++;\n        break;\n    // These are the types that are either guessed above, or\n    // explicitly given using the {'!': type} notation.\n    case 'double':\n    case 'float64':\n        tag('d');\n        buffer.writeDoubleBE(val, offset);\n        offset += 8;\n        break;\n    case 'byte':\n    case 'int8':\n        tag('b');\n        buffer.writeInt8(val, offset); offset++;\n        break;\n    case 'unsignedbyte':\n    case 'uint8':\n        tag('B');\n        buffer.writeUInt8(val, offset); offset++;\n        break;\n    case 'short':\n    case 'int16':\n        tag('s');\n        buffer.writeInt16BE(val, offset); offset += 2;\n        break;\n    case 'unsignedshort':\n    case 'uint16':\n        tag('u');\n        buffer.writeUInt16BE(val, offset); offset += 2;\n        break;\n    case 'int':\n    case 'int32':\n        tag('I');\n        buffer.writeInt32BE(val, offset); offset += 4;\n        break;\n    case 'unsignedint':\n    case 'uint32':\n        tag('i');\n        buffer.writeUInt32BE(val, offset); offset += 4;\n        break;\n    case 'long':\n    case 'int64':\n        tag('l');\n        ints.writeInt64BE(buffer, val, offset); offset += 8;\n        break;\n\n    // Now for exotic types, those can _only_ be denoted by using\n    // `{'!': type, value: val}\n    case 'timestamp':\n        tag('T');\n        ints.writeUInt64BE(buffer, val, offset); offset += 8;\n        break;\n    case 'float':\n        tag('f');\n        buffer.writeFloatBE(val, offset); offset += 4;\n        break;\n    case 'decimal':\n        tag('D');\n        if (val.hasOwnProperty('places') && val.hasOwnProperty('digits')\n            && val.places >= 0 && val.places < 256) {\n            buffer[offset] = val.places; offset++;\n            buffer.writeUInt32BE(val.digits, offset); offset += 4;\n        }\n        else throw new TypeError(\n            \"Decimal value must be {'places': 0..255, 'digits': uint32}, \" +\n                \"got \" + JSON.stringify(val));\n        break;\n    default:\n        throw new TypeError('Unknown type to encode: ' + type);\n    }\n    return offset - start;\n}\n\n// Assume we're given a slice of the buffer that contains just the\n// fields.\nfunction decodeFields(slice) {\n    var fields = {}, offset = 0, size = slice.length;\n    var len, key, val;\n\n    function decodeFieldValue() {\n        var tag = String.fromCharCode(slice[offset]); offset++;\n        switch (tag) {\n        case 'b':\n            val = slice.readInt8(offset); offset++;\n            break;\n        case 'B':\n            val = slice.readUInt8(offset); offset++;\n            break;\n        case 'S':\n            len = slice.readUInt32BE(offset); offset += 4;\n            val = slice.toString('utf8', offset, offset + len);\n            offset += len;\n            break;\n        case 'I':\n            val = slice.readInt32BE(offset); offset += 4;\n            break;\n        case 'i':\n            val = slice.readUInt32BE(offset); offset += 4;\n            break;\n        case 'D': // only positive decimals, apparently.\n            var places = slice[offset]; offset++;\n            var digits = slice.readUInt32BE(offset); offset += 4;\n            val = {'!': 'decimal', value: {places: places, digits: digits}};\n            break;\n        case 'T':\n            val = ints.readUInt64BE(slice, offset); offset += 8;\n            val = {'!': 'timestamp', value: val};\n            break;\n        case 'F':\n            len = slice.readUInt32BE(offset); offset += 4;\n            val = decodeFields(slice.subarray(offset, offset + len));\n            offset += len;\n            break;\n        case 'A':\n            len = slice.readUInt32BE(offset); offset += 4;\n            decodeArray(offset + len);\n            // NB decodeArray will itself update offset and val\n            break;\n        case 'd':\n            val = slice.readDoubleBE(offset); offset += 8;\n            break;\n        case 'f':\n            val = slice.readFloatBE(offset); offset += 4;\n            break;\n        case 'l':\n            val = ints.readInt64BE(slice, offset); offset += 8;\n            break;\n        case 's':\n            val = slice.readInt16BE(offset); offset += 2;\n            break;\n        case 'u':\n            val = slice.readUInt16BE(offset); offset += 2;\n            break;\n        case 't':\n            val = slice[offset] != 0; offset++;\n            break;\n        case 'V':\n            val = null;\n            break;\n        case 'x':\n            len = slice.readUInt32BE(offset); offset += 4;\n            val = slice.subarray(offset, offset + len);\n            offset += len;\n            break;\n        default:\n            throw new TypeError('Unexpected type tag \"' + tag +'\"');\n        }\n    }\n\n    function decodeArray(until) {\n        var vals = [];\n        while (offset < until) {\n            decodeFieldValue();\n            vals.push(val);\n        }\n        val = vals;\n    }\n\n    while (offset < size) {\n        len = slice.readUInt8(offset); offset++;\n        key = slice.toString('utf8', offset, offset + len);\n        offset += len;\n        decodeFieldValue();\n        fields[key] = val;\n    }\n    return fields;\n}\n\nmodule.exports.encodeTable = encodeTable;\nmodule.exports.decodeFields = decodeFields;\n", "/** @preserve This file is generated by the script\n * ../bin/generate-defs.js, which is not in general included in a\n * distribution, but is available in the source repository e.g. at\n * https://github.com/squaremo/amqp.node/\n */\n\"use strict\";\n\nfunction decodeBasicQos(buffer) {\n  var val, offset = 0, fields = {\n    prefetchSize: void 0,\n    prefetchCount: void 0,\n    global: void 0\n  };\n  val = buffer.readUInt32BE(offset);\n  offset += 4;\n  fields.prefetchSize = val;\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.prefetchCount = val;\n  val = !!(1 & buffer[offset]);\n  fields.global = val;\n  return fields;\n}\n\nfunction encodeBasicQos(channel, fields) {\n  var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(19);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932170, 7);\n  offset = 11;\n  val = fields.prefetchSize;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'prefetchSize' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt32BE(val, offset);\n  offset += 4;\n  val = fields.prefetchCount;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'prefetchCount' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.global;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicQosOk(buffer) {\n  return {};\n}\n\nfunction encodeBasicQosOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932171, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicConsume(buffer) {\n  var val, len, offset = 0, fields = {\n    ticket: void 0,\n    queue: void 0,\n    consumerTag: void 0,\n    noLocal: void 0,\n    noAck: void 0,\n    exclusive: void 0,\n    nowait: void 0,\n    arguments: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.ticket = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.queue = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.consumerTag = val;\n  val = !!(1 & buffer[offset]);\n  fields.noLocal = val;\n  val = !!(2 & buffer[offset]);\n  fields.noAck = val;\n  val = !!(4 & buffer[offset]);\n  fields.exclusive = val;\n  val = !!(8 & buffer[offset]);\n  fields.nowait = val;\n  offset++;\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = decodeFields(buffer.subarray(offset, offset + len));\n  offset += len;\n  fields.arguments = val;\n  return fields;\n}\n\nfunction encodeBasicConsume(channel, fields) {\n  var len, offset = 0, val = null, bits = 0, varyingSize = 0, scratchOffset = 0;\n  val = fields.queue;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'queue' is the wrong type; must be a string (up to 255 chars)\");\n  var queue_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += queue_len;\n  val = fields.consumerTag;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'consumerTag' is the wrong type; must be a string (up to 255 chars)\");\n  var consumerTag_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += consumerTag_len;\n  val = fields.arguments;\n  if (void 0 === val) val = {}; else if (\"object\" != typeof val) throw new TypeError(\"Field 'arguments' is the wrong type; must be an object\");\n  len = encodeTable(SCRATCH, val, scratchOffset);\n  var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);\n  scratchOffset += len;\n  varyingSize += arguments_encoded.length;\n  var buffer = Buffer.alloc(17 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932180, 7);\n  offset = 11;\n  val = fields.ticket;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'ticket' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.queue;\n  void 0 === val && (val = \"\");\n  buffer[offset] = queue_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += queue_len;\n  val = fields.consumerTag;\n  void 0 === val && (val = \"\");\n  buffer[offset] = consumerTag_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += consumerTag_len;\n  val = fields.noLocal;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  val = fields.noAck;\n  void 0 === val && (val = !1);\n  val && (bits += 2);\n  val = fields.exclusive;\n  void 0 === val && (val = !1);\n  val && (bits += 4);\n  val = fields.nowait;\n  void 0 === val && (val = !1);\n  val && (bits += 8);\n  buffer[offset] = bits;\n  offset++;\n  bits = 0;\n  offset += arguments_encoded.copy(buffer, offset);\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicConsumeOk(buffer) {\n  var val, len, offset = 0, fields = {\n    consumerTag: void 0\n  };\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.consumerTag = val;\n  return fields;\n}\n\nfunction encodeBasicConsumeOk(channel, fields) {\n  var offset = 0, val = null, varyingSize = 0;\n  val = fields.consumerTag;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'consumerTag'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'consumerTag' is the wrong type; must be a string (up to 255 chars)\");\n  var consumerTag_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += consumerTag_len;\n  var buffer = Buffer.alloc(13 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932181, 7);\n  offset = 11;\n  val = fields.consumerTag;\n  void 0 === val && (val = void 0);\n  buffer[offset] = consumerTag_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += consumerTag_len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicCancel(buffer) {\n  var val, len, offset = 0, fields = {\n    consumerTag: void 0,\n    nowait: void 0\n  };\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.consumerTag = val;\n  val = !!(1 & buffer[offset]);\n  fields.nowait = val;\n  return fields;\n}\n\nfunction encodeBasicCancel(channel, fields) {\n  var offset = 0, val = null, bits = 0, varyingSize = 0;\n  val = fields.consumerTag;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'consumerTag'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'consumerTag' is the wrong type; must be a string (up to 255 chars)\");\n  var consumerTag_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += consumerTag_len;\n  var buffer = Buffer.alloc(14 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932190, 7);\n  offset = 11;\n  val = fields.consumerTag;\n  void 0 === val && (val = void 0);\n  buffer[offset] = consumerTag_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += consumerTag_len;\n  val = fields.nowait;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicCancelOk(buffer) {\n  var val, len, offset = 0, fields = {\n    consumerTag: void 0\n  };\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.consumerTag = val;\n  return fields;\n}\n\nfunction encodeBasicCancelOk(channel, fields) {\n  var offset = 0, val = null, varyingSize = 0;\n  val = fields.consumerTag;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'consumerTag'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'consumerTag' is the wrong type; must be a string (up to 255 chars)\");\n  var consumerTag_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += consumerTag_len;\n  var buffer = Buffer.alloc(13 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932191, 7);\n  offset = 11;\n  val = fields.consumerTag;\n  void 0 === val && (val = void 0);\n  buffer[offset] = consumerTag_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += consumerTag_len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicPublish(buffer) {\n  var val, len, offset = 0, fields = {\n    ticket: void 0,\n    exchange: void 0,\n    routingKey: void 0,\n    mandatory: void 0,\n    immediate: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.ticket = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.exchange = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.routingKey = val;\n  val = !!(1 & buffer[offset]);\n  fields.mandatory = val;\n  val = !!(2 & buffer[offset]);\n  fields.immediate = val;\n  return fields;\n}\n\nfunction encodeBasicPublish(channel, fields) {\n  var offset = 0, val = null, bits = 0, varyingSize = 0;\n  val = fields.exchange;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'exchange' is the wrong type; must be a string (up to 255 chars)\");\n  var exchange_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += exchange_len;\n  val = fields.routingKey;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'routingKey' is the wrong type; must be a string (up to 255 chars)\");\n  var routingKey_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += routingKey_len;\n  var buffer = Buffer.alloc(17 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932200, 7);\n  offset = 11;\n  val = fields.ticket;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'ticket' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.exchange;\n  void 0 === val && (val = \"\");\n  buffer[offset] = exchange_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += exchange_len;\n  val = fields.routingKey;\n  void 0 === val && (val = \"\");\n  buffer[offset] = routingKey_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += routingKey_len;\n  val = fields.mandatory;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  val = fields.immediate;\n  void 0 === val && (val = !1);\n  val && (bits += 2);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicReturn(buffer) {\n  var val, len, offset = 0, fields = {\n    replyCode: void 0,\n    replyText: void 0,\n    exchange: void 0,\n    routingKey: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.replyCode = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.replyText = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.exchange = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.routingKey = val;\n  return fields;\n}\n\nfunction encodeBasicReturn(channel, fields) {\n  var offset = 0, val = null, varyingSize = 0;\n  val = fields.replyText;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'replyText' is the wrong type; must be a string (up to 255 chars)\");\n  var replyText_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += replyText_len;\n  val = fields.exchange;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'exchange'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'exchange' is the wrong type; must be a string (up to 255 chars)\");\n  var exchange_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += exchange_len;\n  val = fields.routingKey;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'routingKey'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'routingKey' is the wrong type; must be a string (up to 255 chars)\");\n  var routingKey_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += routingKey_len;\n  var buffer = Buffer.alloc(17 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932210, 7);\n  offset = 11;\n  val = fields.replyCode;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'replyCode'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'replyCode' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.replyText;\n  void 0 === val && (val = \"\");\n  buffer[offset] = replyText_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += replyText_len;\n  val = fields.exchange;\n  void 0 === val && (val = void 0);\n  buffer[offset] = exchange_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += exchange_len;\n  val = fields.routingKey;\n  void 0 === val && (val = void 0);\n  buffer[offset] = routingKey_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += routingKey_len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicDeliver(buffer) {\n  var val, len, offset = 0, fields = {\n    consumerTag: void 0,\n    deliveryTag: void 0,\n    redelivered: void 0,\n    exchange: void 0,\n    routingKey: void 0\n  };\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.consumerTag = val;\n  val = ints.readUInt64BE(buffer, offset);\n  offset += 8;\n  fields.deliveryTag = val;\n  val = !!(1 & buffer[offset]);\n  fields.redelivered = val;\n  offset++;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.exchange = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.routingKey = val;\n  return fields;\n}\n\nfunction encodeBasicDeliver(channel, fields) {\n  var offset = 0, val = null, bits = 0, varyingSize = 0;\n  val = fields.consumerTag;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'consumerTag'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'consumerTag' is the wrong type; must be a string (up to 255 chars)\");\n  var consumerTag_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += consumerTag_len;\n  val = fields.exchange;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'exchange'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'exchange' is the wrong type; must be a string (up to 255 chars)\");\n  var exchange_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += exchange_len;\n  val = fields.routingKey;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'routingKey'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'routingKey' is the wrong type; must be a string (up to 255 chars)\");\n  var routingKey_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += routingKey_len;\n  var buffer = Buffer.alloc(24 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932220, 7);\n  offset = 11;\n  val = fields.consumerTag;\n  void 0 === val && (val = void 0);\n  buffer[offset] = consumerTag_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += consumerTag_len;\n  val = fields.deliveryTag;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'deliveryTag'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'deliveryTag' is the wrong type; must be a number (but not NaN)\");\n  ints.writeUInt64BE(buffer, val, offset);\n  offset += 8;\n  val = fields.redelivered;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  bits = 0;\n  val = fields.exchange;\n  void 0 === val && (val = void 0);\n  buffer[offset] = exchange_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += exchange_len;\n  val = fields.routingKey;\n  void 0 === val && (val = void 0);\n  buffer[offset] = routingKey_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += routingKey_len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicGet(buffer) {\n  var val, len, offset = 0, fields = {\n    ticket: void 0,\n    queue: void 0,\n    noAck: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.ticket = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.queue = val;\n  val = !!(1 & buffer[offset]);\n  fields.noAck = val;\n  return fields;\n}\n\nfunction encodeBasicGet(channel, fields) {\n  var offset = 0, val = null, bits = 0, varyingSize = 0;\n  val = fields.queue;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'queue' is the wrong type; must be a string (up to 255 chars)\");\n  var queue_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += queue_len;\n  var buffer = Buffer.alloc(16 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932230, 7);\n  offset = 11;\n  val = fields.ticket;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'ticket' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.queue;\n  void 0 === val && (val = \"\");\n  buffer[offset] = queue_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += queue_len;\n  val = fields.noAck;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicGetOk(buffer) {\n  var val, len, offset = 0, fields = {\n    deliveryTag: void 0,\n    redelivered: void 0,\n    exchange: void 0,\n    routingKey: void 0,\n    messageCount: void 0\n  };\n  val = ints.readUInt64BE(buffer, offset);\n  offset += 8;\n  fields.deliveryTag = val;\n  val = !!(1 & buffer[offset]);\n  fields.redelivered = val;\n  offset++;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.exchange = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.routingKey = val;\n  val = buffer.readUInt32BE(offset);\n  offset += 4;\n  fields.messageCount = val;\n  return fields;\n}\n\nfunction encodeBasicGetOk(channel, fields) {\n  var offset = 0, val = null, bits = 0, varyingSize = 0;\n  val = fields.exchange;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'exchange'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'exchange' is the wrong type; must be a string (up to 255 chars)\");\n  var exchange_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += exchange_len;\n  val = fields.routingKey;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'routingKey'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'routingKey' is the wrong type; must be a string (up to 255 chars)\");\n  var routingKey_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += routingKey_len;\n  var buffer = Buffer.alloc(27 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932231, 7);\n  offset = 11;\n  val = fields.deliveryTag;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'deliveryTag'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'deliveryTag' is the wrong type; must be a number (but not NaN)\");\n  ints.writeUInt64BE(buffer, val, offset);\n  offset += 8;\n  val = fields.redelivered;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  bits = 0;\n  val = fields.exchange;\n  void 0 === val && (val = void 0);\n  buffer[offset] = exchange_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += exchange_len;\n  val = fields.routingKey;\n  void 0 === val && (val = void 0);\n  buffer[offset] = routingKey_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += routingKey_len;\n  val = fields.messageCount;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'messageCount'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'messageCount' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt32BE(val, offset);\n  offset += 4;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicGetEmpty(buffer) {\n  var val, len, offset = 0, fields = {\n    clusterId: void 0\n  };\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.clusterId = val;\n  return fields;\n}\n\nfunction encodeBasicGetEmpty(channel, fields) {\n  var offset = 0, val = null, varyingSize = 0;\n  val = fields.clusterId;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'clusterId' is the wrong type; must be a string (up to 255 chars)\");\n  var clusterId_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += clusterId_len;\n  var buffer = Buffer.alloc(13 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932232, 7);\n  offset = 11;\n  val = fields.clusterId;\n  void 0 === val && (val = \"\");\n  buffer[offset] = clusterId_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += clusterId_len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicAck(buffer) {\n  var val, offset = 0, fields = {\n    deliveryTag: void 0,\n    multiple: void 0\n  };\n  val = ints.readUInt64BE(buffer, offset);\n  offset += 8;\n  fields.deliveryTag = val;\n  val = !!(1 & buffer[offset]);\n  fields.multiple = val;\n  return fields;\n}\n\nfunction encodeBasicAck(channel, fields) {\n  var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(21);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932240, 7);\n  offset = 11;\n  val = fields.deliveryTag;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'deliveryTag' is the wrong type; must be a number (but not NaN)\");\n  ints.writeUInt64BE(buffer, val, offset);\n  offset += 8;\n  val = fields.multiple;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicReject(buffer) {\n  var val, offset = 0, fields = {\n    deliveryTag: void 0,\n    requeue: void 0\n  };\n  val = ints.readUInt64BE(buffer, offset);\n  offset += 8;\n  fields.deliveryTag = val;\n  val = !!(1 & buffer[offset]);\n  fields.requeue = val;\n  return fields;\n}\n\nfunction encodeBasicReject(channel, fields) {\n  var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(21);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932250, 7);\n  offset = 11;\n  val = fields.deliveryTag;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'deliveryTag'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'deliveryTag' is the wrong type; must be a number (but not NaN)\");\n  ints.writeUInt64BE(buffer, val, offset);\n  offset += 8;\n  val = fields.requeue;\n  void 0 === val && (val = !0);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicRecoverAsync(buffer) {\n  var val, fields = {\n    requeue: void 0\n  };\n  val = !!(1 & buffer[0]);\n  fields.requeue = val;\n  return fields;\n}\n\nfunction encodeBasicRecoverAsync(channel, fields) {\n  var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(13);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932260, 7);\n  offset = 11;\n  val = fields.requeue;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicRecover(buffer) {\n  var val, fields = {\n    requeue: void 0\n  };\n  val = !!(1 & buffer[0]);\n  fields.requeue = val;\n  return fields;\n}\n\nfunction encodeBasicRecover(channel, fields) {\n  var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(13);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932270, 7);\n  offset = 11;\n  val = fields.requeue;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicRecoverOk(buffer) {\n  return {};\n}\n\nfunction encodeBasicRecoverOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932271, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeBasicNack(buffer) {\n  var val, offset = 0, fields = {\n    deliveryTag: void 0,\n    multiple: void 0,\n    requeue: void 0\n  };\n  val = ints.readUInt64BE(buffer, offset);\n  offset += 8;\n  fields.deliveryTag = val;\n  val = !!(1 & buffer[offset]);\n  fields.multiple = val;\n  val = !!(2 & buffer[offset]);\n  fields.requeue = val;\n  return fields;\n}\n\nfunction encodeBasicNack(channel, fields) {\n  var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(21);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932280, 7);\n  offset = 11;\n  val = fields.deliveryTag;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'deliveryTag' is the wrong type; must be a number (but not NaN)\");\n  ints.writeUInt64BE(buffer, val, offset);\n  offset += 8;\n  val = fields.multiple;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  val = fields.requeue;\n  void 0 === val && (val = !0);\n  val && (bits += 2);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionStart(buffer) {\n  var val, len, offset = 0, fields = {\n    versionMajor: void 0,\n    versionMinor: void 0,\n    serverProperties: void 0,\n    mechanisms: void 0,\n    locales: void 0\n  };\n  val = buffer[offset];\n  offset++;\n  fields.versionMajor = val;\n  val = buffer[offset];\n  offset++;\n  fields.versionMinor = val;\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = decodeFields(buffer.subarray(offset, offset + len));\n  offset += len;\n  fields.serverProperties = val;\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = buffer.subarray(offset, offset + len);\n  offset += len;\n  fields.mechanisms = val;\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = buffer.subarray(offset, offset + len);\n  offset += len;\n  fields.locales = val;\n  return fields;\n}\n\nfunction encodeConnectionStart(channel, fields) {\n  var len, offset = 0, val = null, varyingSize = 0, scratchOffset = 0;\n  val = fields.serverProperties;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'serverProperties'\");\n  if (\"object\" != typeof val) throw new TypeError(\"Field 'serverProperties' is the wrong type; must be an object\");\n  len = encodeTable(SCRATCH, val, scratchOffset);\n  var serverProperties_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);\n  scratchOffset += len;\n  varyingSize += serverProperties_encoded.length;\n  val = fields.mechanisms;\n  if (void 0 === val) val = Buffer.from(\"PLAIN\"); else if (!Buffer.isBuffer(val)) throw new TypeError(\"Field 'mechanisms' is the wrong type; must be a Buffer\");\n  varyingSize += val.length;\n  val = fields.locales;\n  if (void 0 === val) val = Buffer.from(\"en_US\"); else if (!Buffer.isBuffer(val)) throw new TypeError(\"Field 'locales' is the wrong type; must be a Buffer\");\n  varyingSize += val.length;\n  var buffer = Buffer.alloc(22 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655370, 7);\n  offset = 11;\n  val = fields.versionMajor;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'versionMajor' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt8(val, offset);\n  offset++;\n  val = fields.versionMinor;\n  if (void 0 === val) val = 9; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'versionMinor' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt8(val, offset);\n  offset++;\n  offset += serverProperties_encoded.copy(buffer, offset);\n  val = fields.mechanisms;\n  void 0 === val && (val = Buffer.from(\"PLAIN\"));\n  len = val.length;\n  buffer.writeUInt32BE(len, offset);\n  offset += 4;\n  val.copy(buffer, offset);\n  offset += len;\n  val = fields.locales;\n  void 0 === val && (val = Buffer.from(\"en_US\"));\n  len = val.length;\n  buffer.writeUInt32BE(len, offset);\n  offset += 4;\n  val.copy(buffer, offset);\n  offset += len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionStartOk(buffer) {\n  var val, len, offset = 0, fields = {\n    clientProperties: void 0,\n    mechanism: void 0,\n    response: void 0,\n    locale: void 0\n  };\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = decodeFields(buffer.subarray(offset, offset + len));\n  offset += len;\n  fields.clientProperties = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.mechanism = val;\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = buffer.subarray(offset, offset + len);\n  offset += len;\n  fields.response = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.locale = val;\n  return fields;\n}\n\nfunction encodeConnectionStartOk(channel, fields) {\n  var len, offset = 0, val = null, varyingSize = 0, scratchOffset = 0;\n  val = fields.clientProperties;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'clientProperties'\");\n  if (\"object\" != typeof val) throw new TypeError(\"Field 'clientProperties' is the wrong type; must be an object\");\n  len = encodeTable(SCRATCH, val, scratchOffset);\n  var clientProperties_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);\n  scratchOffset += len;\n  varyingSize += clientProperties_encoded.length;\n  val = fields.mechanism;\n  if (void 0 === val) val = \"PLAIN\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'mechanism' is the wrong type; must be a string (up to 255 chars)\");\n  var mechanism_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += mechanism_len;\n  val = fields.response;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'response'\");\n  if (!Buffer.isBuffer(val)) throw new TypeError(\"Field 'response' is the wrong type; must be a Buffer\");\n  varyingSize += val.length;\n  val = fields.locale;\n  if (void 0 === val) val = \"en_US\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'locale' is the wrong type; must be a string (up to 255 chars)\");\n  var locale_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += locale_len;\n  var buffer = Buffer.alloc(18 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655371, 7);\n  offset = 11;\n  offset += clientProperties_encoded.copy(buffer, offset);\n  val = fields.mechanism;\n  void 0 === val && (val = \"PLAIN\");\n  buffer[offset] = mechanism_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += mechanism_len;\n  val = fields.response;\n  void 0 === val && (val = Buffer.from(void 0));\n  len = val.length;\n  buffer.writeUInt32BE(len, offset);\n  offset += 4;\n  val.copy(buffer, offset);\n  offset += len;\n  val = fields.locale;\n  void 0 === val && (val = \"en_US\");\n  buffer[offset] = locale_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += locale_len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionSecure(buffer) {\n  var val, len, offset = 0, fields = {\n    challenge: void 0\n  };\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = buffer.subarray(offset, offset + len);\n  offset += len;\n  fields.challenge = val;\n  return fields;\n}\n\nfunction encodeConnectionSecure(channel, fields) {\n  var len, offset = 0, val = null, varyingSize = 0;\n  val = fields.challenge;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'challenge'\");\n  if (!Buffer.isBuffer(val)) throw new TypeError(\"Field 'challenge' is the wrong type; must be a Buffer\");\n  varyingSize += val.length;\n  var buffer = Buffer.alloc(16 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655380, 7);\n  offset = 11;\n  val = fields.challenge;\n  void 0 === val && (val = Buffer.from(void 0));\n  len = val.length;\n  buffer.writeUInt32BE(len, offset);\n  offset += 4;\n  val.copy(buffer, offset);\n  offset += len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionSecureOk(buffer) {\n  var val, len, offset = 0, fields = {\n    response: void 0\n  };\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = buffer.subarray(offset, offset + len);\n  offset += len;\n  fields.response = val;\n  return fields;\n}\n\nfunction encodeConnectionSecureOk(channel, fields) {\n  var len, offset = 0, val = null, varyingSize = 0;\n  val = fields.response;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'response'\");\n  if (!Buffer.isBuffer(val)) throw new TypeError(\"Field 'response' is the wrong type; must be a Buffer\");\n  varyingSize += val.length;\n  var buffer = Buffer.alloc(16 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655381, 7);\n  offset = 11;\n  val = fields.response;\n  void 0 === val && (val = Buffer.from(void 0));\n  len = val.length;\n  buffer.writeUInt32BE(len, offset);\n  offset += 4;\n  val.copy(buffer, offset);\n  offset += len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionTune(buffer) {\n  var val, offset = 0, fields = {\n    channelMax: void 0,\n    frameMax: void 0,\n    heartbeat: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.channelMax = val;\n  val = buffer.readUInt32BE(offset);\n  offset += 4;\n  fields.frameMax = val;\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.heartbeat = val;\n  return fields;\n}\n\nfunction encodeConnectionTune(channel, fields) {\n  var offset = 0, val = null, buffer = Buffer.alloc(20);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655390, 7);\n  offset = 11;\n  val = fields.channelMax;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'channelMax' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.frameMax;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'frameMax' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt32BE(val, offset);\n  offset += 4;\n  val = fields.heartbeat;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'heartbeat' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionTuneOk(buffer) {\n  var val, offset = 0, fields = {\n    channelMax: void 0,\n    frameMax: void 0,\n    heartbeat: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.channelMax = val;\n  val = buffer.readUInt32BE(offset);\n  offset += 4;\n  fields.frameMax = val;\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.heartbeat = val;\n  return fields;\n}\n\nfunction encodeConnectionTuneOk(channel, fields) {\n  var offset = 0, val = null, buffer = Buffer.alloc(20);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655391, 7);\n  offset = 11;\n  val = fields.channelMax;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'channelMax' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.frameMax;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'frameMax' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt32BE(val, offset);\n  offset += 4;\n  val = fields.heartbeat;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'heartbeat' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionOpen(buffer) {\n  var val, len, offset = 0, fields = {\n    virtualHost: void 0,\n    capabilities: void 0,\n    insist: void 0\n  };\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.virtualHost = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.capabilities = val;\n  val = !!(1 & buffer[offset]);\n  fields.insist = val;\n  return fields;\n}\n\nfunction encodeConnectionOpen(channel, fields) {\n  var offset = 0, val = null, bits = 0, varyingSize = 0;\n  val = fields.virtualHost;\n  if (void 0 === val) val = \"/\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'virtualHost' is the wrong type; must be a string (up to 255 chars)\");\n  var virtualHost_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += virtualHost_len;\n  val = fields.capabilities;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'capabilities' is the wrong type; must be a string (up to 255 chars)\");\n  var capabilities_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += capabilities_len;\n  var buffer = Buffer.alloc(15 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655400, 7);\n  offset = 11;\n  val = fields.virtualHost;\n  void 0 === val && (val = \"/\");\n  buffer[offset] = virtualHost_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += virtualHost_len;\n  val = fields.capabilities;\n  void 0 === val && (val = \"\");\n  buffer[offset] = capabilities_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += capabilities_len;\n  val = fields.insist;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionOpenOk(buffer) {\n  var val, len, offset = 0, fields = {\n    knownHosts: void 0\n  };\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.knownHosts = val;\n  return fields;\n}\n\nfunction encodeConnectionOpenOk(channel, fields) {\n  var offset = 0, val = null, varyingSize = 0;\n  val = fields.knownHosts;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'knownHosts' is the wrong type; must be a string (up to 255 chars)\");\n  var knownHosts_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += knownHosts_len;\n  var buffer = Buffer.alloc(13 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655401, 7);\n  offset = 11;\n  val = fields.knownHosts;\n  void 0 === val && (val = \"\");\n  buffer[offset] = knownHosts_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += knownHosts_len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionClose(buffer) {\n  var val, len, offset = 0, fields = {\n    replyCode: void 0,\n    replyText: void 0,\n    classId: void 0,\n    methodId: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.replyCode = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.replyText = val;\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.classId = val;\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.methodId = val;\n  return fields;\n}\n\nfunction encodeConnectionClose(channel, fields) {\n  var offset = 0, val = null, varyingSize = 0;\n  val = fields.replyText;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'replyText' is the wrong type; must be a string (up to 255 chars)\");\n  var replyText_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += replyText_len;\n  var buffer = Buffer.alloc(19 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655410, 7);\n  offset = 11;\n  val = fields.replyCode;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'replyCode'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'replyCode' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.replyText;\n  void 0 === val && (val = \"\");\n  buffer[offset] = replyText_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += replyText_len;\n  val = fields.classId;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'classId'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'classId' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.methodId;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'methodId'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'methodId' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionCloseOk(buffer) {\n  return {};\n}\n\nfunction encodeConnectionCloseOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655411, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionBlocked(buffer) {\n  var val, len, offset = 0, fields = {\n    reason: void 0\n  };\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.reason = val;\n  return fields;\n}\n\nfunction encodeConnectionBlocked(channel, fields) {\n  var offset = 0, val = null, varyingSize = 0;\n  val = fields.reason;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'reason' is the wrong type; must be a string (up to 255 chars)\");\n  var reason_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += reason_len;\n  var buffer = Buffer.alloc(13 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655420, 7);\n  offset = 11;\n  val = fields.reason;\n  void 0 === val && (val = \"\");\n  buffer[offset] = reason_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += reason_len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionUnblocked(buffer) {\n  return {};\n}\n\nfunction encodeConnectionUnblocked(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655421, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionUpdateSecret(buffer) {\n  var val, len, offset = 0, fields = {\n    newSecret: void 0,\n    reason: void 0\n  };\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = buffer.subarray(offset, offset + len);\n  offset += len;\n  fields.newSecret = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.reason = val;\n  return fields;\n}\n\nfunction encodeConnectionUpdateSecret(channel, fields) {\n  var len, offset = 0, val = null, varyingSize = 0;\n  val = fields.newSecret;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'newSecret'\");\n  if (!Buffer.isBuffer(val)) throw new TypeError(\"Field 'newSecret' is the wrong type; must be a Buffer\");\n  varyingSize += val.length;\n  val = fields.reason;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'reason'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'reason' is the wrong type; must be a string (up to 255 chars)\");\n  var reason_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += reason_len;\n  var buffer = Buffer.alloc(17 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655430, 7);\n  offset = 11;\n  val = fields.newSecret;\n  void 0 === val && (val = Buffer.from(void 0));\n  len = val.length;\n  buffer.writeUInt32BE(len, offset);\n  offset += 4;\n  val.copy(buffer, offset);\n  offset += len;\n  val = fields.reason;\n  void 0 === val && (val = void 0);\n  buffer[offset] = reason_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += reason_len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConnectionUpdateSecretOk(buffer) {\n  return {};\n}\n\nfunction encodeConnectionUpdateSecretOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(655431, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeChannelOpen(buffer) {\n  var val, len, offset = 0, fields = {\n    outOfBand: void 0\n  };\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.outOfBand = val;\n  return fields;\n}\n\nfunction encodeChannelOpen(channel, fields) {\n  var offset = 0, val = null, varyingSize = 0;\n  val = fields.outOfBand;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'outOfBand' is the wrong type; must be a string (up to 255 chars)\");\n  var outOfBand_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += outOfBand_len;\n  var buffer = Buffer.alloc(13 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(1310730, 7);\n  offset = 11;\n  val = fields.outOfBand;\n  void 0 === val && (val = \"\");\n  buffer[offset] = outOfBand_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += outOfBand_len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeChannelOpenOk(buffer) {\n  var val, len, offset = 0, fields = {\n    channelId: void 0\n  };\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = buffer.subarray(offset, offset + len);\n  offset += len;\n  fields.channelId = val;\n  return fields;\n}\n\nfunction encodeChannelOpenOk(channel, fields) {\n  var len, offset = 0, val = null, varyingSize = 0;\n  val = fields.channelId;\n  if (void 0 === val) val = Buffer.from(\"\"); else if (!Buffer.isBuffer(val)) throw new TypeError(\"Field 'channelId' is the wrong type; must be a Buffer\");\n  varyingSize += val.length;\n  var buffer = Buffer.alloc(16 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(1310731, 7);\n  offset = 11;\n  val = fields.channelId;\n  void 0 === val && (val = Buffer.from(\"\"));\n  len = val.length;\n  buffer.writeUInt32BE(len, offset);\n  offset += 4;\n  val.copy(buffer, offset);\n  offset += len;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeChannelFlow(buffer) {\n  var val, fields = {\n    active: void 0\n  };\n  val = !!(1 & buffer[0]);\n  fields.active = val;\n  return fields;\n}\n\nfunction encodeChannelFlow(channel, fields) {\n  var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(13);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(1310740, 7);\n  offset = 11;\n  val = fields.active;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'active'\");\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeChannelFlowOk(buffer) {\n  var val, fields = {\n    active: void 0\n  };\n  val = !!(1 & buffer[0]);\n  fields.active = val;\n  return fields;\n}\n\nfunction encodeChannelFlowOk(channel, fields) {\n  var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(13);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(1310741, 7);\n  offset = 11;\n  val = fields.active;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'active'\");\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeChannelClose(buffer) {\n  var val, len, offset = 0, fields = {\n    replyCode: void 0,\n    replyText: void 0,\n    classId: void 0,\n    methodId: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.replyCode = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.replyText = val;\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.classId = val;\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.methodId = val;\n  return fields;\n}\n\nfunction encodeChannelClose(channel, fields) {\n  var offset = 0, val = null, varyingSize = 0;\n  val = fields.replyText;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'replyText' is the wrong type; must be a string (up to 255 chars)\");\n  var replyText_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += replyText_len;\n  var buffer = Buffer.alloc(19 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(1310760, 7);\n  offset = 11;\n  val = fields.replyCode;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'replyCode'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'replyCode' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.replyText;\n  void 0 === val && (val = \"\");\n  buffer[offset] = replyText_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += replyText_len;\n  val = fields.classId;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'classId'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'classId' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.methodId;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'methodId'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'methodId' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeChannelCloseOk(buffer) {\n  return {};\n}\n\nfunction encodeChannelCloseOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(1310761, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeAccessRequest(buffer) {\n  var val, len, offset = 0, fields = {\n    realm: void 0,\n    exclusive: void 0,\n    passive: void 0,\n    active: void 0,\n    write: void 0,\n    read: void 0\n  };\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.realm = val;\n  val = !!(1 & buffer[offset]);\n  fields.exclusive = val;\n  val = !!(2 & buffer[offset]);\n  fields.passive = val;\n  val = !!(4 & buffer[offset]);\n  fields.active = val;\n  val = !!(8 & buffer[offset]);\n  fields.write = val;\n  val = !!(16 & buffer[offset]);\n  fields.read = val;\n  return fields;\n}\n\nfunction encodeAccessRequest(channel, fields) {\n  var offset = 0, val = null, bits = 0, varyingSize = 0;\n  val = fields.realm;\n  if (void 0 === val) val = \"/data\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'realm' is the wrong type; must be a string (up to 255 chars)\");\n  var realm_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += realm_len;\n  var buffer = Buffer.alloc(14 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(1966090, 7);\n  offset = 11;\n  val = fields.realm;\n  void 0 === val && (val = \"/data\");\n  buffer[offset] = realm_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += realm_len;\n  val = fields.exclusive;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  val = fields.passive;\n  void 0 === val && (val = !0);\n  val && (bits += 2);\n  val = fields.active;\n  void 0 === val && (val = !0);\n  val && (bits += 4);\n  val = fields.write;\n  void 0 === val && (val = !0);\n  val && (bits += 8);\n  val = fields.read;\n  void 0 === val && (val = !0);\n  val && (bits += 16);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeAccessRequestOk(buffer) {\n  var val, offset = 0, fields = {\n    ticket: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.ticket = val;\n  return fields;\n}\n\nfunction encodeAccessRequestOk(channel, fields) {\n  var offset = 0, val = null, buffer = Buffer.alloc(14);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(1966091, 7);\n  offset = 11;\n  val = fields.ticket;\n  if (void 0 === val) val = 1; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'ticket' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeExchangeDeclare(buffer) {\n  var val, len, offset = 0, fields = {\n    ticket: void 0,\n    exchange: void 0,\n    type: void 0,\n    passive: void 0,\n    durable: void 0,\n    autoDelete: void 0,\n    internal: void 0,\n    nowait: void 0,\n    arguments: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.ticket = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.exchange = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.type = val;\n  val = !!(1 & buffer[offset]);\n  fields.passive = val;\n  val = !!(2 & buffer[offset]);\n  fields.durable = val;\n  val = !!(4 & buffer[offset]);\n  fields.autoDelete = val;\n  val = !!(8 & buffer[offset]);\n  fields.internal = val;\n  val = !!(16 & buffer[offset]);\n  fields.nowait = val;\n  offset++;\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = decodeFields(buffer.subarray(offset, offset + len));\n  offset += len;\n  fields.arguments = val;\n  return fields;\n}\n\nfunction encodeExchangeDeclare(channel, fields) {\n  var len, offset = 0, val = null, bits = 0, varyingSize = 0, scratchOffset = 0;\n  val = fields.exchange;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'exchange'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'exchange' is the wrong type; must be a string (up to 255 chars)\");\n  var exchange_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += exchange_len;\n  val = fields.type;\n  if (void 0 === val) val = \"direct\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'type' is the wrong type; must be a string (up to 255 chars)\");\n  var type_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += type_len;\n  val = fields.arguments;\n  if (void 0 === val) val = {}; else if (\"object\" != typeof val) throw new TypeError(\"Field 'arguments' is the wrong type; must be an object\");\n  len = encodeTable(SCRATCH, val, scratchOffset);\n  var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);\n  scratchOffset += len;\n  varyingSize += arguments_encoded.length;\n  var buffer = Buffer.alloc(17 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(2621450, 7);\n  offset = 11;\n  val = fields.ticket;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'ticket' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.exchange;\n  void 0 === val && (val = void 0);\n  buffer[offset] = exchange_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += exchange_len;\n  val = fields.type;\n  void 0 === val && (val = \"direct\");\n  buffer[offset] = type_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += type_len;\n  val = fields.passive;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  val = fields.durable;\n  void 0 === val && (val = !1);\n  val && (bits += 2);\n  val = fields.autoDelete;\n  void 0 === val && (val = !1);\n  val && (bits += 4);\n  val = fields.internal;\n  void 0 === val && (val = !1);\n  val && (bits += 8);\n  val = fields.nowait;\n  void 0 === val && (val = !1);\n  val && (bits += 16);\n  buffer[offset] = bits;\n  offset++;\n  bits = 0;\n  offset += arguments_encoded.copy(buffer, offset);\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeExchangeDeclareOk(buffer) {\n  return {};\n}\n\nfunction encodeExchangeDeclareOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(2621451, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeExchangeDelete(buffer) {\n  var val, len, offset = 0, fields = {\n    ticket: void 0,\n    exchange: void 0,\n    ifUnused: void 0,\n    nowait: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.ticket = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.exchange = val;\n  val = !!(1 & buffer[offset]);\n  fields.ifUnused = val;\n  val = !!(2 & buffer[offset]);\n  fields.nowait = val;\n  return fields;\n}\n\nfunction encodeExchangeDelete(channel, fields) {\n  var offset = 0, val = null, bits = 0, varyingSize = 0;\n  val = fields.exchange;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'exchange'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'exchange' is the wrong type; must be a string (up to 255 chars)\");\n  var exchange_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += exchange_len;\n  var buffer = Buffer.alloc(16 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(2621460, 7);\n  offset = 11;\n  val = fields.ticket;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'ticket' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.exchange;\n  void 0 === val && (val = void 0);\n  buffer[offset] = exchange_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += exchange_len;\n  val = fields.ifUnused;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  val = fields.nowait;\n  void 0 === val && (val = !1);\n  val && (bits += 2);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeExchangeDeleteOk(buffer) {\n  return {};\n}\n\nfunction encodeExchangeDeleteOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(2621461, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeExchangeBind(buffer) {\n  var val, len, offset = 0, fields = {\n    ticket: void 0,\n    destination: void 0,\n    source: void 0,\n    routingKey: void 0,\n    nowait: void 0,\n    arguments: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.ticket = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.destination = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.source = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.routingKey = val;\n  val = !!(1 & buffer[offset]);\n  fields.nowait = val;\n  offset++;\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = decodeFields(buffer.subarray(offset, offset + len));\n  offset += len;\n  fields.arguments = val;\n  return fields;\n}\n\nfunction encodeExchangeBind(channel, fields) {\n  var len, offset = 0, val = null, bits = 0, varyingSize = 0, scratchOffset = 0;\n  val = fields.destination;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'destination'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'destination' is the wrong type; must be a string (up to 255 chars)\");\n  var destination_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += destination_len;\n  val = fields.source;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'source'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'source' is the wrong type; must be a string (up to 255 chars)\");\n  var source_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += source_len;\n  val = fields.routingKey;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'routingKey' is the wrong type; must be a string (up to 255 chars)\");\n  var routingKey_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += routingKey_len;\n  val = fields.arguments;\n  if (void 0 === val) val = {}; else if (\"object\" != typeof val) throw new TypeError(\"Field 'arguments' is the wrong type; must be an object\");\n  len = encodeTable(SCRATCH, val, scratchOffset);\n  var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);\n  scratchOffset += len;\n  varyingSize += arguments_encoded.length;\n  var buffer = Buffer.alloc(18 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(2621470, 7);\n  offset = 11;\n  val = fields.ticket;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'ticket' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.destination;\n  void 0 === val && (val = void 0);\n  buffer[offset] = destination_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += destination_len;\n  val = fields.source;\n  void 0 === val && (val = void 0);\n  buffer[offset] = source_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += source_len;\n  val = fields.routingKey;\n  void 0 === val && (val = \"\");\n  buffer[offset] = routingKey_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += routingKey_len;\n  val = fields.nowait;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  bits = 0;\n  offset += arguments_encoded.copy(buffer, offset);\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeExchangeBindOk(buffer) {\n  return {};\n}\n\nfunction encodeExchangeBindOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(2621471, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeExchangeUnbind(buffer) {\n  var val, len, offset = 0, fields = {\n    ticket: void 0,\n    destination: void 0,\n    source: void 0,\n    routingKey: void 0,\n    nowait: void 0,\n    arguments: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.ticket = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.destination = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.source = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.routingKey = val;\n  val = !!(1 & buffer[offset]);\n  fields.nowait = val;\n  offset++;\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = decodeFields(buffer.subarray(offset, offset + len));\n  offset += len;\n  fields.arguments = val;\n  return fields;\n}\n\nfunction encodeExchangeUnbind(channel, fields) {\n  var len, offset = 0, val = null, bits = 0, varyingSize = 0, scratchOffset = 0;\n  val = fields.destination;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'destination'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'destination' is the wrong type; must be a string (up to 255 chars)\");\n  var destination_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += destination_len;\n  val = fields.source;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'source'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'source' is the wrong type; must be a string (up to 255 chars)\");\n  var source_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += source_len;\n  val = fields.routingKey;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'routingKey' is the wrong type; must be a string (up to 255 chars)\");\n  var routingKey_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += routingKey_len;\n  val = fields.arguments;\n  if (void 0 === val) val = {}; else if (\"object\" != typeof val) throw new TypeError(\"Field 'arguments' is the wrong type; must be an object\");\n  len = encodeTable(SCRATCH, val, scratchOffset);\n  var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);\n  scratchOffset += len;\n  varyingSize += arguments_encoded.length;\n  var buffer = Buffer.alloc(18 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(2621480, 7);\n  offset = 11;\n  val = fields.ticket;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'ticket' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.destination;\n  void 0 === val && (val = void 0);\n  buffer[offset] = destination_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += destination_len;\n  val = fields.source;\n  void 0 === val && (val = void 0);\n  buffer[offset] = source_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += source_len;\n  val = fields.routingKey;\n  void 0 === val && (val = \"\");\n  buffer[offset] = routingKey_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += routingKey_len;\n  val = fields.nowait;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  bits = 0;\n  offset += arguments_encoded.copy(buffer, offset);\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeExchangeUnbindOk(buffer) {\n  return {};\n}\n\nfunction encodeExchangeUnbindOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(2621491, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeQueueDeclare(buffer) {\n  var val, len, offset = 0, fields = {\n    ticket: void 0,\n    queue: void 0,\n    passive: void 0,\n    durable: void 0,\n    exclusive: void 0,\n    autoDelete: void 0,\n    nowait: void 0,\n    arguments: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.ticket = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.queue = val;\n  val = !!(1 & buffer[offset]);\n  fields.passive = val;\n  val = !!(2 & buffer[offset]);\n  fields.durable = val;\n  val = !!(4 & buffer[offset]);\n  fields.exclusive = val;\n  val = !!(8 & buffer[offset]);\n  fields.autoDelete = val;\n  val = !!(16 & buffer[offset]);\n  fields.nowait = val;\n  offset++;\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = decodeFields(buffer.subarray(offset, offset + len));\n  offset += len;\n  fields.arguments = val;\n  return fields;\n}\n\nfunction encodeQueueDeclare(channel, fields) {\n  var len, offset = 0, val = null, bits = 0, varyingSize = 0, scratchOffset = 0;\n  val = fields.queue;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'queue' is the wrong type; must be a string (up to 255 chars)\");\n  var queue_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += queue_len;\n  val = fields.arguments;\n  if (void 0 === val) val = {}; else if (\"object\" != typeof val) throw new TypeError(\"Field 'arguments' is the wrong type; must be an object\");\n  len = encodeTable(SCRATCH, val, scratchOffset);\n  var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);\n  scratchOffset += len;\n  varyingSize += arguments_encoded.length;\n  var buffer = Buffer.alloc(16 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3276810, 7);\n  offset = 11;\n  val = fields.ticket;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'ticket' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.queue;\n  void 0 === val && (val = \"\");\n  buffer[offset] = queue_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += queue_len;\n  val = fields.passive;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  val = fields.durable;\n  void 0 === val && (val = !1);\n  val && (bits += 2);\n  val = fields.exclusive;\n  void 0 === val && (val = !1);\n  val && (bits += 4);\n  val = fields.autoDelete;\n  void 0 === val && (val = !1);\n  val && (bits += 8);\n  val = fields.nowait;\n  void 0 === val && (val = !1);\n  val && (bits += 16);\n  buffer[offset] = bits;\n  offset++;\n  bits = 0;\n  offset += arguments_encoded.copy(buffer, offset);\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeQueueDeclareOk(buffer) {\n  var val, len, offset = 0, fields = {\n    queue: void 0,\n    messageCount: void 0,\n    consumerCount: void 0\n  };\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.queue = val;\n  val = buffer.readUInt32BE(offset);\n  offset += 4;\n  fields.messageCount = val;\n  val = buffer.readUInt32BE(offset);\n  offset += 4;\n  fields.consumerCount = val;\n  return fields;\n}\n\nfunction encodeQueueDeclareOk(channel, fields) {\n  var offset = 0, val = null, varyingSize = 0;\n  val = fields.queue;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'queue'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'queue' is the wrong type; must be a string (up to 255 chars)\");\n  var queue_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += queue_len;\n  var buffer = Buffer.alloc(21 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3276811, 7);\n  offset = 11;\n  val = fields.queue;\n  void 0 === val && (val = void 0);\n  buffer[offset] = queue_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += queue_len;\n  val = fields.messageCount;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'messageCount'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'messageCount' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt32BE(val, offset);\n  offset += 4;\n  val = fields.consumerCount;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'consumerCount'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'consumerCount' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt32BE(val, offset);\n  offset += 4;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeQueueBind(buffer) {\n  var val, len, offset = 0, fields = {\n    ticket: void 0,\n    queue: void 0,\n    exchange: void 0,\n    routingKey: void 0,\n    nowait: void 0,\n    arguments: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.ticket = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.queue = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.exchange = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.routingKey = val;\n  val = !!(1 & buffer[offset]);\n  fields.nowait = val;\n  offset++;\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = decodeFields(buffer.subarray(offset, offset + len));\n  offset += len;\n  fields.arguments = val;\n  return fields;\n}\n\nfunction encodeQueueBind(channel, fields) {\n  var len, offset = 0, val = null, bits = 0, varyingSize = 0, scratchOffset = 0;\n  val = fields.queue;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'queue' is the wrong type; must be a string (up to 255 chars)\");\n  var queue_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += queue_len;\n  val = fields.exchange;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'exchange'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'exchange' is the wrong type; must be a string (up to 255 chars)\");\n  var exchange_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += exchange_len;\n  val = fields.routingKey;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'routingKey' is the wrong type; must be a string (up to 255 chars)\");\n  var routingKey_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += routingKey_len;\n  val = fields.arguments;\n  if (void 0 === val) val = {}; else if (\"object\" != typeof val) throw new TypeError(\"Field 'arguments' is the wrong type; must be an object\");\n  len = encodeTable(SCRATCH, val, scratchOffset);\n  var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);\n  scratchOffset += len;\n  varyingSize += arguments_encoded.length;\n  var buffer = Buffer.alloc(18 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3276820, 7);\n  offset = 11;\n  val = fields.ticket;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'ticket' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.queue;\n  void 0 === val && (val = \"\");\n  buffer[offset] = queue_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += queue_len;\n  val = fields.exchange;\n  void 0 === val && (val = void 0);\n  buffer[offset] = exchange_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += exchange_len;\n  val = fields.routingKey;\n  void 0 === val && (val = \"\");\n  buffer[offset] = routingKey_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += routingKey_len;\n  val = fields.nowait;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  bits = 0;\n  offset += arguments_encoded.copy(buffer, offset);\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeQueueBindOk(buffer) {\n  return {};\n}\n\nfunction encodeQueueBindOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3276821, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeQueuePurge(buffer) {\n  var val, len, offset = 0, fields = {\n    ticket: void 0,\n    queue: void 0,\n    nowait: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.ticket = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.queue = val;\n  val = !!(1 & buffer[offset]);\n  fields.nowait = val;\n  return fields;\n}\n\nfunction encodeQueuePurge(channel, fields) {\n  var offset = 0, val = null, bits = 0, varyingSize = 0;\n  val = fields.queue;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'queue' is the wrong type; must be a string (up to 255 chars)\");\n  var queue_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += queue_len;\n  var buffer = Buffer.alloc(16 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3276830, 7);\n  offset = 11;\n  val = fields.ticket;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'ticket' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.queue;\n  void 0 === val && (val = \"\");\n  buffer[offset] = queue_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += queue_len;\n  val = fields.nowait;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeQueuePurgeOk(buffer) {\n  var val, offset = 0, fields = {\n    messageCount: void 0\n  };\n  val = buffer.readUInt32BE(offset);\n  offset += 4;\n  fields.messageCount = val;\n  return fields;\n}\n\nfunction encodeQueuePurgeOk(channel, fields) {\n  var offset = 0, val = null, buffer = Buffer.alloc(16);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3276831, 7);\n  offset = 11;\n  val = fields.messageCount;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'messageCount'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'messageCount' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt32BE(val, offset);\n  offset += 4;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeQueueDelete(buffer) {\n  var val, len, offset = 0, fields = {\n    ticket: void 0,\n    queue: void 0,\n    ifUnused: void 0,\n    ifEmpty: void 0,\n    nowait: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.ticket = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.queue = val;\n  val = !!(1 & buffer[offset]);\n  fields.ifUnused = val;\n  val = !!(2 & buffer[offset]);\n  fields.ifEmpty = val;\n  val = !!(4 & buffer[offset]);\n  fields.nowait = val;\n  return fields;\n}\n\nfunction encodeQueueDelete(channel, fields) {\n  var offset = 0, val = null, bits = 0, varyingSize = 0;\n  val = fields.queue;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'queue' is the wrong type; must be a string (up to 255 chars)\");\n  var queue_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += queue_len;\n  var buffer = Buffer.alloc(16 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3276840, 7);\n  offset = 11;\n  val = fields.ticket;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'ticket' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.queue;\n  void 0 === val && (val = \"\");\n  buffer[offset] = queue_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += queue_len;\n  val = fields.ifUnused;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  val = fields.ifEmpty;\n  void 0 === val && (val = !1);\n  val && (bits += 2);\n  val = fields.nowait;\n  void 0 === val && (val = !1);\n  val && (bits += 4);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeQueueDeleteOk(buffer) {\n  var val, offset = 0, fields = {\n    messageCount: void 0\n  };\n  val = buffer.readUInt32BE(offset);\n  offset += 4;\n  fields.messageCount = val;\n  return fields;\n}\n\nfunction encodeQueueDeleteOk(channel, fields) {\n  var offset = 0, val = null, buffer = Buffer.alloc(16);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3276841, 7);\n  offset = 11;\n  val = fields.messageCount;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'messageCount'\");\n  if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'messageCount' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt32BE(val, offset);\n  offset += 4;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeQueueUnbind(buffer) {\n  var val, len, offset = 0, fields = {\n    ticket: void 0,\n    queue: void 0,\n    exchange: void 0,\n    routingKey: void 0,\n    arguments: void 0\n  };\n  val = buffer.readUInt16BE(offset);\n  offset += 2;\n  fields.ticket = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.queue = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.exchange = val;\n  len = buffer.readUInt8(offset);\n  offset++;\n  val = buffer.toString(\"utf8\", offset, offset + len);\n  offset += len;\n  fields.routingKey = val;\n  len = buffer.readUInt32BE(offset);\n  offset += 4;\n  val = decodeFields(buffer.subarray(offset, offset + len));\n  offset += len;\n  fields.arguments = val;\n  return fields;\n}\n\nfunction encodeQueueUnbind(channel, fields) {\n  var len, offset = 0, val = null, varyingSize = 0, scratchOffset = 0;\n  val = fields.queue;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'queue' is the wrong type; must be a string (up to 255 chars)\");\n  var queue_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += queue_len;\n  val = fields.exchange;\n  if (void 0 === val) throw new Error(\"Missing value for mandatory field 'exchange'\");\n  if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'exchange' is the wrong type; must be a string (up to 255 chars)\");\n  var exchange_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += exchange_len;\n  val = fields.routingKey;\n  if (void 0 === val) val = \"\"; else if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'routingKey' is the wrong type; must be a string (up to 255 chars)\");\n  var routingKey_len = Buffer.byteLength(val, \"utf8\");\n  varyingSize += routingKey_len;\n  val = fields.arguments;\n  if (void 0 === val) val = {}; else if (\"object\" != typeof val) throw new TypeError(\"Field 'arguments' is the wrong type; must be an object\");\n  len = encodeTable(SCRATCH, val, scratchOffset);\n  var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);\n  scratchOffset += len;\n  varyingSize += arguments_encoded.length;\n  var buffer = Buffer.alloc(17 + varyingSize);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3276850, 7);\n  offset = 11;\n  val = fields.ticket;\n  if (void 0 === val) val = 0; else if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'ticket' is the wrong type; must be a number (but not NaN)\");\n  buffer.writeUInt16BE(val, offset);\n  offset += 2;\n  val = fields.queue;\n  void 0 === val && (val = \"\");\n  buffer[offset] = queue_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += queue_len;\n  val = fields.exchange;\n  void 0 === val && (val = void 0);\n  buffer[offset] = exchange_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += exchange_len;\n  val = fields.routingKey;\n  void 0 === val && (val = \"\");\n  buffer[offset] = routingKey_len;\n  offset++;\n  buffer.write(val, offset, \"utf8\");\n  offset += routingKey_len;\n  offset += arguments_encoded.copy(buffer, offset);\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeQueueUnbindOk(buffer) {\n  return {};\n}\n\nfunction encodeQueueUnbindOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3276851, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeTxSelect(buffer) {\n  return {};\n}\n\nfunction encodeTxSelect(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(5898250, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeTxSelectOk(buffer) {\n  return {};\n}\n\nfunction encodeTxSelectOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(5898251, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeTxCommit(buffer) {\n  return {};\n}\n\nfunction encodeTxCommit(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(5898260, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeTxCommitOk(buffer) {\n  return {};\n}\n\nfunction encodeTxCommitOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(5898261, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeTxRollback(buffer) {\n  return {};\n}\n\nfunction encodeTxRollback(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(5898270, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeTxRollbackOk(buffer) {\n  return {};\n}\n\nfunction encodeTxRollbackOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(5898271, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConfirmSelect(buffer) {\n  var val, fields = {\n    nowait: void 0\n  };\n  val = !!(1 & buffer[0]);\n  fields.nowait = val;\n  return fields;\n}\n\nfunction encodeConfirmSelect(channel, fields) {\n  var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(13);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(5570570, 7);\n  offset = 11;\n  val = fields.nowait;\n  void 0 === val && (val = !1);\n  val && (bits += 1);\n  buffer[offset] = bits;\n  offset++;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction decodeConfirmSelectOk(buffer) {\n  return {};\n}\n\nfunction encodeConfirmSelectOk(channel, fields) {\n  var offset = 0, buffer = Buffer.alloc(12);\n  buffer[0] = 1;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(5570571, 7);\n  offset = 11;\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  return buffer;\n}\n\nfunction encodeBasicProperties(channel, size, fields) {\n  var val, len, offset = 0, flags = 0, scratchOffset = 0, varyingSize = 0;\n  val = fields.contentType;\n  if (void 0 != val) {\n    if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'contentType' is the wrong type; must be a string (up to 255 chars)\");\n    var contentType_len = Buffer.byteLength(val, \"utf8\");\n    varyingSize += 1;\n    varyingSize += contentType_len;\n  }\n  val = fields.contentEncoding;\n  if (void 0 != val) {\n    if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'contentEncoding' is the wrong type; must be a string (up to 255 chars)\");\n    var contentEncoding_len = Buffer.byteLength(val, \"utf8\");\n    varyingSize += 1;\n    varyingSize += contentEncoding_len;\n  }\n  val = fields.headers;\n  if (void 0 != val) {\n    if (\"object\" != typeof val) throw new TypeError(\"Field 'headers' is the wrong type; must be an object\");\n    len = encodeTable(SCRATCH, val, scratchOffset);\n    var headers_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);\n    scratchOffset += len;\n    varyingSize += headers_encoded.length;\n  }\n  val = fields.deliveryMode;\n  if (void 0 != val) {\n    if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'deliveryMode' is the wrong type; must be a number (but not NaN)\");\n    varyingSize += 1;\n  }\n  val = fields.priority;\n  if (void 0 != val) {\n    if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'priority' is the wrong type; must be a number (but not NaN)\");\n    varyingSize += 1;\n  }\n  val = fields.correlationId;\n  if (void 0 != val) {\n    if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'correlationId' is the wrong type; must be a string (up to 255 chars)\");\n    var correlationId_len = Buffer.byteLength(val, \"utf8\");\n    varyingSize += 1;\n    varyingSize += correlationId_len;\n  }\n  val = fields.replyTo;\n  if (void 0 != val) {\n    if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'replyTo' is the wrong type; must be a string (up to 255 chars)\");\n    var replyTo_len = Buffer.byteLength(val, \"utf8\");\n    varyingSize += 1;\n    varyingSize += replyTo_len;\n  }\n  val = fields.expiration;\n  if (void 0 != val) {\n    if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'expiration' is the wrong type; must be a string (up to 255 chars)\");\n    var expiration_len = Buffer.byteLength(val, \"utf8\");\n    varyingSize += 1;\n    varyingSize += expiration_len;\n  }\n  val = fields.messageId;\n  if (void 0 != val) {\n    if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'messageId' is the wrong type; must be a string (up to 255 chars)\");\n    var messageId_len = Buffer.byteLength(val, \"utf8\");\n    varyingSize += 1;\n    varyingSize += messageId_len;\n  }\n  val = fields.timestamp;\n  if (void 0 != val) {\n    if (\"number\" != typeof val || isNaN(val)) throw new TypeError(\"Field 'timestamp' is the wrong type; must be a number (but not NaN)\");\n    varyingSize += 8;\n  }\n  val = fields.type;\n  if (void 0 != val) {\n    if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'type' is the wrong type; must be a string (up to 255 chars)\");\n    var type_len = Buffer.byteLength(val, \"utf8\");\n    varyingSize += 1;\n    varyingSize += type_len;\n  }\n  val = fields.userId;\n  if (void 0 != val) {\n    if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'userId' is the wrong type; must be a string (up to 255 chars)\");\n    var userId_len = Buffer.byteLength(val, \"utf8\");\n    varyingSize += 1;\n    varyingSize += userId_len;\n  }\n  val = fields.appId;\n  if (void 0 != val) {\n    if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'appId' is the wrong type; must be a string (up to 255 chars)\");\n    var appId_len = Buffer.byteLength(val, \"utf8\");\n    varyingSize += 1;\n    varyingSize += appId_len;\n  }\n  val = fields.clusterId;\n  if (void 0 != val) {\n    if (!(\"string\" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError(\"Field 'clusterId' is the wrong type; must be a string (up to 255 chars)\");\n    var clusterId_len = Buffer.byteLength(val, \"utf8\");\n    varyingSize += 1;\n    varyingSize += clusterId_len;\n  }\n  var buffer = Buffer.alloc(22 + varyingSize);\n  buffer[0] = 2;\n  buffer.writeUInt16BE(channel, 1);\n  buffer.writeUInt32BE(3932160, 7);\n  ints.writeUInt64BE(buffer, size, 11);\n  flags = 0;\n  offset = 21;\n  val = fields.contentType;\n  if (void 0 != val) {\n    flags += 32768;\n    buffer[offset] = contentType_len;\n    offset++;\n    buffer.write(val, offset, \"utf8\");\n    offset += contentType_len;\n  }\n  val = fields.contentEncoding;\n  if (void 0 != val) {\n    flags += 16384;\n    buffer[offset] = contentEncoding_len;\n    offset++;\n    buffer.write(val, offset, \"utf8\");\n    offset += contentEncoding_len;\n  }\n  val = fields.headers;\n  if (void 0 != val) {\n    flags += 8192;\n    offset += headers_encoded.copy(buffer, offset);\n  }\n  val = fields.deliveryMode;\n  if (void 0 != val) {\n    flags += 4096;\n    buffer.writeUInt8(val, offset);\n    offset++;\n  }\n  val = fields.priority;\n  if (void 0 != val) {\n    flags += 2048;\n    buffer.writeUInt8(val, offset);\n    offset++;\n  }\n  val = fields.correlationId;\n  if (void 0 != val) {\n    flags += 1024;\n    buffer[offset] = correlationId_len;\n    offset++;\n    buffer.write(val, offset, \"utf8\");\n    offset += correlationId_len;\n  }\n  val = fields.replyTo;\n  if (void 0 != val) {\n    flags += 512;\n    buffer[offset] = replyTo_len;\n    offset++;\n    buffer.write(val, offset, \"utf8\");\n    offset += replyTo_len;\n  }\n  val = fields.expiration;\n  if (void 0 != val) {\n    flags += 256;\n    buffer[offset] = expiration_len;\n    offset++;\n    buffer.write(val, offset, \"utf8\");\n    offset += expiration_len;\n  }\n  val = fields.messageId;\n  if (void 0 != val) {\n    flags += 128;\n    buffer[offset] = messageId_len;\n    offset++;\n    buffer.write(val, offset, \"utf8\");\n    offset += messageId_len;\n  }\n  val = fields.timestamp;\n  if (void 0 != val) {\n    flags += 64;\n    ints.writeUInt64BE(buffer, val, offset);\n    offset += 8;\n  }\n  val = fields.type;\n  if (void 0 != val) {\n    flags += 32;\n    buffer[offset] = type_len;\n    offset++;\n    buffer.write(val, offset, \"utf8\");\n    offset += type_len;\n  }\n  val = fields.userId;\n  if (void 0 != val) {\n    flags += 16;\n    buffer[offset] = userId_len;\n    offset++;\n    buffer.write(val, offset, \"utf8\");\n    offset += userId_len;\n  }\n  val = fields.appId;\n  if (void 0 != val) {\n    flags += 8;\n    buffer[offset] = appId_len;\n    offset++;\n    buffer.write(val, offset, \"utf8\");\n    offset += appId_len;\n  }\n  val = fields.clusterId;\n  if (void 0 != val) {\n    flags += 4;\n    buffer[offset] = clusterId_len;\n    offset++;\n    buffer.write(val, offset, \"utf8\");\n    offset += clusterId_len;\n  }\n  buffer[offset] = 206;\n  buffer.writeUInt32BE(offset - 7, 3);\n  buffer.writeUInt16BE(flags, 19);\n  return buffer.subarray(0, offset + 1);\n}\n\nfunction decodeBasicProperties(buffer) {\n  var flags, val, len, offset = 2;\n  flags = buffer.readUInt16BE(0);\n  if (0 === flags) return {};\n  var fields = {\n    contentType: void 0,\n    contentEncoding: void 0,\n    headers: void 0,\n    deliveryMode: void 0,\n    priority: void 0,\n    correlationId: void 0,\n    replyTo: void 0,\n    expiration: void 0,\n    messageId: void 0,\n    timestamp: void 0,\n    type: void 0,\n    userId: void 0,\n    appId: void 0,\n    clusterId: void 0\n  };\n  if (32768 & flags) {\n    len = buffer.readUInt8(offset);\n    offset++;\n    val = buffer.toString(\"utf8\", offset, offset + len);\n    offset += len;\n    fields.contentType = val;\n  }\n  if (16384 & flags) {\n    len = buffer.readUInt8(offset);\n    offset++;\n    val = buffer.toString(\"utf8\", offset, offset + len);\n    offset += len;\n    fields.contentEncoding = val;\n  }\n  if (8192 & flags) {\n    len = buffer.readUInt32BE(offset);\n    offset += 4;\n    val = decodeFields(buffer.subarray(offset, offset + len));\n    offset += len;\n    fields.headers = val;\n  }\n  if (4096 & flags) {\n    val = buffer[offset];\n    offset++;\n    fields.deliveryMode = val;\n  }\n  if (2048 & flags) {\n    val = buffer[offset];\n    offset++;\n    fields.priority = val;\n  }\n  if (1024 & flags) {\n    len = buffer.readUInt8(offset);\n    offset++;\n    val = buffer.toString(\"utf8\", offset, offset + len);\n    offset += len;\n    fields.correlationId = val;\n  }\n  if (512 & flags) {\n    len = buffer.readUInt8(offset);\n    offset++;\n    val = buffer.toString(\"utf8\", offset, offset + len);\n    offset += len;\n    fields.replyTo = val;\n  }\n  if (256 & flags) {\n    len = buffer.readUInt8(offset);\n    offset++;\n    val = buffer.toString(\"utf8\", offset, offset + len);\n    offset += len;\n    fields.expiration = val;\n  }\n  if (128 & flags) {\n    len = buffer.readUInt8(offset);\n    offset++;\n    val = buffer.toString(\"utf8\", offset, offset + len);\n    offset += len;\n    fields.messageId = val;\n  }\n  if (64 & flags) {\n    val = ints.readUInt64BE(buffer, offset);\n    offset += 8;\n    fields.timestamp = val;\n  }\n  if (32 & flags) {\n    len = buffer.readUInt8(offset);\n    offset++;\n    val = buffer.toString(\"utf8\", offset, offset + len);\n    offset += len;\n    fields.type = val;\n  }\n  if (16 & flags) {\n    len = buffer.readUInt8(offset);\n    offset++;\n    val = buffer.toString(\"utf8\", offset, offset + len);\n    offset += len;\n    fields.userId = val;\n  }\n  if (8 & flags) {\n    len = buffer.readUInt8(offset);\n    offset++;\n    val = buffer.toString(\"utf8\", offset, offset + len);\n    offset += len;\n    fields.appId = val;\n  }\n  if (4 & flags) {\n    len = buffer.readUInt8(offset);\n    offset++;\n    val = buffer.toString(\"utf8\", offset, offset + len);\n    offset += len;\n    fields.clusterId = val;\n  }\n  return fields;\n}\n\nvar codec = require(\"./codec\"), ints = require(\"buffer-more-ints\"), encodeTable = codec.encodeTable, decodeFields = codec.decodeFields, SCRATCH = Buffer.alloc(65536), EMPTY_OBJECT = Object.freeze({});\n\nmodule.exports.constants = {\n  FRAME_METHOD: 1,\n  FRAME_HEADER: 2,\n  FRAME_BODY: 3,\n  FRAME_HEARTBEAT: 8,\n  FRAME_MIN_SIZE: 4096,\n  FRAME_END: 206,\n  REPLY_SUCCESS: 200,\n  CONTENT_TOO_LARGE: 311,\n  NO_ROUTE: 312,\n  NO_CONSUMERS: 313,\n  ACCESS_REFUSED: 403,\n  NOT_FOUND: 404,\n  RESOURCE_LOCKED: 405,\n  PRECONDITION_FAILED: 406,\n  CONNECTION_FORCED: 320,\n  INVALID_PATH: 402,\n  FRAME_ERROR: 501,\n  SYNTAX_ERROR: 502,\n  COMMAND_INVALID: 503,\n  CHANNEL_ERROR: 504,\n  UNEXPECTED_FRAME: 505,\n  RESOURCE_ERROR: 506,\n  NOT_ALLOWED: 530,\n  NOT_IMPLEMENTED: 540,\n  INTERNAL_ERROR: 541\n};\n\nmodule.exports.constant_strs = {\n  \"1\": \"FRAME-METHOD\",\n  \"2\": \"FRAME-HEADER\",\n  \"3\": \"FRAME-BODY\",\n  \"8\": \"FRAME-HEARTBEAT\",\n  \"200\": \"REPLY-SUCCESS\",\n  \"206\": \"FRAME-END\",\n  \"311\": \"CONTENT-TOO-LARGE\",\n  \"312\": \"NO-ROUTE\",\n  \"313\": \"NO-CONSUMERS\",\n  \"320\": \"CONNECTION-FORCED\",\n  \"402\": \"INVALID-PATH\",\n  \"403\": \"ACCESS-REFUSED\",\n  \"404\": \"NOT-FOUND\",\n  \"405\": \"RESOURCE-LOCKED\",\n  \"406\": \"PRECONDITION-FAILED\",\n  \"501\": \"FRAME-ERROR\",\n  \"502\": \"SYNTAX-ERROR\",\n  \"503\": \"COMMAND-INVALID\",\n  \"504\": \"CHANNEL-ERROR\",\n  \"505\": \"UNEXPECTED-FRAME\",\n  \"506\": \"RESOURCE-ERROR\",\n  \"530\": \"NOT-ALLOWED\",\n  \"540\": \"NOT-IMPLEMENTED\",\n  \"541\": \"INTERNAL-ERROR\",\n  \"4096\": \"FRAME-MIN-SIZE\"\n};\n\nmodule.exports.FRAME_OVERHEAD = 8;\n\nmodule.exports.decode = function(id, buf) {\n  switch (id) {\n   case 3932170:\n    return decodeBasicQos(buf);\n\n   case 3932171:\n    return decodeBasicQosOk(buf);\n\n   case 3932180:\n    return decodeBasicConsume(buf);\n\n   case 3932181:\n    return decodeBasicConsumeOk(buf);\n\n   case 3932190:\n    return decodeBasicCancel(buf);\n\n   case 3932191:\n    return decodeBasicCancelOk(buf);\n\n   case 3932200:\n    return decodeBasicPublish(buf);\n\n   case 3932210:\n    return decodeBasicReturn(buf);\n\n   case 3932220:\n    return decodeBasicDeliver(buf);\n\n   case 3932230:\n    return decodeBasicGet(buf);\n\n   case 3932231:\n    return decodeBasicGetOk(buf);\n\n   case 3932232:\n    return decodeBasicGetEmpty(buf);\n\n   case 3932240:\n    return decodeBasicAck(buf);\n\n   case 3932250:\n    return decodeBasicReject(buf);\n\n   case 3932260:\n    return decodeBasicRecoverAsync(buf);\n\n   case 3932270:\n    return decodeBasicRecover(buf);\n\n   case 3932271:\n    return decodeBasicRecoverOk(buf);\n\n   case 3932280:\n    return decodeBasicNack(buf);\n\n   case 655370:\n    return decodeConnectionStart(buf);\n\n   case 655371:\n    return decodeConnectionStartOk(buf);\n\n   case 655380:\n    return decodeConnectionSecure(buf);\n\n   case 655381:\n    return decodeConnectionSecureOk(buf);\n\n   case 655390:\n    return decodeConnectionTune(buf);\n\n   case 655391:\n    return decodeConnectionTuneOk(buf);\n\n   case 655400:\n    return decodeConnectionOpen(buf);\n\n   case 655401:\n    return decodeConnectionOpenOk(buf);\n\n   case 655410:\n    return decodeConnectionClose(buf);\n\n   case 655411:\n    return decodeConnectionCloseOk(buf);\n\n   case 655420:\n    return decodeConnectionBlocked(buf);\n\n   case 655421:\n    return decodeConnectionUnblocked(buf);\n\n   case 655430:\n    return decodeConnectionUpdateSecret(buf);\n\n   case 655431:\n    return decodeConnectionUpdateSecretOk(buf);\n\n   case 1310730:\n    return decodeChannelOpen(buf);\n\n   case 1310731:\n    return decodeChannelOpenOk(buf);\n\n   case 1310740:\n    return decodeChannelFlow(buf);\n\n   case 1310741:\n    return decodeChannelFlowOk(buf);\n\n   case 1310760:\n    return decodeChannelClose(buf);\n\n   case 1310761:\n    return decodeChannelCloseOk(buf);\n\n   case 1966090:\n    return decodeAccessRequest(buf);\n\n   case 1966091:\n    return decodeAccessRequestOk(buf);\n\n   case 2621450:\n    return decodeExchangeDeclare(buf);\n\n   case 2621451:\n    return decodeExchangeDeclareOk(buf);\n\n   case 2621460:\n    return decodeExchangeDelete(buf);\n\n   case 2621461:\n    return decodeExchangeDeleteOk(buf);\n\n   case 2621470:\n    return decodeExchangeBind(buf);\n\n   case 2621471:\n    return decodeExchangeBindOk(buf);\n\n   case 2621480:\n    return decodeExchangeUnbind(buf);\n\n   case 2621491:\n    return decodeExchangeUnbindOk(buf);\n\n   case 3276810:\n    return decodeQueueDeclare(buf);\n\n   case 3276811:\n    return decodeQueueDeclareOk(buf);\n\n   case 3276820:\n    return decodeQueueBind(buf);\n\n   case 3276821:\n    return decodeQueueBindOk(buf);\n\n   case 3276830:\n    return decodeQueuePurge(buf);\n\n   case 3276831:\n    return decodeQueuePurgeOk(buf);\n\n   case 3276840:\n    return decodeQueueDelete(buf);\n\n   case 3276841:\n    return decodeQueueDeleteOk(buf);\n\n   case 3276850:\n    return decodeQueueUnbind(buf);\n\n   case 3276851:\n    return decodeQueueUnbindOk(buf);\n\n   case 5898250:\n    return decodeTxSelect(buf);\n\n   case 5898251:\n    return decodeTxSelectOk(buf);\n\n   case 5898260:\n    return decodeTxCommit(buf);\n\n   case 5898261:\n    return decodeTxCommitOk(buf);\n\n   case 5898270:\n    return decodeTxRollback(buf);\n\n   case 5898271:\n    return decodeTxRollbackOk(buf);\n\n   case 5570570:\n    return decodeConfirmSelect(buf);\n\n   case 5570571:\n    return decodeConfirmSelectOk(buf);\n\n   case 60:\n    return decodeBasicProperties(buf);\n\n   default:\n    throw new Error(\"Unknown class/method ID\");\n  }\n};\n\nmodule.exports.encodeMethod = function(id, channel, fields) {\n  switch (id) {\n   case 3932170:\n    return encodeBasicQos(channel, fields);\n\n   case 3932171:\n    return encodeBasicQosOk(channel, fields);\n\n   case 3932180:\n    return encodeBasicConsume(channel, fields);\n\n   case 3932181:\n    return encodeBasicConsumeOk(channel, fields);\n\n   case 3932190:\n    return encodeBasicCancel(channel, fields);\n\n   case 3932191:\n    return encodeBasicCancelOk(channel, fields);\n\n   case 3932200:\n    return encodeBasicPublish(channel, fields);\n\n   case 3932210:\n    return encodeBasicReturn(channel, fields);\n\n   case 3932220:\n    return encodeBasicDeliver(channel, fields);\n\n   case 3932230:\n    return encodeBasicGet(channel, fields);\n\n   case 3932231:\n    return encodeBasicGetOk(channel, fields);\n\n   case 3932232:\n    return encodeBasicGetEmpty(channel, fields);\n\n   case 3932240:\n    return encodeBasicAck(channel, fields);\n\n   case 3932250:\n    return encodeBasicReject(channel, fields);\n\n   case 3932260:\n    return encodeBasicRecoverAsync(channel, fields);\n\n   case 3932270:\n    return encodeBasicRecover(channel, fields);\n\n   case 3932271:\n    return encodeBasicRecoverOk(channel, fields);\n\n   case 3932280:\n    return encodeBasicNack(channel, fields);\n\n   case 655370:\n    return encodeConnectionStart(channel, fields);\n\n   case 655371:\n    return encodeConnectionStartOk(channel, fields);\n\n   case 655380:\n    return encodeConnectionSecure(channel, fields);\n\n   case 655381:\n    return encodeConnectionSecureOk(channel, fields);\n\n   case 655390:\n    return encodeConnectionTune(channel, fields);\n\n   case 655391:\n    return encodeConnectionTuneOk(channel, fields);\n\n   case 655400:\n    return encodeConnectionOpen(channel, fields);\n\n   case 655401:\n    return encodeConnectionOpenOk(channel, fields);\n\n   case 655410:\n    return encodeConnectionClose(channel, fields);\n\n   case 655411:\n    return encodeConnectionCloseOk(channel, fields);\n\n   case 655420:\n    return encodeConnectionBlocked(channel, fields);\n\n   case 655421:\n    return encodeConnectionUnblocked(channel, fields);\n\n   case 655430:\n    return encodeConnectionUpdateSecret(channel, fields);\n\n   case 655431:\n    return encodeConnectionUpdateSecretOk(channel, fields);\n\n   case 1310730:\n    return encodeChannelOpen(channel, fields);\n\n   case 1310731:\n    return encodeChannelOpenOk(channel, fields);\n\n   case 1310740:\n    return encodeChannelFlow(channel, fields);\n\n   case 1310741:\n    return encodeChannelFlowOk(channel, fields);\n\n   case 1310760:\n    return encodeChannelClose(channel, fields);\n\n   case 1310761:\n    return encodeChannelCloseOk(channel, fields);\n\n   case 1966090:\n    return encodeAccessRequest(channel, fields);\n\n   case 1966091:\n    return encodeAccessRequestOk(channel, fields);\n\n   case 2621450:\n    return encodeExchangeDeclare(channel, fields);\n\n   case 2621451:\n    return encodeExchangeDeclareOk(channel, fields);\n\n   case 2621460:\n    return encodeExchangeDelete(channel, fields);\n\n   case 2621461:\n    return encodeExchangeDeleteOk(channel, fields);\n\n   case 2621470:\n    return encodeExchangeBind(channel, fields);\n\n   case 2621471:\n    return encodeExchangeBindOk(channel, fields);\n\n   case 2621480:\n    return encodeExchangeUnbind(channel, fields);\n\n   case 2621491:\n    return encodeExchangeUnbindOk(channel, fields);\n\n   case 3276810:\n    return encodeQueueDeclare(channel, fields);\n\n   case 3276811:\n    return encodeQueueDeclareOk(channel, fields);\n\n   case 3276820:\n    return encodeQueueBind(channel, fields);\n\n   case 3276821:\n    return encodeQueueBindOk(channel, fields);\n\n   case 3276830:\n    return encodeQueuePurge(channel, fields);\n\n   case 3276831:\n    return encodeQueuePurgeOk(channel, fields);\n\n   case 3276840:\n    return encodeQueueDelete(channel, fields);\n\n   case 3276841:\n    return encodeQueueDeleteOk(channel, fields);\n\n   case 3276850:\n    return encodeQueueUnbind(channel, fields);\n\n   case 3276851:\n    return encodeQueueUnbindOk(channel, fields);\n\n   case 5898250:\n    return encodeTxSelect(channel, fields);\n\n   case 5898251:\n    return encodeTxSelectOk(channel, fields);\n\n   case 5898260:\n    return encodeTxCommit(channel, fields);\n\n   case 5898261:\n    return encodeTxCommitOk(channel, fields);\n\n   case 5898270:\n    return encodeTxRollback(channel, fields);\n\n   case 5898271:\n    return encodeTxRollbackOk(channel, fields);\n\n   case 5570570:\n    return encodeConfirmSelect(channel, fields);\n\n   case 5570571:\n    return encodeConfirmSelectOk(channel, fields);\n\n   default:\n    throw new Error(\"Unknown class/method ID\");\n  }\n};\n\nmodule.exports.encodeProperties = function(id, channel, size, fields) {\n  switch (id) {\n   case 60:\n    return encodeBasicProperties(channel, size, fields);\n\n   default:\n    throw new Error(\"Unknown class/properties ID\");\n  }\n};\n\nmodule.exports.info = function(id) {\n  switch (id) {\n   case 3932170:\n    return methodInfoBasicQos;\n\n   case 3932171:\n    return methodInfoBasicQosOk;\n\n   case 3932180:\n    return methodInfoBasicConsume;\n\n   case 3932181:\n    return methodInfoBasicConsumeOk;\n\n   case 3932190:\n    return methodInfoBasicCancel;\n\n   case 3932191:\n    return methodInfoBasicCancelOk;\n\n   case 3932200:\n    return methodInfoBasicPublish;\n\n   case 3932210:\n    return methodInfoBasicReturn;\n\n   case 3932220:\n    return methodInfoBasicDeliver;\n\n   case 3932230:\n    return methodInfoBasicGet;\n\n   case 3932231:\n    return methodInfoBasicGetOk;\n\n   case 3932232:\n    return methodInfoBasicGetEmpty;\n\n   case 3932240:\n    return methodInfoBasicAck;\n\n   case 3932250:\n    return methodInfoBasicReject;\n\n   case 3932260:\n    return methodInfoBasicRecoverAsync;\n\n   case 3932270:\n    return methodInfoBasicRecover;\n\n   case 3932271:\n    return methodInfoBasicRecoverOk;\n\n   case 3932280:\n    return methodInfoBasicNack;\n\n   case 655370:\n    return methodInfoConnectionStart;\n\n   case 655371:\n    return methodInfoConnectionStartOk;\n\n   case 655380:\n    return methodInfoConnectionSecure;\n\n   case 655381:\n    return methodInfoConnectionSecureOk;\n\n   case 655390:\n    return methodInfoConnectionTune;\n\n   case 655391:\n    return methodInfoConnectionTuneOk;\n\n   case 655400:\n    return methodInfoConnectionOpen;\n\n   case 655401:\n    return methodInfoConnectionOpenOk;\n\n   case 655410:\n    return methodInfoConnectionClose;\n\n   case 655411:\n    return methodInfoConnectionCloseOk;\n\n   case 655420:\n    return methodInfoConnectionBlocked;\n\n   case 655421:\n    return methodInfoConnectionUnblocked;\n\n   case 655430:\n    return methodInfoConnectionUpdateSecret;\n\n   case 655431:\n    return methodInfoConnectionUpdateSecretOk;\n\n   case 1310730:\n    return methodInfoChannelOpen;\n\n   case 1310731:\n    return methodInfoChannelOpenOk;\n\n   case 1310740:\n    return methodInfoChannelFlow;\n\n   case 1310741:\n    return methodInfoChannelFlowOk;\n\n   case 1310760:\n    return methodInfoChannelClose;\n\n   case 1310761:\n    return methodInfoChannelCloseOk;\n\n   case 1966090:\n    return methodInfoAccessRequest;\n\n   case 1966091:\n    return methodInfoAccessRequestOk;\n\n   case 2621450:\n    return methodInfoExchangeDeclare;\n\n   case 2621451:\n    return methodInfoExchangeDeclareOk;\n\n   case 2621460:\n    return methodInfoExchangeDelete;\n\n   case 2621461:\n    return methodInfoExchangeDeleteOk;\n\n   case 2621470:\n    return methodInfoExchangeBind;\n\n   case 2621471:\n    return methodInfoExchangeBindOk;\n\n   case 2621480:\n    return methodInfoExchangeUnbind;\n\n   case 2621491:\n    return methodInfoExchangeUnbindOk;\n\n   case 3276810:\n    return methodInfoQueueDeclare;\n\n   case 3276811:\n    return methodInfoQueueDeclareOk;\n\n   case 3276820:\n    return methodInfoQueueBind;\n\n   case 3276821:\n    return methodInfoQueueBindOk;\n\n   case 3276830:\n    return methodInfoQueuePurge;\n\n   case 3276831:\n    return methodInfoQueuePurgeOk;\n\n   case 3276840:\n    return methodInfoQueueDelete;\n\n   case 3276841:\n    return methodInfoQueueDeleteOk;\n\n   case 3276850:\n    return methodInfoQueueUnbind;\n\n   case 3276851:\n    return methodInfoQueueUnbindOk;\n\n   case 5898250:\n    return methodInfoTxSelect;\n\n   case 5898251:\n    return methodInfoTxSelectOk;\n\n   case 5898260:\n    return methodInfoTxCommit;\n\n   case 5898261:\n    return methodInfoTxCommitOk;\n\n   case 5898270:\n    return methodInfoTxRollback;\n\n   case 5898271:\n    return methodInfoTxRollbackOk;\n\n   case 5570570:\n    return methodInfoConfirmSelect;\n\n   case 5570571:\n    return methodInfoConfirmSelectOk;\n\n   case 60:\n    return propertiesInfoBasicProperties;\n\n   default:\n    throw new Error(\"Unknown class/method ID\");\n  }\n};\n\nmodule.exports.BasicQos = 3932170;\n\nvar methodInfoBasicQos = module.exports.methodInfoBasicQos = {\n  id: 3932170,\n  classId: 60,\n  methodId: 10,\n  name: \"BasicQos\",\n  args: [ {\n    type: \"long\",\n    name: \"prefetchSize\",\n    default: 0\n  }, {\n    type: \"short\",\n    name: \"prefetchCount\",\n    default: 0\n  }, {\n    type: \"bit\",\n    name: \"global\",\n    default: !1\n  } ]\n};\n\nmodule.exports.BasicQosOk = 3932171;\n\nvar methodInfoBasicQosOk = module.exports.methodInfoBasicQosOk = {\n  id: 3932171,\n  classId: 60,\n  methodId: 11,\n  name: \"BasicQosOk\",\n  args: []\n};\n\nmodule.exports.BasicConsume = 3932180;\n\nvar methodInfoBasicConsume = module.exports.methodInfoBasicConsume = {\n  id: 3932180,\n  classId: 60,\n  methodId: 20,\n  name: \"BasicConsume\",\n  args: [ {\n    type: \"short\",\n    name: \"ticket\",\n    default: 0\n  }, {\n    type: \"shortstr\",\n    name: \"queue\",\n    default: \"\"\n  }, {\n    type: \"shortstr\",\n    name: \"consumerTag\",\n    default: \"\"\n  }, {\n    type: \"bit\",\n    name: \"noLocal\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"noAck\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"exclusive\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"nowait\",\n    default: !1\n  }, {\n    type: \"table\",\n    name: \"arguments\",\n    default: {}\n  } ]\n};\n\nmodule.exports.BasicConsumeOk = 3932181;\n\nvar methodInfoBasicConsumeOk = module.exports.methodInfoBasicConsumeOk = {\n  id: 3932181,\n  classId: 60,\n  methodId: 21,\n  name: \"BasicConsumeOk\",\n  args: [ {\n    type: \"shortstr\",\n    name: \"consumerTag\"\n  } ]\n};\n\nmodule.exports.BasicCancel = 3932190;\n\nvar methodInfoBasicCancel = module.exports.methodInfoBasicCancel = {\n  id: 3932190,\n  classId: 60,\n  methodId: 30,\n  name: \"BasicCancel\",\n  args: [ {\n    type: \"shortstr\",\n    name: \"consumerTag\"\n  }, {\n    type: \"bit\",\n    name: \"nowait\",\n    default: !1\n  } ]\n};\n\nmodule.exports.BasicCancelOk = 3932191;\n\nvar methodInfoBasicCancelOk = module.exports.methodInfoBasicCancelOk = {\n  id: 3932191,\n  classId: 60,\n  methodId: 31,\n  name: \"BasicCancelOk\",\n  args: [ {\n    type: \"shortstr\",\n    name: \"consumerTag\"\n  } ]\n};\n\nmodule.exports.BasicPublish = 3932200;\n\nvar methodInfoBasicPublish = module.exports.methodInfoBasicPublish = {\n  id: 3932200,\n  classId: 60,\n  methodId: 40,\n  name: \"BasicPublish\",\n  args: [ {\n    type: \"short\",\n    name: \"ticket\",\n    default: 0\n  }, {\n    type: \"shortstr\",\n    name: \"exchange\",\n    default: \"\"\n  }, {\n    type: \"shortstr\",\n    name: \"routingKey\",\n    default: \"\"\n  }, {\n    type: \"bit\",\n    name: \"mandatory\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"immediate\",\n    default: !1\n  } ]\n};\n\nmodule.exports.BasicReturn = 3932210;\n\nvar methodInfoBasicReturn = module.exports.methodInfoBasicReturn = {\n  id: 3932210,\n  classId: 60,\n  methodId: 50,\n  name: \"BasicReturn\",\n  args: [ {\n    type: \"short\",\n    name: \"replyCode\"\n  }, {\n    type: \"shortstr\",\n    name: \"replyText\",\n    default: \"\"\n  }, {\n    type: \"shortstr\",\n    name: \"exchange\"\n  }, {\n    type: \"shortstr\",\n    name: \"routingKey\"\n  } ]\n};\n\nmodule.exports.BasicDeliver = 3932220;\n\nvar methodInfoBasicDeliver = module.exports.methodInfoBasicDeliver = {\n  id: 3932220,\n  classId: 60,\n  methodId: 60,\n  name: \"BasicDeliver\",\n  args: [ {\n    type: \"shortstr\",\n    name: \"consumerTag\"\n  }, {\n    type: \"longlong\",\n    name: \"deliveryTag\"\n  }, {\n    type: \"bit\",\n    name: \"redelivered\",\n    default: !1\n  }, {\n    type: \"shortstr\",\n    name: \"exchange\"\n  }, {\n    type: \"shortstr\",\n    name: \"routingKey\"\n  } ]\n};\n\nmodule.exports.BasicGet = 3932230;\n\nvar methodInfoBasicGet = module.exports.methodInfoBasicGet = {\n  id: 3932230,\n  classId: 60,\n  methodId: 70,\n  name: \"BasicGet\",\n  args: [ {\n    type: \"short\",\n    name: \"ticket\",\n    default: 0\n  }, {\n    type: \"shortstr\",\n    name: \"queue\",\n    default: \"\"\n  }, {\n    type: \"bit\",\n    name: \"noAck\",\n    default: !1\n  } ]\n};\n\nmodule.exports.BasicGetOk = 3932231;\n\nvar methodInfoBasicGetOk = module.exports.methodInfoBasicGetOk = {\n  id: 3932231,\n  classId: 60,\n  methodId: 71,\n  name: \"BasicGetOk\",\n  args: [ {\n    type: \"longlong\",\n    name: \"deliveryTag\"\n  }, {\n    type: \"bit\",\n    name: \"redelivered\",\n    default: !1\n  }, {\n    type: \"shortstr\",\n    name: \"exchange\"\n  }, {\n    type: \"shortstr\",\n    name: \"routingKey\"\n  }, {\n    type: \"long\",\n    name: \"messageCount\"\n  } ]\n};\n\nmodule.exports.BasicGetEmpty = 3932232;\n\nvar methodInfoBasicGetEmpty = module.exports.methodInfoBasicGetEmpty = {\n  id: 3932232,\n  classId: 60,\n  methodId: 72,\n  name: \"BasicGetEmpty\",\n  args: [ {\n    type: \"shortstr\",\n    name: \"clusterId\",\n    default: \"\"\n  } ]\n};\n\nmodule.exports.BasicAck = 3932240;\n\nvar methodInfoBasicAck = module.exports.methodInfoBasicAck = {\n  id: 3932240,\n  classId: 60,\n  methodId: 80,\n  name: \"BasicAck\",\n  args: [ {\n    type: \"longlong\",\n    name: \"deliveryTag\",\n    default: 0\n  }, {\n    type: \"bit\",\n    name: \"multiple\",\n    default: !1\n  } ]\n};\n\nmodule.exports.BasicReject = 3932250;\n\nvar methodInfoBasicReject = module.exports.methodInfoBasicReject = {\n  id: 3932250,\n  classId: 60,\n  methodId: 90,\n  name: \"BasicReject\",\n  args: [ {\n    type: \"longlong\",\n    name: \"deliveryTag\"\n  }, {\n    type: \"bit\",\n    name: \"requeue\",\n    default: !0\n  } ]\n};\n\nmodule.exports.BasicRecoverAsync = 3932260;\n\nvar methodInfoBasicRecoverAsync = module.exports.methodInfoBasicRecoverAsync = {\n  id: 3932260,\n  classId: 60,\n  methodId: 100,\n  name: \"BasicRecoverAsync\",\n  args: [ {\n    type: \"bit\",\n    name: \"requeue\",\n    default: !1\n  } ]\n};\n\nmodule.exports.BasicRecover = 3932270;\n\nvar methodInfoBasicRecover = module.exports.methodInfoBasicRecover = {\n  id: 3932270,\n  classId: 60,\n  methodId: 110,\n  name: \"BasicRecover\",\n  args: [ {\n    type: \"bit\",\n    name: \"requeue\",\n    default: !1\n  } ]\n};\n\nmodule.exports.BasicRecoverOk = 3932271;\n\nvar methodInfoBasicRecoverOk = module.exports.methodInfoBasicRecoverOk = {\n  id: 3932271,\n  classId: 60,\n  methodId: 111,\n  name: \"BasicRecoverOk\",\n  args: []\n};\n\nmodule.exports.BasicNack = 3932280;\n\nvar methodInfoBasicNack = module.exports.methodInfoBasicNack = {\n  id: 3932280,\n  classId: 60,\n  methodId: 120,\n  name: \"BasicNack\",\n  args: [ {\n    type: \"longlong\",\n    name: \"deliveryTag\",\n    default: 0\n  }, {\n    type: \"bit\",\n    name: \"multiple\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"requeue\",\n    default: !0\n  } ]\n};\n\nmodule.exports.ConnectionStart = 655370;\n\nvar methodInfoConnectionStart = module.exports.methodInfoConnectionStart = {\n  id: 655370,\n  classId: 10,\n  methodId: 10,\n  name: \"ConnectionStart\",\n  args: [ {\n    type: \"octet\",\n    name: \"versionMajor\",\n    default: 0\n  }, {\n    type: \"octet\",\n    name: \"versionMinor\",\n    default: 9\n  }, {\n    type: \"table\",\n    name: \"serverProperties\"\n  }, {\n    type: \"longstr\",\n    name: \"mechanisms\",\n    default: \"PLAIN\"\n  }, {\n    type: \"longstr\",\n    name: \"locales\",\n    default: \"en_US\"\n  } ]\n};\n\nmodule.exports.ConnectionStartOk = 655371;\n\nvar methodInfoConnectionStartOk = module.exports.methodInfoConnectionStartOk = {\n  id: 655371,\n  classId: 10,\n  methodId: 11,\n  name: \"ConnectionStartOk\",\n  args: [ {\n    type: \"table\",\n    name: \"clientProperties\"\n  }, {\n    type: \"shortstr\",\n    name: \"mechanism\",\n    default: \"PLAIN\"\n  }, {\n    type: \"longstr\",\n    name: \"response\"\n  }, {\n    type: \"shortstr\",\n    name: \"locale\",\n    default: \"en_US\"\n  } ]\n};\n\nmodule.exports.ConnectionSecure = 655380;\n\nvar methodInfoConnectionSecure = module.exports.methodInfoConnectionSecure = {\n  id: 655380,\n  classId: 10,\n  methodId: 20,\n  name: \"ConnectionSecure\",\n  args: [ {\n    type: \"longstr\",\n    name: \"challenge\"\n  } ]\n};\n\nmodule.exports.ConnectionSecureOk = 655381;\n\nvar methodInfoConnectionSecureOk = module.exports.methodInfoConnectionSecureOk = {\n  id: 655381,\n  classId: 10,\n  methodId: 21,\n  name: \"ConnectionSecureOk\",\n  args: [ {\n    type: \"longstr\",\n    name: \"response\"\n  } ]\n};\n\nmodule.exports.ConnectionTune = 655390;\n\nvar methodInfoConnectionTune = module.exports.methodInfoConnectionTune = {\n  id: 655390,\n  classId: 10,\n  methodId: 30,\n  name: \"ConnectionTune\",\n  args: [ {\n    type: \"short\",\n    name: \"channelMax\",\n    default: 0\n  }, {\n    type: \"long\",\n    name: \"frameMax\",\n    default: 0\n  }, {\n    type: \"short\",\n    name: \"heartbeat\",\n    default: 0\n  } ]\n};\n\nmodule.exports.ConnectionTuneOk = 655391;\n\nvar methodInfoConnectionTuneOk = module.exports.methodInfoConnectionTuneOk = {\n  id: 655391,\n  classId: 10,\n  methodId: 31,\n  name: \"ConnectionTuneOk\",\n  args: [ {\n    type: \"short\",\n    name: \"channelMax\",\n    default: 0\n  }, {\n    type: \"long\",\n    name: \"frameMax\",\n    default: 0\n  }, {\n    type: \"short\",\n    name: \"heartbeat\",\n    default: 0\n  } ]\n};\n\nmodule.exports.ConnectionOpen = 655400;\n\nvar methodInfoConnectionOpen = module.exports.methodInfoConnectionOpen = {\n  id: 655400,\n  classId: 10,\n  methodId: 40,\n  name: \"ConnectionOpen\",\n  args: [ {\n    type: \"shortstr\",\n    name: \"virtualHost\",\n    default: \"/\"\n  }, {\n    type: \"shortstr\",\n    name: \"capabilities\",\n    default: \"\"\n  }, {\n    type: \"bit\",\n    name: \"insist\",\n    default: !1\n  } ]\n};\n\nmodule.exports.ConnectionOpenOk = 655401;\n\nvar methodInfoConnectionOpenOk = module.exports.methodInfoConnectionOpenOk = {\n  id: 655401,\n  classId: 10,\n  methodId: 41,\n  name: \"ConnectionOpenOk\",\n  args: [ {\n    type: \"shortstr\",\n    name: \"knownHosts\",\n    default: \"\"\n  } ]\n};\n\nmodule.exports.ConnectionClose = 655410;\n\nvar methodInfoConnectionClose = module.exports.methodInfoConnectionClose = {\n  id: 655410,\n  classId: 10,\n  methodId: 50,\n  name: \"ConnectionClose\",\n  args: [ {\n    type: \"short\",\n    name: \"replyCode\"\n  }, {\n    type: \"shortstr\",\n    name: \"replyText\",\n    default: \"\"\n  }, {\n    type: \"short\",\n    name: \"classId\"\n  }, {\n    type: \"short\",\n    name: \"methodId\"\n  } ]\n};\n\nmodule.exports.ConnectionCloseOk = 655411;\n\nvar methodInfoConnectionCloseOk = module.exports.methodInfoConnectionCloseOk = {\n  id: 655411,\n  classId: 10,\n  methodId: 51,\n  name: \"ConnectionCloseOk\",\n  args: []\n};\n\nmodule.exports.ConnectionBlocked = 655420;\n\nvar methodInfoConnectionBlocked = module.exports.methodInfoConnectionBlocked = {\n  id: 655420,\n  classId: 10,\n  methodId: 60,\n  name: \"ConnectionBlocked\",\n  args: [ {\n    type: \"shortstr\",\n    name: \"reason\",\n    default: \"\"\n  } ]\n};\n\nmodule.exports.ConnectionUnblocked = 655421;\n\nvar methodInfoConnectionUnblocked = module.exports.methodInfoConnectionUnblocked = {\n  id: 655421,\n  classId: 10,\n  methodId: 61,\n  name: \"ConnectionUnblocked\",\n  args: []\n};\n\nmodule.exports.ConnectionUpdateSecret = 655430;\n\nvar methodInfoConnectionUpdateSecret = module.exports.methodInfoConnectionUpdateSecret = {\n  id: 655430,\n  classId: 10,\n  methodId: 70,\n  name: \"ConnectionUpdateSecret\",\n  args: [ {\n    type: \"longstr\",\n    name: \"newSecret\"\n  }, {\n    type: \"shortstr\",\n    name: \"reason\"\n  } ]\n};\n\nmodule.exports.ConnectionUpdateSecretOk = 655431;\n\nvar methodInfoConnectionUpdateSecretOk = module.exports.methodInfoConnectionUpdateSecretOk = {\n  id: 655431,\n  classId: 10,\n  methodId: 71,\n  name: \"ConnectionUpdateSecretOk\",\n  args: []\n};\n\nmodule.exports.ChannelOpen = 1310730;\n\nvar methodInfoChannelOpen = module.exports.methodInfoChannelOpen = {\n  id: 1310730,\n  classId: 20,\n  methodId: 10,\n  name: \"ChannelOpen\",\n  args: [ {\n    type: \"shortstr\",\n    name: \"outOfBand\",\n    default: \"\"\n  } ]\n};\n\nmodule.exports.ChannelOpenOk = 1310731;\n\nvar methodInfoChannelOpenOk = module.exports.methodInfoChannelOpenOk = {\n  id: 1310731,\n  classId: 20,\n  methodId: 11,\n  name: \"ChannelOpenOk\",\n  args: [ {\n    type: \"longstr\",\n    name: \"channelId\",\n    default: \"\"\n  } ]\n};\n\nmodule.exports.ChannelFlow = 1310740;\n\nvar methodInfoChannelFlow = module.exports.methodInfoChannelFlow = {\n  id: 1310740,\n  classId: 20,\n  methodId: 20,\n  name: \"ChannelFlow\",\n  args: [ {\n    type: \"bit\",\n    name: \"active\"\n  } ]\n};\n\nmodule.exports.ChannelFlowOk = 1310741;\n\nvar methodInfoChannelFlowOk = module.exports.methodInfoChannelFlowOk = {\n  id: 1310741,\n  classId: 20,\n  methodId: 21,\n  name: \"ChannelFlowOk\",\n  args: [ {\n    type: \"bit\",\n    name: \"active\"\n  } ]\n};\n\nmodule.exports.ChannelClose = 1310760;\n\nvar methodInfoChannelClose = module.exports.methodInfoChannelClose = {\n  id: 1310760,\n  classId: 20,\n  methodId: 40,\n  name: \"ChannelClose\",\n  args: [ {\n    type: \"short\",\n    name: \"replyCode\"\n  }, {\n    type: \"shortstr\",\n    name: \"replyText\",\n    default: \"\"\n  }, {\n    type: \"short\",\n    name: \"classId\"\n  }, {\n    type: \"short\",\n    name: \"methodId\"\n  } ]\n};\n\nmodule.exports.ChannelCloseOk = 1310761;\n\nvar methodInfoChannelCloseOk = module.exports.methodInfoChannelCloseOk = {\n  id: 1310761,\n  classId: 20,\n  methodId: 41,\n  name: \"ChannelCloseOk\",\n  args: []\n};\n\nmodule.exports.AccessRequest = 1966090;\n\nvar methodInfoAccessRequest = module.exports.methodInfoAccessRequest = {\n  id: 1966090,\n  classId: 30,\n  methodId: 10,\n  name: \"AccessRequest\",\n  args: [ {\n    type: \"shortstr\",\n    name: \"realm\",\n    default: \"/data\"\n  }, {\n    type: \"bit\",\n    name: \"exclusive\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"passive\",\n    default: !0\n  }, {\n    type: \"bit\",\n    name: \"active\",\n    default: !0\n  }, {\n    type: \"bit\",\n    name: \"write\",\n    default: !0\n  }, {\n    type: \"bit\",\n    name: \"read\",\n    default: !0\n  } ]\n};\n\nmodule.exports.AccessRequestOk = 1966091;\n\nvar methodInfoAccessRequestOk = module.exports.methodInfoAccessRequestOk = {\n  id: 1966091,\n  classId: 30,\n  methodId: 11,\n  name: \"AccessRequestOk\",\n  args: [ {\n    type: \"short\",\n    name: \"ticket\",\n    default: 1\n  } ]\n};\n\nmodule.exports.ExchangeDeclare = 2621450;\n\nvar methodInfoExchangeDeclare = module.exports.methodInfoExchangeDeclare = {\n  id: 2621450,\n  classId: 40,\n  methodId: 10,\n  name: \"ExchangeDeclare\",\n  args: [ {\n    type: \"short\",\n    name: \"ticket\",\n    default: 0\n  }, {\n    type: \"shortstr\",\n    name: \"exchange\"\n  }, {\n    type: \"shortstr\",\n    name: \"type\",\n    default: \"direct\"\n  }, {\n    type: \"bit\",\n    name: \"passive\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"durable\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"autoDelete\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"internal\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"nowait\",\n    default: !1\n  }, {\n    type: \"table\",\n    name: \"arguments\",\n    default: {}\n  } ]\n};\n\nmodule.exports.ExchangeDeclareOk = 2621451;\n\nvar methodInfoExchangeDeclareOk = module.exports.methodInfoExchangeDeclareOk = {\n  id: 2621451,\n  classId: 40,\n  methodId: 11,\n  name: \"ExchangeDeclareOk\",\n  args: []\n};\n\nmodule.exports.ExchangeDelete = 2621460;\n\nvar methodInfoExchangeDelete = module.exports.methodInfoExchangeDelete = {\n  id: 2621460,\n  classId: 40,\n  methodId: 20,\n  name: \"ExchangeDelete\",\n  args: [ {\n    type: \"short\",\n    name: \"ticket\",\n    default: 0\n  }, {\n    type: \"shortstr\",\n    name: \"exchange\"\n  }, {\n    type: \"bit\",\n    name: \"ifUnused\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"nowait\",\n    default: !1\n  } ]\n};\n\nmodule.exports.ExchangeDeleteOk = 2621461;\n\nvar methodInfoExchangeDeleteOk = module.exports.methodInfoExchangeDeleteOk = {\n  id: 2621461,\n  classId: 40,\n  methodId: 21,\n  name: \"ExchangeDeleteOk\",\n  args: []\n};\n\nmodule.exports.ExchangeBind = 2621470;\n\nvar methodInfoExchangeBind = module.exports.methodInfoExchangeBind = {\n  id: 2621470,\n  classId: 40,\n  methodId: 30,\n  name: \"ExchangeBind\",\n  args: [ {\n    type: \"short\",\n    name: \"ticket\",\n    default: 0\n  }, {\n    type: \"shortstr\",\n    name: \"destination\"\n  }, {\n    type: \"shortstr\",\n    name: \"source\"\n  }, {\n    type: \"shortstr\",\n    name: \"routingKey\",\n    default: \"\"\n  }, {\n    type: \"bit\",\n    name: \"nowait\",\n    default: !1\n  }, {\n    type: \"table\",\n    name: \"arguments\",\n    default: {}\n  } ]\n};\n\nmodule.exports.ExchangeBindOk = 2621471;\n\nvar methodInfoExchangeBindOk = module.exports.methodInfoExchangeBindOk = {\n  id: 2621471,\n  classId: 40,\n  methodId: 31,\n  name: \"ExchangeBindOk\",\n  args: []\n};\n\nmodule.exports.ExchangeUnbind = 2621480;\n\nvar methodInfoExchangeUnbind = module.exports.methodInfoExchangeUnbind = {\n  id: 2621480,\n  classId: 40,\n  methodId: 40,\n  name: \"ExchangeUnbind\",\n  args: [ {\n    type: \"short\",\n    name: \"ticket\",\n    default: 0\n  }, {\n    type: \"shortstr\",\n    name: \"destination\"\n  }, {\n    type: \"shortstr\",\n    name: \"source\"\n  }, {\n    type: \"shortstr\",\n    name: \"routingKey\",\n    default: \"\"\n  }, {\n    type: \"bit\",\n    name: \"nowait\",\n    default: !1\n  }, {\n    type: \"table\",\n    name: \"arguments\",\n    default: {}\n  } ]\n};\n\nmodule.exports.ExchangeUnbindOk = 2621491;\n\nvar methodInfoExchangeUnbindOk = module.exports.methodInfoExchangeUnbindOk = {\n  id: 2621491,\n  classId: 40,\n  methodId: 51,\n  name: \"ExchangeUnbindOk\",\n  args: []\n};\n\nmodule.exports.QueueDeclare = 3276810;\n\nvar methodInfoQueueDeclare = module.exports.methodInfoQueueDeclare = {\n  id: 3276810,\n  classId: 50,\n  methodId: 10,\n  name: \"QueueDeclare\",\n  args: [ {\n    type: \"short\",\n    name: \"ticket\",\n    default: 0\n  }, {\n    type: \"shortstr\",\n    name: \"queue\",\n    default: \"\"\n  }, {\n    type: \"bit\",\n    name: \"passive\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"durable\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"exclusive\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"autoDelete\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"nowait\",\n    default: !1\n  }, {\n    type: \"table\",\n    name: \"arguments\",\n    default: {}\n  } ]\n};\n\nmodule.exports.QueueDeclareOk = 3276811;\n\nvar methodInfoQueueDeclareOk = module.exports.methodInfoQueueDeclareOk = {\n  id: 3276811,\n  classId: 50,\n  methodId: 11,\n  name: \"QueueDeclareOk\",\n  args: [ {\n    type: \"shortstr\",\n    name: \"queue\"\n  }, {\n    type: \"long\",\n    name: \"messageCount\"\n  }, {\n    type: \"long\",\n    name: \"consumerCount\"\n  } ]\n};\n\nmodule.exports.QueueBind = 3276820;\n\nvar methodInfoQueueBind = module.exports.methodInfoQueueBind = {\n  id: 3276820,\n  classId: 50,\n  methodId: 20,\n  name: \"QueueBind\",\n  args: [ {\n    type: \"short\",\n    name: \"ticket\",\n    default: 0\n  }, {\n    type: \"shortstr\",\n    name: \"queue\",\n    default: \"\"\n  }, {\n    type: \"shortstr\",\n    name: \"exchange\"\n  }, {\n    type: \"shortstr\",\n    name: \"routingKey\",\n    default: \"\"\n  }, {\n    type: \"bit\",\n    name: \"nowait\",\n    default: !1\n  }, {\n    type: \"table\",\n    name: \"arguments\",\n    default: {}\n  } ]\n};\n\nmodule.exports.QueueBindOk = 3276821;\n\nvar methodInfoQueueBindOk = module.exports.methodInfoQueueBindOk = {\n  id: 3276821,\n  classId: 50,\n  methodId: 21,\n  name: \"QueueBindOk\",\n  args: []\n};\n\nmodule.exports.QueuePurge = 3276830;\n\nvar methodInfoQueuePurge = module.exports.methodInfoQueuePurge = {\n  id: 3276830,\n  classId: 50,\n  methodId: 30,\n  name: \"QueuePurge\",\n  args: [ {\n    type: \"short\",\n    name: \"ticket\",\n    default: 0\n  }, {\n    type: \"shortstr\",\n    name: \"queue\",\n    default: \"\"\n  }, {\n    type: \"bit\",\n    name: \"nowait\",\n    default: !1\n  } ]\n};\n\nmodule.exports.QueuePurgeOk = 3276831;\n\nvar methodInfoQueuePurgeOk = module.exports.methodInfoQueuePurgeOk = {\n  id: 3276831,\n  classId: 50,\n  methodId: 31,\n  name: \"QueuePurgeOk\",\n  args: [ {\n    type: \"long\",\n    name: \"messageCount\"\n  } ]\n};\n\nmodule.exports.QueueDelete = 3276840;\n\nvar methodInfoQueueDelete = module.exports.methodInfoQueueDelete = {\n  id: 3276840,\n  classId: 50,\n  methodId: 40,\n  name: \"QueueDelete\",\n  args: [ {\n    type: \"short\",\n    name: \"ticket\",\n    default: 0\n  }, {\n    type: \"shortstr\",\n    name: \"queue\",\n    default: \"\"\n  }, {\n    type: \"bit\",\n    name: \"ifUnused\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"ifEmpty\",\n    default: !1\n  }, {\n    type: \"bit\",\n    name: \"nowait\",\n    default: !1\n  } ]\n};\n\nmodule.exports.QueueDeleteOk = 3276841;\n\nvar methodInfoQueueDeleteOk = module.exports.methodInfoQueueDeleteOk = {\n  id: 3276841,\n  classId: 50,\n  methodId: 41,\n  name: \"QueueDeleteOk\",\n  args: [ {\n    type: \"long\",\n    name: \"messageCount\"\n  } ]\n};\n\nmodule.exports.QueueUnbind = 3276850;\n\nvar methodInfoQueueUnbind = module.exports.methodInfoQueueUnbind = {\n  id: 3276850,\n  classId: 50,\n  methodId: 50,\n  name: \"QueueUnbind\",\n  args: [ {\n    type: \"short\",\n    name: \"ticket\",\n    default: 0\n  }, {\n    type: \"shortstr\",\n    name: \"queue\",\n    default: \"\"\n  }, {\n    type: \"shortstr\",\n    name: \"exchange\"\n  }, {\n    type: \"shortstr\",\n    name: \"routingKey\",\n    default: \"\"\n  }, {\n    type: \"table\",\n    name: \"arguments\",\n    default: {}\n  } ]\n};\n\nmodule.exports.QueueUnbindOk = 3276851;\n\nvar methodInfoQueueUnbindOk = module.exports.methodInfoQueueUnbindOk = {\n  id: 3276851,\n  classId: 50,\n  methodId: 51,\n  name: \"QueueUnbindOk\",\n  args: []\n};\n\nmodule.exports.TxSelect = 5898250;\n\nvar methodInfoTxSelect = module.exports.methodInfoTxSelect = {\n  id: 5898250,\n  classId: 90,\n  methodId: 10,\n  name: \"TxSelect\",\n  args: []\n};\n\nmodule.exports.TxSelectOk = 5898251;\n\nvar methodInfoTxSelectOk = module.exports.methodInfoTxSelectOk = {\n  id: 5898251,\n  classId: 90,\n  methodId: 11,\n  name: \"TxSelectOk\",\n  args: []\n};\n\nmodule.exports.TxCommit = 5898260;\n\nvar methodInfoTxCommit = module.exports.methodInfoTxCommit = {\n  id: 5898260,\n  classId: 90,\n  methodId: 20,\n  name: \"TxCommit\",\n  args: []\n};\n\nmodule.exports.TxCommitOk = 5898261;\n\nvar methodInfoTxCommitOk = module.exports.methodInfoTxCommitOk = {\n  id: 5898261,\n  classId: 90,\n  methodId: 21,\n  name: \"TxCommitOk\",\n  args: []\n};\n\nmodule.exports.TxRollback = 5898270;\n\nvar methodInfoTxRollback = module.exports.methodInfoTxRollback = {\n  id: 5898270,\n  classId: 90,\n  methodId: 30,\n  name: \"TxRollback\",\n  args: []\n};\n\nmodule.exports.TxRollbackOk = 5898271;\n\nvar methodInfoTxRollbackOk = module.exports.methodInfoTxRollbackOk = {\n  id: 5898271,\n  classId: 90,\n  methodId: 31,\n  name: \"TxRollbackOk\",\n  args: []\n};\n\nmodule.exports.ConfirmSelect = 5570570;\n\nvar methodInfoConfirmSelect = module.exports.methodInfoConfirmSelect = {\n  id: 5570570,\n  classId: 85,\n  methodId: 10,\n  name: \"ConfirmSelect\",\n  args: [ {\n    type: \"bit\",\n    name: \"nowait\",\n    default: !1\n  } ]\n};\n\nmodule.exports.ConfirmSelectOk = 5570571;\n\nvar methodInfoConfirmSelectOk = module.exports.methodInfoConfirmSelectOk = {\n  id: 5570571,\n  classId: 85,\n  methodId: 11,\n  name: \"ConfirmSelectOk\",\n  args: []\n};\n\nmodule.exports.BasicProperties = 60;\n\nvar propertiesInfoBasicProperties = module.exports.propertiesInfoBasicProperties = {\n  id: 60,\n  name: \"BasicProperties\",\n  args: [ {\n    type: \"shortstr\",\n    name: \"contentType\"\n  }, {\n    type: \"shortstr\",\n    name: \"contentEncoding\"\n  }, {\n    type: \"table\",\n    name: \"headers\"\n  }, {\n    type: \"octet\",\n    name: \"deliveryMode\"\n  }, {\n    type: \"octet\",\n    name: \"priority\"\n  }, {\n    type: \"shortstr\",\n    name: \"correlationId\"\n  }, {\n    type: \"shortstr\",\n    name: \"replyTo\"\n  }, {\n    type: \"shortstr\",\n    name: \"expiration\"\n  }, {\n    type: \"shortstr\",\n    name: \"messageId\"\n  }, {\n    type: \"timestamp\",\n    name: \"timestamp\"\n  }, {\n    type: \"shortstr\",\n    name: \"type\"\n  }, {\n    type: \"shortstr\",\n    name: \"userId\"\n  }, {\n    type: \"shortstr\",\n    name: \"appId\"\n  }, {\n    type: \"shortstr\",\n    name: \"clusterId\"\n  } ]\n};", "// The river sweeps through\n// Silt and twigs, gravel and leaves\n// Driving the wheel on\n\n'use strict';\n\nconst ints = require('buffer-more-ints')\nvar defs = require('./defs');\nvar constants = defs.constants;\nvar decode = defs.decode;\n\nmodule.exports.PROTOCOL_HEADER = \"AMQP\" + String.fromCharCode(0, 0, 9, 1);\n\n/*\n  Frame format:\n\n  0      1         3             7                size+7 size+8\n  +------+---------+-------------+ +------------+ +-----------+\n  | type | channel | size        | | payload    | | frame-end |\n  +------+---------+-------------+ +------------+ +-----------+\n  octet   short     long            size octets    octet\n\n  In general I want to know those first three things straight away, so I\n  can discard frames early.\n\n*/\n\n// framing constants\nvar FRAME_METHOD = constants.FRAME_METHOD,\nFRAME_HEARTBEAT = constants.FRAME_HEARTBEAT,\nFRAME_HEADER = constants.FRAME_HEADER,\nFRAME_BODY = constants.FRAME_BODY,\nFRAME_END = constants.FRAME_END;\n\n// expected byte sizes for frame parts\nconst TYPE_BYTES = 1\nconst CHANNEL_BYTES = 2\nconst SIZE_BYTES = 4\nconst FRAME_HEADER_BYTES = TYPE_BYTES + CHANNEL_BYTES + SIZE_BYTES\nconst FRAME_END_BYTES = 1\n\n/**\n * @typedef {{\n *   type: number,\n *   channel: number,\n *   size: number,\n *   payload: Buffer,\n *   rest: Buffer\n * }} FrameStructure\n */\n\n/**\n * This is a polyfill which will read a big int 64 bit as a number.\n * @arg { Buffer } buffer\n * @arg { number } offset\n * @returns { number }\n */\nfunction readInt64BE(buffer, offset) {\n  /**\n   * We try to use native implementation if available here because\n   * buffer-more-ints does not\n   */\n  if (typeof Buffer.prototype.readBigInt64BE === 'function') {\n    return Number(buffer.readBigInt64BE(offset))\n  }\n\n  return ints.readInt64BE(buffer, offset)\n}\n\n// %%% TESTME possibly better to cons the first bit and write the\n// second directly, in the absence of IO lists\n/**\n * Make a frame header\n * @arg { number } channel\n * @arg { Buffer } payload\n */\nmodule.exports.makeBodyFrame = function (channel, payload) {\n  const frameSize = FRAME_HEADER_BYTES + payload.length + FRAME_END_BYTES\n\n  const frame = Buffer.alloc(frameSize)\n\n  let offset = 0\n\n  offset = frame.writeUInt8(FRAME_BODY, offset)\n  offset = frame.writeUInt16BE(channel, offset)\n  offset = frame.writeInt32BE(payload.length, offset)\n\n  payload.copy(frame, offset)\n  offset += payload.length\n\n  frame.writeUInt8(FRAME_END, offset)\n\n  return frame\n};\n\n/**\n * Parse an AMQP frame\n * @arg { Buffer } bin\n * @arg { number } max\n * @returns { FrameStructure | boolean }\n */\nfunction parseFrame(bin) {\n  if (bin.length < FRAME_HEADER_BYTES) {\n    return false\n  }\n\n  const type = bin.readUInt8(0)\n  const channel = bin.readUInt16BE(1)\n  const size = bin.readUInt32BE(3)\n\n  const totalSize = FRAME_HEADER_BYTES + size + FRAME_END_BYTES\n\n  if (bin.length < totalSize) {\n    return false\n  }\n\n  const frameEnd = bin.readUInt8(FRAME_HEADER_BYTES + size)\n\n  if (frameEnd !== FRAME_END) {\n    throw new Error('Invalid frame')\n  }\n\n  return {\n    type,\n    channel,\n    size,\n    payload: bin.subarray(FRAME_HEADER_BYTES, FRAME_HEADER_BYTES + size),\n    rest: bin.subarray(totalSize)\n  }\n}\n\nmodule.exports.parseFrame = parseFrame;\n\nvar HEARTBEAT = {channel: 0};\n\n/**\n * Decode AMQP frame into JS object\n * @param { FrameStructure } frame\n * @returns\n */\nmodule.exports.decodeFrame = (frame) => {\n  const payload = frame.payload\n  const channel = frame.channel\n\n  switch (frame.type) {\n    case FRAME_METHOD: {\n      const id = payload.readUInt32BE(0)\n      const args = payload.subarray(4)\n      const fields = decode(id, args)\n      return { id, channel, fields }\n    }\n    case FRAME_HEADER: {\n      const id = payload.readUInt16BE(0)\n      // const weight = payload.readUInt16BE(2)\n      const size = readInt64BE(payload, 4)\n      const flagsAndfields = payload.subarray(12)\n      const fields = decode(id, flagsAndfields)\n      return { id, channel, size, fields }\n    }\n    case FRAME_BODY:\n      return { channel, content: payload }\n    case FRAME_HEARTBEAT:\n      return HEARTBEAT\n    default:\n      throw new Error('Unknown frame type ' + frame.type)\n  }\n}\n\n// encoded heartbeat\nmodule.exports.HEARTBEAT_BUF = Buffer.from([constants.FRAME_HEARTBEAT,\n                                           0, 0, 0, 0, // size = 0\n                                           0, 0, // channel = 0\n                                           constants.FRAME_END]);\n\nmodule.exports.HEARTBEAT = HEARTBEAT;", "//\n//\n//\n\n'use strict';\n\n// A Mux is an object into which other readable streams may be piped;\n// it then writes 'packets' from the upstreams to the given\n// downstream.\n\nvar assert = require('assert');\n\nvar schedule = (typeof setImmediate === 'function') ?\n  setImmediate : process.nextTick;\n\nclass Mux {\n  constructor (downstream) {\n    this.newStreams = [];\n    this.oldStreams = [];\n    this.blocked = false;\n    this.scheduledRead = false;\n\n    this.out = downstream;\n    var self = this;\n    downstream.on('drain', function () {\n      self.blocked = false;\n      self._readIncoming();\n    });\n  }\n\n  // There are 2 states we can be in:\n  // - waiting for outbound capacity, which will be signalled by a\n  // - 'drain' event on the downstream; or,\n  // - no packets to send, waiting for an inbound buffer to have\n  //   packets, which will be signalled by a 'readable' event\n  // If we write all packets available whenever there is outbound\n  // capacity, we will either run out of outbound capacity (`#write`\n  // returns false), or run out of packets (all calls to an\n  // `inbound.read()` have returned null).\n  _readIncoming () {\n\n    // We may be sent here speculatively, if an incoming stream has\n    // become readable\n    if (this.blocked) return;\n\n    var accepting = true;\n    var out = this.out;\n\n    // Try to read a chunk from each stream in turn, until all streams\n    // are empty, or we exhaust our ability to accept chunks.\n    function roundrobin (streams) {\n      var s;\n      while (accepting && (s = streams.shift())) {\n        var chunk = s.read();\n        if (chunk !== null) {\n          accepting = out.write(chunk);\n          streams.push(s);\n        }\n      }\n    }\n\n    roundrobin(this.newStreams);\n\n    // Either we exhausted the new queues, or we ran out of capacity. If\n    // we ran out of capacity, all the remaining new streams (i.e.,\n    // those with packets left) become old streams. This effectively\n    // prioritises streams that keep their buffers close to empty over\n    // those that are constantly near full.\n    if (accepting) { // all new queues are exhausted, write as many as\n      // we can from the old streams\n      assert.equal(0, this.newStreams.length);\n      roundrobin(this.oldStreams);\n    }\n    else { // ran out of room\n      assert(this.newStreams.length > 0, \"Expect some new streams to remain\");\n      Array.prototype.push.apply(this.oldStreams, this.newStreams);\n      this.newStreams = [];\n    }\n    // We may have exhausted all the old queues, or run out of room;\n    // either way, all we need to do is record whether we have capacity\n    // or not, so any speculative reads will know\n    this.blocked = !accepting;\n  }\n\n  _scheduleRead () {\n    var self = this;\n\n    if (!self.scheduledRead) {\n      schedule(function () {\n        self.scheduledRead = false;\n        self._readIncoming();\n      });\n      self.scheduledRead = true;\n    }\n  }\n\n  pipeFrom (readable) {\n    var self = this;\n\n    function enqueue () {\n      self.newStreams.push(readable);\n      self._scheduleRead();\n    }\n\n    function cleanup () {\n      readable.removeListener('readable', enqueue);\n      readable.removeListener('error', cleanup);\n      readable.removeListener('end', cleanup);\n      readable.removeListener('unpipeFrom', cleanupIfMe);\n    }\n    function cleanupIfMe (dest) {\n      if (dest === self) cleanup();\n    }\n\n    readable.on('unpipeFrom', cleanupIfMe);\n    readable.on('end', cleanup);\n    readable.on('error', cleanup);\n    readable.on('readable', enqueue);\n  }\n\n  unpipeFrom (readable) {\n    readable.emit('unpipeFrom', this);\n  }\n}\n\nmodule.exports.Mux = Mux;\n", "//\n//\n//\n\n// Heartbeats. In AMQP both clients and servers may expect a heartbeat\n// frame if there is no activity on the connection for a negotiated\n// period of time. If there's no activity for two such intervals, the\n// server or client is allowed to close the connection on the\n// presumption that the other party is dead.\n//\n// The client has two jobs here: the first is to send a heartbeat\n// frame if it's not sent any frames for a while, so that the server\n// doesn't think it's dead; the second is to check periodically that\n// it's seen activity from the server, and to advise if there doesn't\n// appear to have been any for over two intervals.\n//\n// Node.JS timers are a bit unreliable, in that they endeavour only to\n// fire at some indeterminate point *after* the given time (rather\n// gives the lie to 'realtime', dunnit). Because the scheduler is just\n// an event loop, it's quite easy to delay timers indefinitely by\n// reacting to some I/O with a lot of computation.\n//\n// To mitigate this I need a bit of creative interpretation:\n//\n//  - I'll schedule a server activity check for every `interval`, and\n//    check just how much time has passed. It will overshoot by at\n//    least a small margin; modulo missing timer deadlines, it'll\n//    notice between two and three intervals after activity actually\n//    stops (otherwise, at some point after two intervals).\n//\n//  - Every `interval / 2` I'll check that we've sent something since\n//    the last check, and if not, send a heartbeat frame. If we're\n//    really too busy to even run the check for two whole heartbeat\n//    intervals, there must be a lot of I (but not O, at least not on\n//    the connection), or computation, in which case perhaps it's best\n//    the server cuts us off anyway. Why `interval / 2`? Because the\n//    edge case is that the client sent a frame just after a\n//    heartbeat, which would mean I only send one after almost two\n//    intervals. (NB a heartbeat counts as a send, so it'll be checked\n//    at least twice before sending another)\n//\n// This design is based largely on RabbitMQ's heartbeating:\n// https://github.com/rabbitmq/rabbitmq-common/blob/master/src/rabbit_heartbeat.erl\n\n// %% Yes, I could apply the same 'actually passage of time' thing to\n// %% send as well as to recv.\n\n'use strict';\n\nvar EventEmitter = require('events');\n\n// Exported so that we can mess with it in tests\nmodule.exports.UNITS_TO_MS = 1000;\n\nclass Heart extends EventEmitter {\n  constructor (interval, checkSend, checkRecv) {\n    super();\n\n    this.interval = interval;\n\n    var intervalMs = interval * module.exports.UNITS_TO_MS;\n    // Function#bind is my new best friend\n    var beat = this.emit.bind(this, 'beat');\n    var timeout = this.emit.bind(this, 'timeout');\n\n    this.sendTimer = setInterval(\n      this.runHeartbeat.bind(this, checkSend, beat), intervalMs / 2);\n\n    // A timeout occurs if I see nothing for *two consecutive* intervals\n    var recvMissed = 0;\n    function missedTwo () {\n      if (!checkRecv())\n        return (++recvMissed < 2);\n      else { recvMissed = 0; return true; }\n    }\n    this.recvTimer = setInterval(\n      this.runHeartbeat.bind(this, missedTwo, timeout), intervalMs);\n  }\n\n  clear () {\n    clearInterval(this.sendTimer);\n    clearInterval(this.recvTimer);\n  }\n\n  runHeartbeat (check, fail) {\n    // Have we seen activity?\n    if (!check())\n      fail();\n  }\n}\n\nmodule.exports.Heart = Heart;\n", "//\n//\n//\n\n// Stringifying various things\n\n'use strict';\n\nvar defs = require('./defs');\nvar format = require('util').format;\nvar HEARTBEAT = require('./frame').HEARTBEAT;\n\nmodule.exports.closeMessage = function(close) {\n  var code = close.fields.replyCode;\n  return format('%d (%s) with message \"%s\"',\n                code, defs.constant_strs[code],\n                close.fields.replyText);\n}\n\nmodule.exports.methodName = function(id) {\n  return defs.info(id).name;\n};\n\nmodule.exports.inspect = function(frame, showFields) {\n  if (frame === HEARTBEAT) {\n    return '<Heartbeat>';\n  }\n  else if (!frame.id) {\n    return format('<Content channel:%d size:%d>',\n                  frame.channel, frame.size);\n  }\n  else {\n    var info = defs.info(frame.id);\n    return format('<%s channel:%d%s>', info.name, frame.channel,\n                  (showFields)\n                  ? ' ' + JSON.stringify(frame.fields, undefined, 2)\n                  : '');\n  }\n}\n", "//\n//\n//\n\n'use strict';\n\n/**\n * A bitset implementation, after that in java.util.  Yes there\n * already exist such things, but none implement next{Clear|Set}Bit or\n * equivalent, and none involved me tooling about for an evening.\n */\nclass BitSet {\n  /**\n   * @param {number} [size]\n   */\n  constructor(size) {\n    if (size) {\n      const numWords = Math.ceil(size / 32);\n      this.words = new Array(numWords);\n    }\n    else {\n      this.words = [];\n    }\n    this.wordsInUse = 0; // = number, not index\n  }\n\n  /**\n   * @param {number} numWords\n   */\n  ensureSize(numWords) {\n    const wordsPresent = this.words.length;\n    if (wordsPresent < numWords) {\n      this.words = this.words.concat(new Array(numWords - wordsPresent));\n    }\n  }\n\n  /**\n   * @param {number} bitIndex\n   */\n  set(bitIndex) {\n    const w = wordIndex(bitIndex);\n    if (w >= this.wordsInUse) {\n      this.ensureSize(w + 1);\n      this.wordsInUse = w + 1;\n    }\n    const bit = 1 << bitIndex;\n    this.words[w] |= bit;\n  }\n\n  /**\n   * @param {number} bitIndex\n   */\n  clear(bitIndex) {\n    const w = wordIndex(bitIndex);\n    if (w >= this.wordsInUse) return;\n    const mask = ~(1 << bitIndex);\n    this.words[w] &= mask;\n  }\n\n  /**\n   * @param {number} bitIndex\n   */\n  get(bitIndex) {\n    const w = wordIndex(bitIndex);\n    if (w >= this.wordsInUse) return false; // >= since index vs size\n    const bit = 1 << bitIndex;\n    return !!(this.words[w] & bit);\n  }\n\n  /**\n   * Give the next bit that is set on or after fromIndex, or -1 if no such bit\n   *\n   * @param {number} fromIndex\n   */\n  nextSetBit(fromIndex) {\n    let w = wordIndex(fromIndex);\n    if (w >= this.wordsInUse) return -1;\n\n    // the right-hand side is shifted to only test the bits of the first\n    // word that are > fromIndex\n    let word = this.words[w] & (0xffffffff << fromIndex);\n    while (true) {\n      if (word) return (w * 32) + trailingZeros(word);\n      w++;\n      if (w === this.wordsInUse) return -1;\n      word = this.words[w];\n    }\n  }\n\n  /**\n   * @param {number} fromIndex\n   */\n  nextClearBit(fromIndex) {\n    let w = wordIndex(fromIndex);\n    if (w >= this.wordsInUse) return fromIndex;\n\n    let word = ~(this.words[w]) & (0xffffffff << fromIndex);\n    while (true) {\n      if (word) return (w * 32) + trailingZeros(word);\n      w++;\n      if (w == this.wordsInUse) return w * 32;\n      word = ~(this.words[w]);\n    }\n  }\n}\n\n/**\n * @param {number} bitIndex\n */\nfunction wordIndex(bitIndex) {\n  return Math.floor(bitIndex / 32);\n}\n\n/**\n * @param {number} i\n */\nfunction trailingZeros(i) {\n  // From Hacker's Delight, via JDK. Probably far less effective here,\n  // since bit ops are not necessarily the quick way to do things in\n  // JS.\n  if (i === 0) return 32;\n  let y, n = 31;\n  y = i << 16; if (y != 0) { n = n -16; i = y; }\n  y = i << 8;  if (y != 0) { n = n - 8; i = y; }\n  y = i << 4;  if (y != 0) { n = n - 4; i = y; }\n  y = i << 2;  if (y != 0) { n = n - 2; i = y; }\n  return n - ((i << 1) >>> 31);\n}\n\nmodule.exports.BitSet = BitSet;\n", "var inherits = require('util').inherits;\n\nfunction trimStack(stack, num) {\n  return stack && stack.split('\\n').slice(num).join('\\n');\n}\n\nfunction IllegalOperationError(msg, stack) {\n  var tmp = new Error();\n  this.message = msg;\n  this.stack = this.toString() + '\\n' + trimStack(tmp.stack, 2);\n  this.stackAtStateChange = stack;\n}\ninherits(IllegalOperationError, Error);\n\nIllegalOperationError.prototype.name = 'IllegalOperationError';\n\nfunction stackCapture(reason) {\n  var e = new Error();\n  return 'Stack capture: ' + reason + '\\n' +\n    trimStack(e.stack, 2);\n}\n\nmodule.exports.IllegalOperationError = IllegalOperationError;\nmodule.exports.stackCapture = stackCapture;\n", "//\n//\n//\n\n'use strict';\n\nvar defs = require('./defs');\nvar constants = defs.constants;\nvar frame = require('./frame');\nvar HEARTBEAT = frame.HEARTBEAT;\nvar Mux = require('./mux').Mux;\n\nvar Duplex = require('stream').Duplex;\nvar EventEmitter = require('events');\nvar Heart = require('./heartbeat').Heart;\n\nvar methodName = require('./format').methodName;\nvar closeMsg = require('./format').closeMessage;\nvar inspect = require('./format').inspect;\n\nvar BitSet = require('./bitset').BitSet;\nvar fmt = require('util').format;\nvar PassThrough = require('stream').PassThrough;\nvar IllegalOperationError = require('./error').IllegalOperationError;\nvar stackCapture = require('./error').stackCapture;\n\n// High-water mark for channel write buffers, in 'objects' (which are\n// encoded frames as buffers).\nvar DEFAULT_WRITE_HWM = 1024;\n// If all the frames of a message (method, properties, content) total\n// to less than this, copy them into a single buffer and write it all\n// at once. Note that this is less than the minimum frame size: if it\n// was greater, we might have to fragment the content.\nvar SINGLE_CHUNK_THRESHOLD = 2048;\n\nclass Connection extends EventEmitter {\n  constructor (underlying) {\n    super();\n\n    var stream = this.stream = wrapStream(underlying);\n    this.muxer = new Mux(stream);\n\n    // frames\n    this.rest = Buffer.alloc(0);\n    this.frameMax = constants.FRAME_MIN_SIZE;\n    this.sentSinceLastCheck = false;\n    this.recvSinceLastCheck = false;\n\n    this.expectSocketClose = false;\n    this.freeChannels = new BitSet();\n    this.channels = [{\n      channel: { accept: channel0(this) },\n      buffer: underlying\n    }];\n  }\n\n  // This changed between versions, as did the codec, methods, etc. AMQP\n  // 0-9-1 is fairly similar to 0.8, but better, and nothing implements\n  // 0.8 that doesn't implement 0-9-1. In other words, it doesn't make\n  // much sense to generalise here.\n  sendProtocolHeader () {\n    this.sendBytes(frame.PROTOCOL_HEADER);\n  }\n\n  /*\n    The frighteningly complicated opening protocol (spec section 2.2.4):\n\n       Client -> Server\n\n         protocol header ->\n           <- start\n         start-ok ->\n       .. next two zero or more times ..\n           <- secure\n         secure-ok ->\n           <- tune\n         tune-ok ->\n         open ->\n           <- open-ok\n\n  If I'm only supporting SASL's PLAIN mechanism (which I am for the time\n  being), it gets a bit easier since the server won't in general send\n  back a `secure`, it'll just send `tune` after the `start-ok`.\n  (SASL PLAIN: http://tools.ietf.org/html/rfc4616)\n\n  */\n  open (allFields, openCallback0) {\n    var self = this;\n    var openCallback = openCallback0 || function () { };\n\n    // This is where we'll put our negotiated values\n    var tunedOptions = Object.create(allFields);\n\n    function wait (k) {\n      self.step(function (err, frame) {\n        if (err !== null)\n          bail(err);\n        else if (frame.channel !== 0) {\n          bail(new Error(\n            fmt(\"Frame on channel != 0 during handshake: %s\",\n              inspect(frame, false))));\n        }\n        else\n          k(frame);\n      });\n    }\n\n    function expect (Method, k) {\n      wait(function (frame) {\n        if (frame.id === Method)\n          k(frame);\n        else {\n          bail(new Error(\n            fmt(\"Expected %s; got %s\",\n              methodName(Method), inspect(frame, false))));\n        }\n      });\n    }\n\n    function bail (err) {\n      openCallback(err);\n    }\n\n    function send (Method) {\n      // This can throw an exception if there's some problem with the\n      // options; e.g., something is a string instead of a number.\n      self.sendMethod(0, Method, tunedOptions);\n    }\n\n    function negotiate (server, desired) {\n      // We get sent values for channelMax, frameMax and heartbeat,\n      // which we may accept or lower (subject to a minimum for\n      // frameMax, but we'll leave that to the server to enforce). In\n      // all cases, `0` really means \"no limit\", or rather the highest\n      // value in the encoding, e.g., unsigned short for channelMax.\n      if (server === 0 || desired === 0) {\n        // i.e., whichever places a limit, if either\n        return Math.max(server, desired);\n      }\n      else {\n        return Math.min(server, desired);\n      }\n    }\n\n    function onStart (start) {\n      var mechanisms = start.fields.mechanisms.toString().split(' ');\n      if (mechanisms.indexOf(allFields.mechanism) < 0) {\n        bail(new Error(fmt('SASL mechanism %s is not provided by the server',\n          allFields.mechanism)));\n        return;\n      }\n      self.serverProperties = start.fields.serverProperties;\n      try {\n        send(defs.ConnectionStartOk);\n      } catch (err) {\n        bail(err);\n        return;\n      }\n      wait(afterStartOk);\n    }\n\n    function afterStartOk (reply) {\n      switch (reply.id) {\n        case defs.ConnectionSecure:\n          bail(new Error(\n            \"Wasn't expecting to have to go through secure\"));\n          break;\n        case defs.ConnectionClose:\n          bail(new Error(fmt(\"Handshake terminated by server: %s\",\n            closeMsg(reply))));\n          break;\n        case defs.ConnectionTune:\n          var fields = reply.fields;\n          tunedOptions.frameMax =\n            negotiate(fields.frameMax, allFields.frameMax);\n          tunedOptions.channelMax =\n            negotiate(fields.channelMax, allFields.channelMax);\n          tunedOptions.heartbeat =\n            negotiate(fields.heartbeat, allFields.heartbeat);\n          try {\n            send(defs.ConnectionTuneOk);\n            send(defs.ConnectionOpen);\n          } catch (err) {\n            bail(err);\n            return;\n          }\n          expect(defs.ConnectionOpenOk, onOpenOk);\n          break;\n        default:\n          bail(new Error(\n            fmt(\"Expected connection.secure, connection.close, \" +\n              \"or connection.tune during handshake; got %s\",\n              inspect(reply, false))));\n          break;\n      }\n    }\n\n    function onOpenOk (openOk) {\n      // Impose the maximum of the encoded value, if the negotiated\n      // value is zero, meaning \"no, no limits\"\n      self.channelMax = tunedOptions.channelMax || 0xffff;\n      self.frameMax = tunedOptions.frameMax || 0xffffffff;\n      // 0 means \"no heartbeat\", rather than \"maximum period of\n      // heartbeating\"\n      self.heartbeat = tunedOptions.heartbeat;\n      self.heartbeater = self.startHeartbeater();\n      self.accept = mainAccept;\n      succeed(openOk);\n    }\n\n    // If the server closes the connection, it's probably because of\n    // something we did\n    function endWhileOpening (err) {\n      bail(err || new Error('Socket closed abruptly ' +\n        'during opening handshake'));\n    }\n\n    this.stream.on('end', endWhileOpening);\n    this.stream.on('error', endWhileOpening);\n\n    function succeed (ok) {\n      self.stream.removeListener('end', endWhileOpening);\n      self.stream.removeListener('error', endWhileOpening);\n      self.stream.on('error', self.onSocketError.bind(self));\n      self.stream.on('end', self.onSocketError.bind(\n        self, new Error('Unexpected close')));\n      self.on('frameError', self.onSocketError.bind(self));\n      self.acceptLoop();\n      openCallback(null, ok);\n    }\n\n    // Now kick off the handshake by prompting the server\n    this.sendProtocolHeader();\n    expect(defs.ConnectionStart, onStart);\n  }\n\n  // Closing things: AMQP has a closing handshake that applies to\n  // closing both connects and channels. As the initiating party, I send\n  // Close, then ignore all frames until I see either CloseOK --\n  // which signifies that the other party has seen the Close and shut\n  // the connection or channel down, so it's fine to free resources; or\n  // Close, which means the other party also wanted to close the\n  // whatever, and I should send CloseOk so it can free resources,\n  // then go back to waiting for the CloseOk. If I receive a Close\n  // out of the blue, I should throw away any unsent frames (they will\n  // be ignored anyway) and send CloseOk, then clean up resources. In\n  // general, Close out of the blue signals an error (or a forced\n  // closure, which may as well be an error).\n  //\n  //  RUNNING [1] --- send Close ---> Closing [2] ---> recv Close --+\n  //     |                               |                         [3]\n  //     |                               +------ send CloseOk ------+\n  //  recv Close                   recv CloseOk\n  //     |                               |\n  //     V                               V\n  //  Ended [4] ---- send CloseOk ---> Closed [5]\n  //\n  // [1] All frames accepted; getting a Close frame from the server\n  // moves to Ended; client may initiate a close by sending Close\n  // itself.\n  // [2] Client has initiated a close; only CloseOk or (simulataneously\n  // sent) Close is accepted.\n  // [3] Simultaneous close\n  // [4] Server won't send any more frames; accept no more frames, send\n  // CloseOk.\n  // [5] Fully closed, client will send no more, server will send no\n  // more. Signal 'close' or 'error'.\n  //\n  // There are two signalling mechanisms used in the API. The first is\n  // that calling `close` will return a promise, that will either\n  // resolve once the connection or channel is cleanly shut down, or\n  // will reject if the shutdown times out.\n  //\n  // The second is the 'close' and 'error' events. These are\n  // emitted as above. The events will fire *before* promises are\n  // resolved.\n  // Close the connection without even giving a reason. Typical.\n  close (closeCallback) {\n    var k = closeCallback && function () { closeCallback(null); };\n    this.closeBecause(\"Cheers, thanks\", constants.REPLY_SUCCESS, k);\n  }\n\n  // Close with a reason and a 'code'. I'm pretty sure RabbitMQ totally\n  // ignores these; maybe it logs them. The continuation will be invoked\n  // when the CloseOk has been received, and before the 'close' event.\n  closeBecause (reason, code, k) {\n    this.sendMethod(0, defs.ConnectionClose, {\n      replyText: reason,\n      replyCode: code,\n      methodId: 0, classId: 0\n    });\n    var s = stackCapture('closeBecause called: ' + reason);\n    this.toClosing(s, k);\n  }\n\n  closeWithError (reason, code, error) {\n    this.emit('error', error);\n    this.closeBecause(reason, code);\n  }\n\n  onSocketError (err) {\n    if (!this.expectSocketClose) {\n      // forestall any more calls to onSocketError, since we're signed\n      // up for `'error'` *and* `'end'`\n      this.expectSocketClose = true;\n      this.emit('error', err);\n      var s = stackCapture('Socket error');\n      this.toClosed(s, err);\n    }\n  }\n\n  // A close has been initiated. Repeat: a close has been initiated.\n  // This means we should not send more frames, anyway they will be\n  // ignored. We also have to shut down all the channels.\n  toClosing (capturedStack, k) {\n    var send = this.sendMethod.bind(this);\n\n    this.accept = function (f) {\n      if (f.id === defs.ConnectionCloseOk) {\n        if (k)\n          k();\n        var s = stackCapture('ConnectionCloseOk received');\n        this.toClosed(s, undefined);\n      }\n      else if (f.id === defs.ConnectionClose) {\n        send(0, defs.ConnectionCloseOk, {});\n      }\n      // else ignore frame\n    };\n    invalidateSend(this, 'Connection closing', capturedStack);\n  }\n\n  _closeChannels (capturedStack) {\n    for (var i = 1; i < this.channels.length; i++) {\n      var ch = this.channels[i];\n      if (ch !== null) {\n        ch.channel.toClosed(capturedStack); // %%% or with an error? not clear\n      }\n    }\n  }\n\n  // A close has been confirmed. Cease all communication.\n  toClosed (capturedStack, maybeErr) {\n    this._closeChannels(capturedStack);\n    var info = fmt('Connection closed (%s)',\n      (maybeErr) ? maybeErr.toString() : 'by client');\n    // Tidy up, invalidate enverything, dynamite the bridges.\n    invalidateSend(this, info, capturedStack);\n    this.accept = invalidOp(info, capturedStack);\n    this.close = function (cb) {\n      cb && cb(new IllegalOperationError(info, capturedStack));\n    };\n    if (this.heartbeater)\n      this.heartbeater.clear();\n    // This is certainly true now, if it wasn't before\n    this.expectSocketClose = true;\n    this.stream.end();\n    this.emit('close', maybeErr);\n  }\n\n  _updateSecret(newSecret, reason, cb) {\n    this.sendMethod(0, defs.ConnectionUpdateSecret, {\n      newSecret,\n      reason\n    });\n    this.once('update-secret-ok', cb);\n  }\n\n  // ===\n  startHeartbeater () {\n    if (this.heartbeat === 0)\n      return null;\n    else {\n      var self = this;\n      var hb = new Heart(this.heartbeat,\n        this.checkSend.bind(this),\n        this.checkRecv.bind(this));\n      hb.on('timeout', function () {\n        var hberr = new Error(\"Heartbeat timeout\");\n        self.emit('error', hberr);\n        var s = stackCapture('Heartbeat timeout');\n        self.toClosed(s, hberr);\n      });\n      hb.on('beat', function () {\n        self.sendHeartbeat();\n      });\n      return hb;\n    }\n  }\n\n  // I use an array to keep track of the channels, rather than an\n  // object. The channel identifiers are numbers, and allocated by the\n  // connection. If I try to allocate low numbers when they are\n  // available (which I do, by looking from the start of the bitset),\n  // this ought to keep the array small, and out of 'sparse array\n  // storage'. I also set entries to null, rather than deleting them, in\n  // the expectation that the next channel allocation will fill the slot\n  // again rather than growing the array. See\n  // http://www.html5rocks.com/en/tutorials/speed/v8/\n  freshChannel (channel, options) {\n    var next = this.freeChannels.nextClearBit(1);\n    if (next < 0 || next > this.channelMax)\n      throw new Error(\"No channels left to allocate\");\n    this.freeChannels.set(next);\n\n    var hwm = (options && options.highWaterMark) || DEFAULT_WRITE_HWM;\n    var writeBuffer = new PassThrough({\n      objectMode: true, highWaterMark: hwm\n    });\n    this.channels[next] = { channel: channel, buffer: writeBuffer };\n    writeBuffer.on('drain', function () {\n      channel.onBufferDrain();\n    });\n    this.muxer.pipeFrom(writeBuffer);\n    return next;\n  }\n\n  releaseChannel (channel) {\n    this.freeChannels.clear(channel);\n    var buffer = this.channels[channel].buffer;\n    buffer.end(); // will also cause it to be unpiped\n    this.channels[channel] = null;\n  }\n\n  acceptLoop () {\n    var self = this;\n\n    function go () {\n      try {\n        var f; while (f = self.recvFrame())\n          self.accept(f);\n      }\n      catch (e) {\n        self.emit('frameError', e);\n      }\n    }\n    self.stream.on('readable', go);\n    go();\n  }\n\n  step (cb) {\n    var self = this;\n    function recv () {\n      var f;\n      try {\n        f = self.recvFrame();\n      }\n      catch (e) {\n        cb(e, null);\n        return;\n      }\n      if (f)\n        cb(null, f);\n      else\n        self.stream.once('readable', recv);\n    }\n    recv();\n  }\n\n  checkSend () {\n    var check = this.sentSinceLastCheck;\n    this.sentSinceLastCheck = false;\n    return check;\n  }\n\n  checkRecv () {\n    var check = this.recvSinceLastCheck;\n    this.recvSinceLastCheck = false;\n    return check;\n  }\n\n  sendBytes (bytes) {\n    this.sentSinceLastCheck = true;\n    this.stream.write(bytes);\n  }\n\n  sendHeartbeat () {\n    return this.sendBytes(frame.HEARTBEAT_BUF);\n  }\n\n  sendMethod (channel, Method, fields) {\n    var frame = encodeMethod(Method, channel, fields);\n    this.sentSinceLastCheck = true;\n    var buffer = this.channels[channel].buffer;\n    return buffer.write(frame);\n  }\n\n  sendMessage (channel, Method, fields, Properties, props, content) {\n    if (!Buffer.isBuffer(content))\n      throw new TypeError('content is not a buffer');\n\n    var mframe = encodeMethod(Method, channel, fields);\n    var pframe = encodeProperties(Properties, channel,\n      content.length, props);\n    var buffer = this.channels[channel].buffer;\n    this.sentSinceLastCheck = true;\n\n    var methodHeaderLen = mframe.length + pframe.length;\n    var bodyLen = (content.length > 0) ?\n      content.length + FRAME_OVERHEAD : 0;\n    var allLen = methodHeaderLen + bodyLen;\n\n    if (allLen < SINGLE_CHUNK_THRESHOLD) {\n      // Use `allocUnsafe` to avoid excessive allocations and CPU usage\n      // from zeroing. The returned Buffer is not zeroed and so must be\n      // completely filled to be used safely.\n      // See https://github.com/amqp-node/amqplib/pull/695\n      var all = Buffer.allocUnsafe(allLen);\n      var offset = mframe.copy(all, 0);\n      offset += pframe.copy(all, offset);\n\n      if (bodyLen > 0)\n        makeBodyFrame(channel, content).copy(all, offset);\n      return buffer.write(all);\n    }\n    else {\n      if (methodHeaderLen < SINGLE_CHUNK_THRESHOLD) {\n        // Use `allocUnsafe` to avoid excessive allocations and CPU usage\n        // from zeroing. The returned Buffer is not zeroed and so must be\n        // completely filled to be used safely.\n        // See https://github.com/amqp-node/amqplib/pull/695\n        var both = Buffer.allocUnsafe(methodHeaderLen);\n        var offset = mframe.copy(both, 0);\n        pframe.copy(both, offset);\n        buffer.write(both);\n      }\n      else {\n        buffer.write(mframe);\n        buffer.write(pframe);\n      }\n      return this.sendContent(channel, content);\n    }\n  }\n\n  sendContent (channel, body) {\n    if (!Buffer.isBuffer(body)) {\n      throw new TypeError(fmt(\"Expected buffer; got %s\", body));\n    }\n    var writeResult = true;\n    var buffer = this.channels[channel].buffer;\n\n    var maxBody = this.frameMax - FRAME_OVERHEAD;\n\n    for (var offset = 0; offset < body.length; offset += maxBody) {\n      var end = offset + maxBody;\n      var slice = (end > body.length) ? body.subarray(offset) : body.subarray(offset, end);\n      var bodyFrame = makeBodyFrame(channel, slice);\n      writeResult = buffer.write(bodyFrame);\n    }\n    this.sentSinceLastCheck = true;\n    return writeResult;\n  }\n\n  recvFrame () {\n    // %%% identifying invariants might help here?\n    var frame = parseFrame(this.rest);\n\n    if (!frame) {\n      var incoming = this.stream.read();\n      if (incoming === null) {\n        return false;\n      }\n      else {\n        this.recvSinceLastCheck = true;\n        this.rest = Buffer.concat([this.rest, incoming]);\n        return this.recvFrame();\n      }\n    }\n    else {\n      this.rest = frame.rest;\n      return decodeFrame(frame);\n    }\n  }\n}\n\n// Usual frame accept mode\nfunction mainAccept(frame) {\n  var rec = this.channels[frame.channel];\n  if (rec) { return rec.channel.accept(frame); }\n  // NB CHANNEL_ERROR may not be right, but I don't know what is ..\n  else\n    this.closeWithError(\n      fmt('Frame on unknown channel %d', frame.channel),\n      constants.CHANNEL_ERROR,\n      new Error(fmt(\"Frame on unknown channel: %s\",\n                    inspect(frame, false))));\n}\n\n// Handle anything that comes through on channel 0, that's the\n// connection control channel. This is only used once mainAccept is\n// installed as the frame handler, after the opening handshake.\nfunction channel0(connection) {\n  return function(f) {\n    // Once we get a 'close', we know 1. we'll get no more frames, and\n    // 2. anything we send except close, or close-ok, will be\n    // ignored. If we already sent 'close', this won't be invoked since\n    // we're already in closing mode; if we didn't well we're not going\n    // to send it now are we.\n    if (f === HEARTBEAT); // ignore; it's already counted as activity\n                          // on the socket, which is its purpose\n    else if (f.id === defs.ConnectionClose) {\n      // Oh. OK. I guess we're done here then.\n      connection.sendMethod(0, defs.ConnectionCloseOk, {});\n      var emsg = fmt('Connection closed: %s', closeMsg(f));\n      var s = stackCapture(emsg);\n      var e = new Error(emsg);\n      e.code = f.fields.replyCode;\n      if (isFatalError(e)) {\n        connection.emit('error', e);\n      }\n      connection.toClosed(s, e);\n    }\n    else if (f.id === defs.ConnectionBlocked) {\n      connection.emit('blocked', f.fields.reason);\n    }\n    else if (f.id === defs.ConnectionUnblocked) {\n      connection.emit('unblocked');\n    }\n    else if (f.id === defs.ConnectionUpdateSecretOk) {\n      connection.emit('update-secret-ok');\n    }\n    else {\n      connection.closeWithError(\n        fmt(\"Unexpected frame on channel 0\"),\n        constants.UNEXPECTED_FRAME,\n        new Error(fmt(\"Unexpected frame on channel 0: %s\",\n                      inspect(f, false))));\n    }\n  };\n}\n\nfunction invalidOp(msg, stack) {\n  return function() {\n    throw new IllegalOperationError(msg, stack);\n  };\n}\n\nfunction invalidateSend(conn, msg, stack) {\n  conn.sendMethod = conn.sendContent = conn.sendMessage =\n    invalidOp(msg, stack);\n}\n\nvar encodeMethod = defs.encodeMethod;\nvar encodeProperties = defs.encodeProperties;\n\nvar FRAME_OVERHEAD = defs.FRAME_OVERHEAD;\nvar makeBodyFrame = frame.makeBodyFrame;\n\nvar parseFrame = frame.parseFrame;\nvar decodeFrame = frame.decodeFrame;\n\nfunction wrapStream(s) {\n  if (s instanceof Duplex) return s;\n  else {\n    var ws = new Duplex();\n    ws.wrap(s); //wraps the readable side of things\n    ws._write = function(chunk, encoding, callback) {\n      return s.write(chunk, encoding, callback);\n    };\n    return ws;\n  }\n}\n\nfunction isFatalError(error) {\n  switch (error && error.code) {\n  case defs.constants.CONNECTION_FORCED:\n  case defs.constants.REPLY_SUCCESS:\n    return false;\n  default:\n    return true;\n  }\n}\n\nmodule.exports.Connection = Connection;\nmodule.exports.isFatalError = isFatalError;\n", "//\n//\n//\n\n// Different kind of credentials that can be supplied when opening a\n// connection, corresponding to SASL mechanisms There's only two\n// useful mechanisms that RabbitMQ implements:\n//  * PLAIN (send username and password in the plain)\n//  * EXTERNAL (assume the server will figure out who you are from\n//    context, i.e., your SSL certificate)\nvar codec = require('./codec')\n\nmodule.exports.plain = function(user, passwd) {\n  return {\n    mechanism: 'PLAIN',\n    response: function() {\n      return Buffer.from(['', user, passwd].join(String.fromCharCode(0)))\n    },\n    username: user,\n    password: passwd\n  }\n}\n\nmodule.exports.amqplain = function(user, passwd) {\n  return {\n    mechanism: 'AMQPLAIN',\n    response: function() {\n      const buffer = Buffer.alloc(16384);\n      const size = codec.encodeTable(buffer, { LOGIN: user, PASSWORD: passwd}, 0);\n      return buffer.subarray(4, size);\n    },\n    username: user,\n    password: passwd\n  }\n}\n\nmodule.exports.external = function() {\n  return {\n    mechanism: 'EXTERNAL',\n    response: function() { return Buffer.from(''); }\n  }\n}\n", "//\n//\n//\n\n// General-purpose API for glueing everything together.\n\n'use strict';\n\nvar URL = require('url-parse');\nvar QS = require('querystring');\nvar Connection = require('./connection').Connection;\nvar fmt = require('util').format;\nvar credentials = require('./credentials');\n\nfunction copyInto(obj, target) {\n  var keys = Object.keys(obj);\n  var i = keys.length;\n  while (i--) {\n    var k = keys[i];\n    target[k] = obj[k];\n  }\n  return target;\n}\n\n// Adapted from util._extend, which is too fringe to use.\nfunction clone(obj) {\n  return copyInto(obj, {});\n}\n\nvar CLIENT_PROPERTIES = {\n  \"product\": \"amqplib\",\n  \"version\": require('../package.json').version,\n  \"platform\": fmt('Node.JS %s', process.version),\n  \"information\": \"https://amqp-node.github.io/amqplib/\",\n  \"capabilities\": {\n    \"publisher_confirms\": true,\n    \"exchange_exchange_bindings\": true,\n    \"basic.nack\": true,\n    \"consumer_cancel_notify\": true,\n    \"connection.blocked\": true,\n    \"authentication_failure_close\": true\n  }\n};\n\n// Construct the main frames used in the opening handshake\nfunction openFrames(vhost, query, credentials, extraClientProperties) {\n  if (!vhost)\n    vhost = '/';\n  else\n    vhost = QS.unescape(vhost);\n\n  var query = query || {};\n\n  function intOrDefault(val, def) {\n    return (val === undefined) ? def : parseInt(val);\n  }\n\n  var clientProperties = Object.create(CLIENT_PROPERTIES);\n\n  return {\n    // start-ok\n    'clientProperties': copyInto(extraClientProperties, clientProperties),\n    'mechanism': credentials.mechanism,\n    'response': credentials.response(),\n    'locale': query.locale || 'en_US',\n\n    // tune-ok\n    'channelMax': intOrDefault(query.channelMax, 0),\n    'frameMax': intOrDefault(query.frameMax, 131072),\n    'heartbeat': intOrDefault(query.heartbeat, 0),\n\n    // open\n    'virtualHost': vhost,\n    'capabilities': '',\n    'insist': 0\n  };\n}\n\n// Decide on credentials based on what we're supplied.\nfunction credentialsFromUrl(parts) {\n  var user = 'guest', passwd = 'guest';\n  if (parts.username != '' || parts.password != '') {\n    user = (parts.username) ? unescape(parts.username) : '';\n    passwd = (parts.password) ? unescape(parts.password) : '';\n  }\n  return credentials.plain(user, passwd);\n}\n\nfunction connect(url, socketOptions, openCallback) {\n  // tls.connect uses `util._extend()` on the options given it, which\n  // copies only properties mentioned in `Object.keys()`, when\n  // processing the options. So I have to make copies too, rather\n  // than using `Object.create()`.\n  var sockopts = clone(socketOptions || {});\n  url = url || 'amqp://localhost';\n\n  var noDelay = !!sockopts.noDelay;\n  var timeout = sockopts.timeout;\n  var keepAlive = !!sockopts.keepAlive;\n  // 0 is default for node\n  var keepAliveDelay = sockopts.keepAliveDelay || 0;\n\n  var extraClientProperties = sockopts.clientProperties || {};\n\n  var protocol, fields;\n  if (typeof url === 'object') {\n    protocol = (url.protocol || 'amqp') + ':';\n    sockopts.host = url.hostname;\n    sockopts.servername = sockopts.servername || url.hostname;\n    sockopts.port = url.port || ((protocol === 'amqp:') ? 5672 : 5671);\n\n    var user, pass;\n    // Only default if both are missing, to have the same behaviour as\n    // the stringly URL.\n    if (url.username == undefined && url.password == undefined) {\n      user = 'guest'; pass = 'guest';\n    } else {\n      user = url.username || '';\n      pass = url.password || '';\n    }\n\n    var config = {\n      locale: url.locale,\n      channelMax: url.channelMax,\n      frameMax: url.frameMax,\n      heartbeat: url.heartbeat,\n    };\n\n    fields = openFrames(url.vhost, config, sockopts.credentials || credentials.plain(user, pass), extraClientProperties);\n  } else {\n    var parts = URL(url, true); // yes, parse the query string\n    protocol = parts.protocol;\n    sockopts.host = parts.hostname;\n    sockopts.servername = sockopts.servername || parts.hostname;\n    sockopts.port = parseInt(parts.port) || ((protocol === 'amqp:') ? 5672 : 5671);\n    var vhost = parts.pathname ? parts.pathname.substr(1) : null;\n    fields = openFrames(vhost, parts.query, sockopts.credentials || credentialsFromUrl(parts), extraClientProperties);\n  }\n\n  var sockok = false;\n  var sock;\n\n  function onConnect() {\n    sockok = true;\n    sock.setNoDelay(noDelay);\n    if (keepAlive) sock.setKeepAlive(keepAlive, keepAliveDelay);\n\n    var c = new Connection(sock);\n    c.open(fields, function(err, ok) {\n      // disable timeout once the connection is open, we don't want\n      // it fouling things\n      if (timeout) sock.setTimeout(0);\n      if (err === null) {\n        openCallback(null, c);\n      } else {\n        // The connection isn't closed by the server on e.g. wrong password\n        sock.end();\n        sock.destroy();\n        openCallback(err);\n      }\n    });\n  }\n\n  if (protocol === 'amqp:') {\n    sock = require('net').connect(sockopts, onConnect);\n  }\n  else if (protocol === 'amqps:') {\n    sock = require('tls').connect(sockopts, onConnect);\n  }\n  else {\n    throw new Error(\"Expected amqp: or amqps: as the protocol; got \" + protocol);\n  }\n\n  if (timeout) {\n    sock.setTimeout(timeout, function() {\n      sock.end();\n      sock.destroy();\n      openCallback(new Error('connect ETIMEDOUT'));\n    });\n  }\n\n  sock.once('error', function(err) {\n    if (!sockok) openCallback(err);\n  });\n\n}\n\nmodule.exports.connect = connect;\nmodule.exports.credentialsFromUrl = credentialsFromUrl;\n", "//\n//\n//\n\n// Channel machinery.\n\n'use strict';\n\nvar defs = require('./defs');\nvar closeMsg = require('./format').closeMessage;\nvar inspect = require('./format').inspect;\nvar methodName = require('./format').methodName;\nvar assert = require('assert');\nvar EventEmitter = require('events');\nvar fmt = require('util').format;\nvar IllegalOperationError = require('./error').IllegalOperationError;\nvar stackCapture = require('./error').stackCapture;\n\nclass Channel extends EventEmitter {\n  constructor (connection) {\n    super();\n\n    this.connection = connection;\n    // for the presently outstanding RPC\n    this.reply = null;\n    // for the RPCs awaiting action\n    this.pending = [];\n    // for unconfirmed messages\n    this.lwm = 1; // the least, unconfirmed deliveryTag\n    this.unconfirmed = []; // rolling window of delivery callbacks\n    this.on('ack', this.handleConfirm.bind(this, function (cb) {\n      if (cb)\n        cb(null);\n    }));\n    this.on('nack', this.handleConfirm.bind(this, function (cb) {\n      if (cb)\n        cb(new Error('message nacked'));\n    }));\n    this.on('close', function () {\n      var cb;\n      while (cb = this.unconfirmed.shift()) {\n        if (cb)\n          cb(new Error('channel closed'));\n      }\n    });\n    // message frame state machine\n    this.handleMessage = acceptDeliveryOrReturn;\n  }\n\n  setOptions(options) {\n    this.options = options;\n  }\n\n  allocate () {\n    this.ch = this.connection.freshChannel(this, this.options);\n    return this;\n  }\n\n  // Incoming frames are either notifications of e.g., message delivery,\n  // or replies to something we've sent. In general I deal with the\n  // former by emitting an event, and with the latter by keeping a track\n  // of what's expecting a reply.\n  //\n  // The AMQP specification implies that RPCs can't be pipelined; that\n  // is, you can have only one outstanding RPC on a channel at a\n  // time. Certainly that's what RabbitMQ and its clients assume. For\n  // this reason, I buffer RPCs if the channel is already waiting for a\n  // reply.\n  // Just send the damn frame.\n  sendImmediately (method, fields) {\n    return this.connection.sendMethod(this.ch, method, fields);\n  }\n\n  // Invariant: !this.reply -> pending.length == 0. That is, whenever we\n  // clear a reply, we must send another RPC (and thereby fill\n  // this.reply) if there is one waiting. The invariant relevant here\n  // and in `accept`.\n  sendOrEnqueue (method, fields, reply) {\n    if (!this.reply) { // if no reply waiting, we can go\n      assert(this.pending.length === 0);\n      this.reply = reply;\n      this.sendImmediately(method, fields);\n    }\n    else {\n      this.pending.push({\n        method: method,\n        fields: fields,\n        reply: reply\n      });\n    }\n  }\n\n  sendMessage (fields, properties, content) {\n    return this.connection.sendMessage(\n      this.ch,\n      defs.BasicPublish, fields,\n      defs.BasicProperties, properties,\n      content);\n  }\n\n  // Internal, synchronously resolved RPC; the return value is resolved\n  // with the whole frame.\n  _rpc (method, fields, expect, cb) {\n    var self = this;\n\n    function reply (err, f) {\n      if (err === null) {\n        if (f.id === expect) {\n          return cb(null, f);\n        }\n        else {\n          // We have detected a problem, so it's up to us to close the\n          // channel\n          var expectedName = methodName(expect);\n\n          var e = new Error(fmt(\"Expected %s; got %s\",\n            expectedName, inspect(f, false)));\n          self.closeWithError(f.id, fmt('Expected %s; got %s',\n            expectedName, methodName(f.id)),\n            defs.constants.UNEXPECTED_FRAME, e);\n          return cb(e);\n        }\n      }\n\n\n      // An error will be given if, for example, this is waiting to be\n      // sent and the connection closes\n      else if (err instanceof Error)\n        return cb(err);\n\n\n      // A close frame will be given if this is the RPC awaiting reply\n      // and the channel is closed by the server\n      else {\n        // otherwise, it's a close frame\n        var closeReason = (err.fields.classId << 16) + err.fields.methodId;\n        var e = (method === closeReason)\n          ? fmt(\"Operation failed: %s; %s\",\n            methodName(method), closeMsg(err))\n          : fmt(\"Channel closed by server: %s\", closeMsg(err));\n        var closeFrameError = new Error(e);\n        closeFrameError.code = err.fields.replyCode;\n        closeFrameError.classId = err.fields.classId;\n        closeFrameError.methodId = err.fields.methodId;\n        return cb(closeFrameError);\n      }\n    }\n\n    this.sendOrEnqueue(method, fields, reply);\n  }\n\n  // Move to entirely closed state.\n  toClosed (capturedStack) {\n    this._rejectPending();\n    invalidateSend(this, 'Channel closed', capturedStack);\n    this.accept = invalidOp('Channel closed', capturedStack);\n    this.connection.releaseChannel(this.ch);\n    this.emit('close');\n  }\n\n  // Stop being able to send and receive methods and content. Used when\n  // we close the channel. Invokes the continuation once the server has\n  // acknowledged the close, but before the channel is moved to the\n  // closed state.\n  toClosing (capturedStack, k) {\n    var send = this.sendImmediately.bind(this);\n    invalidateSend(this, 'Channel closing', capturedStack);\n\n    this.accept = function (f) {\n      if (f.id === defs.ChannelCloseOk) {\n        if (k)\n          k();\n        var s = stackCapture('ChannelCloseOk frame received');\n        this.toClosed(s);\n      }\n      else if (f.id === defs.ChannelClose) {\n        send(defs.ChannelCloseOk, {});\n      }\n      // else ignore frame\n    };\n  }\n\n  _rejectPending () {\n    function rej (r) {\n      r(new Error(\"Channel ended, no reply will be forthcoming\"));\n    }\n    if (this.reply !== null)\n      rej(this.reply);\n    this.reply = null;\n\n    var discard;\n    while (discard = this.pending.shift())\n      rej(discard.reply);\n    this.pending = null; // so pushes will break\n  }\n\n  closeBecause (reason, code, k) {\n    this.sendImmediately(defs.ChannelClose, {\n      replyText: reason,\n      replyCode: code,\n      methodId: 0, classId: 0\n    });\n    var s = stackCapture('closeBecause called: ' + reason);\n    this.toClosing(s, k);\n  }\n\n  // If we close because there's been an error, we need to distinguish\n  // between what we tell the server (`reason`) and what we report as\n  // the cause in the client (`error`).\n  closeWithError (id, reason, code, error) {\n    var self = this;\n    this.closeBecause(reason, code, function () {\n      error.code = code;\n      // content frames and consumer errors do not provide a method a class/method ID\n      if (id) {\n        error.classId = defs.info(id).classId;\n        error.methodId = defs.info(id).methodId;\n      }\n      self.emit('error', error);\n    });\n  }\n\n  // A trampolining state machine for message frames on a channel. A\n  // message arrives in at least two frames: first, a method announcing\n  // the message (either a BasicDeliver or BasicGetOk); then, a message\n  // header with the message properties; then, zero or more content\n  // frames.\n  // Keep the try/catch localised, in an attempt to avoid disabling\n  // optimisation\n  acceptMessageFrame (f) {\n    try {\n      this.handleMessage = this.handleMessage(f);\n    }\n    catch (msg) {\n      if (typeof msg === 'string') {\n        this.closeWithError(f.id, msg, defs.constants.UNEXPECTED_FRAME,\n          new Error(msg));\n      }\n      else if (msg instanceof Error) {\n        this.closeWithError(f.id, 'Error while processing message',\n          defs.constants.INTERNAL_ERROR, msg);\n      }\n      else {\n        this.closeWithError(f.id, 'Internal error while processing message',\n          defs.constants.INTERNAL_ERROR,\n          new Error(msg.toString()));\n      }\n    }\n  }\n\n  handleConfirm (handle, f) {\n    var tag = f.deliveryTag;\n    var multi = f.multiple;\n\n    if (multi) {\n      var confirmed = this.unconfirmed.splice(0, tag - this.lwm + 1);\n      this.lwm = tag + 1;\n      confirmed.forEach(handle);\n    }\n    else {\n      var c;\n      if (tag === this.lwm) {\n        c = this.unconfirmed.shift();\n        this.lwm++;\n        // Advance the LWM and the window to the next non-gap, or\n        // possibly to the end\n        while (this.unconfirmed[0] === null) {\n          this.unconfirmed.shift();\n          this.lwm++;\n        }\n      }\n      else {\n        c = this.unconfirmed[tag - this.lwm];\n        this.unconfirmed[tag - this.lwm] = null;\n      }\n      // Technically, in the single-deliveryTag case, I should report a\n      // protocol breach if it's already been confirmed.\n      handle(c);\n    }\n  }\n\n  pushConfirmCallback (cb) {\n    // `null` is used specifically for marking already confirmed slots,\n    // so I coerce `undefined` and `null` to false; functions are never\n    // falsey.\n    this.unconfirmed.push(cb || false);\n  }\n\n  onBufferDrain () {\n    this.emit('drain');\n  }\n\n  accept(f) {\n\n    switch (f.id) {\n\n      // Message frames\n    case undefined: // content frame!\n    case defs.BasicDeliver:\n    case defs.BasicReturn:\n    case defs.BasicProperties:\n      return this.acceptMessageFrame(f);\n\n      // confirmations, need to do confirm.select first\n    case defs.BasicAck:\n      return this.emit('ack', f.fields);\n    case defs.BasicNack:\n      return this.emit('nack', f.fields);\n    case defs.BasicCancel:\n      // The broker can send this if e.g., the queue is deleted.\n      return this.emit('cancel', f.fields);\n\n    case defs.ChannelClose:\n      // Any remote closure is an error to us. Reject the pending reply\n      // with the close frame, so it can see whether it was that\n      // operation that caused it to close.\n      if (this.reply) {\n        var reply = this.reply; this.reply = null;\n        reply(f);\n      }\n      var emsg = \"Channel closed by server: \" + closeMsg(f);\n      this.sendImmediately(defs.ChannelCloseOk, {});\n\n      var error = new Error(emsg);\n      error.code = f.fields.replyCode;\n      error.classId = f.fields.classId;\n      error.methodId = f.fields.methodId;\n      this.emit('error', error);\n\n      var s = stackCapture(emsg);\n      this.toClosed(s);\n      return;\n\n    case defs.BasicFlow:\n      // RabbitMQ doesn't send this, it just blocks the TCP socket\n      return this.closeWithError(f.id, \"Flow not implemented\",\n                                 defs.constants.NOT_IMPLEMENTED,\n                                 new Error('Flow not implemented'));\n\n    default: // assume all other things are replies\n      // Resolving the reply may lead to another RPC; to make sure we\n      // don't hold that up, clear this.reply\n      var reply = this.reply; this.reply = null;\n      // however, maybe there's an RPC waiting to go? If so, that'll\n      // fill this.reply again, restoring the invariant. This does rely\n      // on any response being recv'ed after resolving the promise,\n      // below; hence, I use synchronous defer.\n      if (this.pending.length > 0) {\n        var send = this.pending.shift();\n        this.reply = send.reply;\n        this.sendImmediately(send.method, send.fields);\n      }\n      return reply(null, f);\n    }\n  }\n}\n\n// Shutdown protocol. There's three scenarios:\n//\n// 1. The application decides to shut the channel\n// 2. The server decides to shut the channel, possibly because of\n// something the application did\n// 3. The connection is closing, so there won't be any more frames\n// going back and forth.\n//\n// 1 and 2 involve an exchange of method frames (Close and CloseOk),\n// while 3 doesn't; the connection simply says \"shutdown\" to the\n// channel, which then acts as if it's closing, without going through\n// the exchange.\n\nfunction invalidOp(msg, stack) {\n  return function() {\n    throw new IllegalOperationError(msg, stack);\n  };\n}\n\nfunction invalidateSend(ch, msg, stack) {\n  ch.sendImmediately = ch.sendOrEnqueue = ch.sendMessage =\n    invalidOp(msg, stack);\n}\n\n// Kick off a message delivery given a BasicDeliver or BasicReturn\n// frame (BasicGet uses the RPC mechanism)\nfunction acceptDeliveryOrReturn(f) {\n  var event;\n  if (f.id === defs.BasicDeliver) event = 'delivery';\n  else if (f.id === defs.BasicReturn) event = 'return';\n  else throw fmt(\"Expected BasicDeliver or BasicReturn; got %s\",\n                 inspect(f));\n\n  var self = this;\n  var fields = f.fields;\n  return acceptMessage(function(message) {\n    message.fields = fields;\n    self.emit(event, message);\n  });\n}\n\n// Move to the state of waiting for message frames (headers, then\n// one or more content frames)\nfunction acceptMessage(continuation) {\n  var totalSize = 0, remaining = 0;\n  var buffers = null;\n\n  var message = {\n    fields: null,\n    properties: null,\n    content: null\n  };\n\n  return headers;\n\n  // expect a headers frame\n  function headers(f) {\n    if (f.id === defs.BasicProperties) {\n      message.properties = f.fields;\n      totalSize = remaining = f.size;\n\n      // for zero-length messages, content frames aren't required.\n      if (totalSize === 0) {\n        message.content = Buffer.alloc(0);\n        continuation(message);\n        return acceptDeliveryOrReturn;\n      }\n      else {\n        return content;\n      }\n    }\n    else {\n      throw \"Expected headers frame after delivery\";\n    }\n  }\n\n  // expect a content frame\n  // %%% TODO cancelled messages (sent as zero-length content frame)\n  function content(f) {\n    if (f.content) {\n      var size = f.content.length;\n      remaining -= size;\n      if (remaining === 0) {\n        if (buffers !== null) {\n          buffers.push(f.content);\n          message.content = Buffer.concat(buffers);\n        }\n        else {\n          message.content = f.content;\n        }\n        continuation(message);\n        return acceptDeliveryOrReturn;\n      }\n      else if (remaining < 0) {\n        throw fmt(\"Too much content sent! Expected %d bytes\",\n                  totalSize);\n      }\n      else {\n        if (buffers !== null)\n          buffers.push(f.content);\n        else\n          buffers = [f.content];\n        return content;\n      }\n    }\n    else throw \"Expected content frame after headers\"\n  }\n}\n\n// This adds just a bit more stuff useful for the APIs, but not\n// low-level machinery.\nclass BaseChannel extends Channel {\n  constructor (connection) {\n    super(connection);\n    this.consumers = new Map();\n  }\n\n  // Not sure I like the ff, it's going to be changing hidden classes\n  // all over the place. On the other hand, whaddya do.\n  registerConsumer (tag, callback) {\n    this.consumers.set(tag, callback);\n  }\n\n  unregisterConsumer (tag) {\n    this.consumers.delete(tag);\n  }\n\n  dispatchMessage (fields, message) {\n    var consumerTag = fields.consumerTag;\n    var consumer = this.consumers.get(consumerTag);\n    if (consumer) {\n      return consumer(message);\n    }\n    else {\n      // %%% Surely a race here\n      throw new Error(\"Unknown consumer: \" + consumerTag);\n    }\n  }\n\n  handleDelivery (message) {\n    return this.dispatchMessage(message.fields, message);\n  }\n\n  handleCancel (fields) {\n    var result = this.dispatchMessage(fields, null);\n    this.unregisterConsumer(fields.consumerTag);\n    return result;\n  }\n}\n\nmodule.exports.acceptMessage = acceptMessage;\nmodule.exports.BaseChannel = BaseChannel;\nmodule.exports.Channel = Channel;\n", "//\n//\n//\n\n'use strict';\n\n/*\nThe channel (promise) and callback APIs have similar signatures, and\nin particular, both need AMQP fields prepared from the same arguments\nand options. The arguments marshalling is done here. Each of the\nprocedures below takes arguments and options (the latter in an object)\nparticular to the operation it represents, and returns an object with\nfields for handing to the encoder.\n*/\n\n// A number of AMQP methods have a table-typed field called\n// `arguments`, that is intended to carry extension-specific\n// values. RabbitMQ uses this in a number of places; e.g., to specify\n// an 'alternate exchange'.\n//\n// Many of the methods in this API have an `options` argument, from\n// which I take both values that have a default in AMQP (e.g.,\n// autoDelete in QueueDeclare) *and* values that are specific to\n// RabbitMQ (e.g., 'alternate-exchange'), which would normally be\n// supplied in `arguments`. So that extensions I don't support yet can\n// be used, I include `arguments` itself among the options.\n//\n// The upshot of this is that I often need to prepare an `arguments`\n// value that has any values passed in `options.arguments` as well as\n// any I've promoted to being options themselves. Since I don't want\n// to mutate anything passed in, the general pattern is to create a\n// fresh object with the `arguments` value given as its prototype; all\n// fields in the supplied value will be serialised, as well as any I\n// set on the fresh object. What I don't want to do, however, is set a\n// field to undefined by copying possibly missing field values,\n// because that will mask a value in the prototype.\n//\n// NB the `arguments` field already has a default value of `{}`, so\n// there's no need to explicitly default it unless I'm setting values.\nfunction setIfDefined(obj, prop, value) {\n  if (value != undefined) obj[prop] = value;\n}\n\nvar EMPTY_OPTIONS = Object.freeze({});\n\nvar Args = {};\n\nArgs.assertQueue = function(queue, options) {\n  queue = queue || '';\n  options = options || EMPTY_OPTIONS;\n\n  var argt = Object.create(options.arguments || null);\n  setIfDefined(argt, 'x-expires', options.expires);\n  setIfDefined(argt, 'x-message-ttl', options.messageTtl);\n  setIfDefined(argt, 'x-dead-letter-exchange',\n               options.deadLetterExchange);\n  setIfDefined(argt, 'x-dead-letter-routing-key',\n               options.deadLetterRoutingKey);\n  setIfDefined(argt, 'x-max-length', options.maxLength);\n  setIfDefined(argt, 'x-max-priority', options.maxPriority);\n  setIfDefined(argt, 'x-overflow', options.overflow);\n  setIfDefined(argt, 'x-queue-mode', options.queueMode);\n\n  return {\n    queue: queue,\n    exclusive: !!options.exclusive,\n    durable: (options.durable === undefined) ? true : options.durable,\n    autoDelete: !!options.autoDelete,\n    arguments: argt,\n    passive: false,\n    // deprecated but we have to include it\n    ticket: 0,\n    nowait: false\n  };\n};\n\nArgs.checkQueue = function(queue) {\n  return {\n    queue: queue,\n    passive: true, // switch to \"completely different\" mode\n    nowait: false,\n    durable: true, autoDelete: false, exclusive: false, // ignored\n    ticket: 0,\n  };\n};\n\nArgs.deleteQueue = function(queue, options) {\n  options = options || EMPTY_OPTIONS;\n  return {\n    queue: queue,\n    ifUnused: !!options.ifUnused,\n    ifEmpty: !!options.ifEmpty,\n    ticket: 0, nowait: false\n  };\n};\n\nArgs.purgeQueue = function(queue) {\n  return {\n    queue: queue,\n    ticket: 0, nowait: false\n  };\n};\n\nArgs.bindQueue = function(queue, source, pattern, argt) {\n  return {\n    queue: queue,\n    exchange: source,\n    routingKey: pattern,\n    arguments: argt,\n    ticket: 0, nowait: false\n  };\n};\n\nArgs.unbindQueue = function(queue, source, pattern, argt) {\n  return {\n    queue: queue,\n    exchange: source,\n    routingKey: pattern,\n    arguments: argt,\n    ticket: 0, nowait: false\n  };\n};\n\nArgs.assertExchange = function(exchange, type, options) {\n  options = options || EMPTY_OPTIONS;\n  var argt = Object.create(options.arguments || null);\n  setIfDefined(argt, 'alternate-exchange', options.alternateExchange);\n  return {\n    exchange: exchange,\n    ticket: 0,\n    type: type,\n    passive: false,\n    durable: (options.durable === undefined) ? true : options.durable,\n    autoDelete: !!options.autoDelete,\n    internal: !!options.internal,\n    nowait: false,\n    arguments: argt\n  };\n};\n\nArgs.checkExchange = function(exchange) {\n  return {\n    exchange: exchange,\n    passive: true, // switch to 'may as well be another method' mode\n    nowait: false,\n    // ff are ignored\n    durable: true, internal: false,  type: '',  autoDelete: false,\n    ticket: 0\n  };\n};\n\nArgs.deleteExchange = function(exchange, options) {\n  options = options || EMPTY_OPTIONS;\n  return {\n    exchange: exchange,\n    ifUnused: !!options.ifUnused,\n    ticket: 0, nowait: false\n  };\n};\n\nArgs.bindExchange = function(dest, source, pattern, argt) {\n  return {\n    source: source,\n    destination: dest,\n    routingKey: pattern,\n    arguments: argt,\n    ticket: 0, nowait: false\n  };\n};\n\nArgs.unbindExchange = function(dest, source, pattern, argt) {\n  return {\n    source: source,\n    destination: dest,\n    routingKey: pattern,\n    arguments: argt,\n    ticket: 0, nowait: false\n  };\n};\n\n// It's convenient to construct the properties and the method fields\n// at the same time, since in the APIs, values for both can appear in\n// `options`. Since the property or mthod field names don't overlap, I\n// just return one big object that can be used for both purposes, and\n// the encoder will pick out what it wants.\nArgs.publish = function(exchange, routingKey, options) {\n  options = options || EMPTY_OPTIONS;\n\n  // The CC and BCC fields expect an array of \"longstr\", which would\n  // normally be buffer values in JavaScript; however, since a field\n  // array (or table) cannot have shortstr values, the codec will\n  // encode all strings as longstrs anyway.\n  function convertCC(cc) {\n    if (cc === undefined) {\n      return undefined;\n    }\n    else if (Array.isArray(cc)) {\n      return cc.map(String);\n    }\n    else return [String(cc)];\n  }\n\n  var headers = Object.create(options.headers || null);\n  setIfDefined(headers, 'CC', convertCC(options.CC));\n  setIfDefined(headers, 'BCC', convertCC(options.BCC));\n\n  var deliveryMode; // undefined will default to 1 (non-persistent)\n\n  // Previously I overloaded deliveryMode be a boolean meaning\n  // 'persistent or not'; better is to name this option for what it\n  // is, but I need to have backwards compatibility for applications\n  // that either supply a numeric or boolean value.\n  if (options.persistent !== undefined)\n    deliveryMode = (options.persistent) ? 2 : 1;\n  else if (typeof options.deliveryMode === 'number')\n    deliveryMode = options.deliveryMode;\n  else if (options.deliveryMode) // is supplied and truthy\n    deliveryMode = 2;\n\n  var expiration = options.expiration;\n  if (expiration !== undefined) expiration = expiration.toString();\n\n  return {\n    // method fields\n    exchange: exchange,\n    routingKey: routingKey,\n    mandatory: !!options.mandatory,\n    immediate: false, // RabbitMQ doesn't implement this any more\n    ticket: undefined,\n    // properties\n    contentType: options.contentType,\n    contentEncoding: options.contentEncoding,\n    headers: headers,\n    deliveryMode: deliveryMode,\n    priority: options.priority,\n    correlationId: options.correlationId,\n    replyTo: options.replyTo,\n    expiration: expiration,\n    messageId: options.messageId,\n    timestamp: options.timestamp,\n    type: options.type,\n    userId: options.userId,\n    appId: options.appId,\n    clusterId: undefined\n  };\n};\n\nArgs.consume = function(queue, options) {\n  options = options || EMPTY_OPTIONS;\n  var argt = Object.create(options.arguments || null);\n  setIfDefined(argt, 'x-priority', options.priority);\n  return {\n    ticket: 0,\n    queue: queue,\n    consumerTag: options.consumerTag || '',\n    noLocal: !!options.noLocal,\n    noAck: !!options.noAck,\n    exclusive: !!options.exclusive,\n    nowait: false,\n    arguments: argt\n  };\n};\n\nArgs.cancel = function(consumerTag) {\n  return {\n    consumerTag: consumerTag,\n    nowait: false\n  };\n};\n\nArgs.get = function(queue, options) {\n  options = options || EMPTY_OPTIONS;\n  return {\n    ticket: 0,\n    queue: queue,\n    noAck: !!options.noAck\n  };\n};\n\nArgs.ack = function(tag, allUpTo) {\n  return {\n    deliveryTag: tag,\n    multiple: !!allUpTo\n  };\n};\n\nArgs.nack = function(tag, allUpTo, requeue) {\n  return {\n    deliveryTag: tag,\n    multiple: !!allUpTo,\n    requeue: (requeue === undefined) ? true : requeue\n  };\n};\n\nArgs.reject = function(tag, requeue) {\n  return {\n    deliveryTag: tag,\n    requeue: (requeue === undefined) ? true : requeue\n  };\n};\n\nArgs.prefetch = function(count, global) {\n  return {\n    prefetchCount: count || 0,\n    prefetchSize: 0,\n    global: !!global\n  };\n};\n\nArgs.recover = function() {\n  return {requeue: true};\n};\n\nmodule.exports = Object.freeze(Args);\n", "//\n//\n//\n\n'use strict';\n\nconst EventEmitter = require('events');\nconst promisify = require('util').promisify;\nconst defs = require('./defs');\nconst {BaseChannel} = require('./channel');\nconst {acceptMessage} = require('./channel');\nconst Args = require('./api_args');\nconst {inspect} = require('./format');\n\nclass ChannelModel extends EventEmitter {\n  constructor(connection) {\n    super();\n    this.connection = connection;\n\n    ['error', 'close', 'blocked', 'unblocked'].forEach(ev => {\n      connection.on(ev, this.emit.bind(this, ev));\n    });\n  }\n\n  close() {\n    return promisify(this.connection.close.bind(this.connection))();\n  }\n\n  updateSecret(newSecret, reason) {\n    return promisify(this.connection._updateSecret.bind(this.connection))(newSecret, reason);\n  }\n\n  async createChannel(options) {\n    const channel = new Channel(this.connection);\n    channel.setOptions(options);\n    await channel.open();\n    return channel;\n  }\n\n  async createConfirmChannel(options) {\n    const channel = new ConfirmChannel(this.connection);\n    channel.setOptions(options);\n    await channel.open();\n    await channel.rpc(defs.ConfirmSelect, {nowait: false}, defs.ConfirmSelectOk);\n    return channel;\n  }\n}\n\n// Channels\n\nclass Channel extends BaseChannel {\n  constructor(connection) {\n    super(connection);\n    this.on('delivery', this.handleDelivery.bind(this));\n    this.on('cancel', this.handleCancel.bind(this));\n  }\n\n  // An RPC that returns a 'proper' promise, which resolves to just the\n  // response's fields; this is intended to be suitable for implementing\n  // API procedures.\n  async rpc(method, fields, expect) {\n    const f = await promisify(cb => {\n      return this._rpc(method, fields, expect, cb);\n    })();\n\n    return f.fields;\n  }\n\n  // Do the remarkably simple channel open handshake\n  async open() {\n    const ch = await this.allocate.bind(this)();\n    return ch.rpc(defs.ChannelOpen, {outOfBand: \"\"},\n                  defs.ChannelOpenOk);\n  }\n\n  close() {\n    return promisify(cb => {\n      return this.closeBecause(\"Goodbye\", defs.constants.REPLY_SUCCESS,\n                      cb);\n    })();\n  }\n\n  // === Public API, declaring queues and stuff ===\n\n  assertQueue(queue, options) {\n    return this.rpc(defs.QueueDeclare,\n                    Args.assertQueue(queue, options),\n                    defs.QueueDeclareOk);\n  }\n\n  checkQueue(queue) {\n    return this.rpc(defs.QueueDeclare,\n                    Args.checkQueue(queue),\n                    defs.QueueDeclareOk);\n  }\n\n  deleteQueue(queue, options) {\n    return this.rpc(defs.QueueDelete,\n                    Args.deleteQueue(queue, options),\n                    defs.QueueDeleteOk);\n  }\n\n  purgeQueue(queue) {\n    return this.rpc(defs.QueuePurge,\n                    Args.purgeQueue(queue),\n                    defs.QueuePurgeOk);\n  }\n\n  bindQueue(queue, source, pattern, argt) {\n    return this.rpc(defs.QueueBind,\n                    Args.bindQueue(queue, source, pattern, argt),\n                    defs.QueueBindOk);\n  }\n\n  unbindQueue(queue, source, pattern, argt) {\n    return this.rpc(defs.QueueUnbind,\n                    Args.unbindQueue(queue, source, pattern, argt),\n                    defs.QueueUnbindOk);\n  }\n\n  assertExchange(exchange, type, options) {\n    // The server reply is an empty set of fields, but it's convenient\n    // to have the exchange name handed to the continuation.\n    return this.rpc(defs.ExchangeDeclare,\n                    Args.assertExchange(exchange, type, options),\n                    defs.ExchangeDeclareOk)\n      .then(_ok => { return { exchange }; });\n  }\n\n  checkExchange(exchange) {\n    return this.rpc(defs.ExchangeDeclare,\n                    Args.checkExchange(exchange),\n                    defs.ExchangeDeclareOk);\n  }\n\n  deleteExchange(name, options) {\n    return this.rpc(defs.ExchangeDelete,\n                    Args.deleteExchange(name, options),\n                    defs.ExchangeDeleteOk);\n  }\n\n  bindExchange(dest, source, pattern, argt) {\n    return this.rpc(defs.ExchangeBind,\n                    Args.bindExchange(dest, source, pattern, argt),\n                    defs.ExchangeBindOk);\n  }\n\n  unbindExchange(dest, source, pattern, argt) {\n    return this.rpc(defs.ExchangeUnbind,\n                    Args.unbindExchange(dest, source, pattern, argt),\n                    defs.ExchangeUnbindOk);\n  }\n\n  // Working with messages\n\n  publish(exchange, routingKey, content, options) {\n    const fieldsAndProps = Args.publish(exchange, routingKey, options);\n    return this.sendMessage(fieldsAndProps, fieldsAndProps, content);\n  }\n\n  sendToQueue(queue, content, options) {\n    return this.publish('', queue, content, options);\n  }\n\n  consume(queue, callback, options) {\n    // NB we want the callback to be run synchronously, so that we've\n    // registered the consumerTag before any messages can arrive.\n    const fields = Args.consume(queue, options);\n    return new Promise((resolve, reject) => {\n      this._rpc(defs.BasicConsume, fields, defs.BasicConsumeOk, (err, ok) => {\n        if (err) return reject(err);\n        this.registerConsumer(ok.fields.consumerTag, callback);\n        resolve(ok.fields);\n      });\n    });\n  }\n\n  async cancel(consumerTag) {\n    const ok = await promisify(cb => {\n      this._rpc(defs.BasicCancel, Args.cancel(consumerTag),\n            defs.BasicCancelOk,\n            cb);\n    })()\n    .then(ok => {\n      this.unregisterConsumer(consumerTag);\n      return ok.fields;\n    });\n  }\n\n  get(queue, options) {\n    const fields = Args.get(queue, options);\n    return new Promise((resolve, reject) => {\n      this.sendOrEnqueue(defs.BasicGet, fields, (err, f) => {\n        if (err) return reject(err);\n        if (f.id === defs.BasicGetEmpty) {\n          return resolve(false);\n        }\n        else if (f.id === defs.BasicGetOk) {\n          const fields = f.fields;\n          this.handleMessage = acceptMessage(m => {\n            m.fields = fields;\n            resolve(m);\n          });\n        }\n        else {\n          reject(new Error(`Unexpected response to BasicGet: ${inspect(f)}`));\n        }\n      });\n    });\n  }\n\n  ack(message, allUpTo) {\n    this.sendImmediately(\n      defs.BasicAck,\n      Args.ack(message.fields.deliveryTag, allUpTo));\n  }\n\n  ackAll() {\n    this.sendImmediately(defs.BasicAck, Args.ack(0, true));\n  }\n\n  nack(message, allUpTo, requeue) {\n    this.sendImmediately(\n      defs.BasicNack,\n      Args.nack(message.fields.deliveryTag, allUpTo, requeue));\n  }\n\n  nackAll(requeue) {\n    this.sendImmediately(defs.BasicNack,\n                         Args.nack(0, true, requeue));\n  }\n\n  // `Basic.Nack` is not available in older RabbitMQ versions (or in the\n  // AMQP specification), so you have to use the one-at-a-time\n  // `Basic.Reject`. This is otherwise synonymous with\n  // `#nack(message, false, requeue)`.\n  reject(message, requeue) {\n    this.sendImmediately(\n      defs.BasicReject,\n      Args.reject(message.fields.deliveryTag, requeue));\n  }\n\n  recover() {\n    return this.rpc(defs.BasicRecover,\n                    Args.recover(),\n                    defs.BasicRecoverOk);\n  }\n\n  qos(count, global) {\n    return this.rpc(defs.BasicQos,\n                    Args.prefetch(count, global),\n                    defs.BasicQosOk);\n  }\n}\n\n// There are more options in AMQP than exposed here; RabbitMQ only\n// implements prefetch based on message count, and only for individual\n// channels or consumers. RabbitMQ v3.3.0 and after treat prefetch\n// (without `global` set) as per-consumer (for consumers following),\n// and prefetch with `global` set as per-channel.\nChannel.prototype.prefetch = Channel.prototype.qos\n\n// Confirm channel. This is a channel with confirms 'switched on',\n// meaning sent messages will provoke a responding 'ack' or 'nack'\n// from the server. The upshot of this is that `publish` and\n// `sendToQueue` both take a callback, which will be called either\n// with `null` as its argument to signify 'ack', or an exception as\n// its argument to signify 'nack'.\n\nclass ConfirmChannel extends Channel {\n  publish(exchange, routingKey, content, options, cb) {\n    this.pushConfirmCallback(cb);\n    return super.publish(exchange, routingKey, content, options);\n  }\n\n  sendToQueue(queue, content, options, cb) {\n    return this.publish('', queue, content, options, cb);\n  }\n\n  waitForConfirms() {\n    const awaiting = [];\n    const unconfirmed = this.unconfirmed;\n    unconfirmed.forEach((val, index) => {\n      if (val !== null) {\n        const confirmed = new Promise((resolve, reject) => {\n          unconfirmed[index] = err => {\n            if (val) val(err);\n            if (err === null) resolve();\n            else reject(err);\n          };\n        });\n        awaiting.push(confirmed);\n      }\n    });\n    // Channel closed\n    if (!this.pending) {\n      var cb;\n      while (cb = this.unconfirmed.shift()) {\n        if (cb) cb(new Error('channel closed'));\n      }\n    }\n    return Promise.all(awaiting);\n  }\n}\n\nmodule.exports.ConfirmChannel = ConfirmChannel;\nmodule.exports.Channel = Channel;\nmodule.exports.ChannelModel = ChannelModel;\n", "var raw_connect = require('./lib/connect').connect;\nvar ChannelModel = require('./lib/channel_model').ChannelModel;\nvar promisify = require('util').promisify;\n\nfunction connect(url, connOptions) {\n  return promisify(function(cb) {\n    return raw_connect(url, connOptions, cb);\n  })()\n  .then(function(conn) {\n    return new ChannelModel(conn);\n  });\n};\n\nmodule.exports.connect = connect;\nmodule.exports.credentials = require('./lib/credentials');\nmodule.exports.IllegalOperationError = require('./lib/error').IllegalOperationError;\n", "/**\n * Node.js版本的模型配置加载器\n * 用于在Electron主进程中动态加载模型配置\n */\n\nimport fs from 'fs';\nimport path from 'path';\nimport { fileURLToPath } from 'url';\n\n// 在ES模块中获取__dirname的等价物\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\n// 模型配置接口（与前端保持一致）\nexport interface ModelConfig {\n  name: string;\n  displayName: string;\n  description: string;\n  category: 'linear' | 'tree' | 'ml' | 'clustering' | 'preprocessing';\n  type: 'regression' | 'classification' | 'clustering' | 'preprocessing';\n  [key: string]: any;\n}\n\n// 模型元数据接口\nexport interface ModelMetadata {\n  id: string;\n  fileName: string;\n  config: ModelConfig;\n}\n\n// Node.js版本的模型配置管理器\nexport class NodeModelConfigManager {\n  private static instance: NodeModelConfigManager;\n  private modelConfigs: Map<string, ModelMetadata> = new Map();\n  private modelDisplayNameMap: Map<string, string> = new Map();\n  private initialized = false;\n  private configDir: string;\n\n  private constructor() {\n    // 获取配置文件目录路径\n    // 在开发环境中：electron/utils -> src/config/modelParams\n    // 在生产环境中：需要考虑打包后的路径结构\n    this.configDir = this.getConfigDirectory();\n  }\n\n  /**\n   * 获取配置文件目录路径\n   */\n  private getConfigDirectory(): string {\n    // 尝试多个可能的路径\n    const possiblePaths = [\n      // 开发环境路径\n      path.join(__dirname, '../../src/config/modelParams'),\n      // 生产环境路径（相对于打包后的位置）\n      path.join(__dirname, '../../../src/config/modelParams'),\n      path.join(__dirname, '../../config/modelParams'),\n      // 如果在resources目录中\n      path.join(__dirname, '../config/modelParams'),\n    ];\n\n    for (const configPath of possiblePaths) {\n      if (fs.existsSync(configPath)) {\n        console.log(`Found model config directory at: ${configPath}`);\n        return configPath;\n      }\n    }\n\n    // 如果都找不到，使用默认路径并记录警告\n    const defaultPath = possiblePaths[0];\n    console.warn(`Model config directory not found, using default: ${defaultPath}`);\n    return defaultPath;\n  }\n\n  public static getInstance(): NodeModelConfigManager {\n    if (!NodeModelConfigManager.instance) {\n      NodeModelConfigManager.instance = new NodeModelConfigManager();\n    }\n    return NodeModelConfigManager.instance;\n  }\n\n  /**\n   * 初始化模型配置\n   */\n  public async initialize(): Promise<void> {\n    if (this.initialized) return;\n\n    try {\n      await this.loadAllModelConfigs();\n      this.buildDisplayNameMap();\n      this.initialized = true;\n      console.log('Node model config manager initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize node model config manager:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 加载所有模型配置文件\n   */\n  private async loadAllModelConfigs(): Promise<void> {\n    try {\n      // 检查配置目录是否存在\n      if (!fs.existsSync(this.configDir)) {\n        console.warn(`Model config directory not found: ${this.configDir}`);\n        return;\n      }\n\n      // 读取配置目录中的所有JSON文件\n      const files = fs.readdirSync(this.configDir);\n      const jsonFiles = files.filter(file => file.endsWith('.json'));\n\n      for (const fileName of jsonFiles) {\n        try {\n          const filePath = path.join(this.configDir, fileName);\n          const fileContent = fs.readFileSync(filePath, 'utf-8');\n          const config: ModelConfig = JSON.parse(fileContent);\n\n          const configId = fileName.replace('.json', '');\n\n          // 自动推断分类（如果配置文件中没有指定）\n          if (!config.category) {\n            config.category = this.inferCategory(configId);\n          }\n\n          const metadata: ModelMetadata = {\n            id: configId,\n            fileName,\n            config\n          };\n\n          this.modelConfigs.set(configId, metadata);\n          console.log(`Loaded model config: ${configId}`);\n        } catch (error) {\n          console.warn(`Failed to load model config: ${fileName}`, error);\n        }\n      }\n    } catch (error) {\n      console.error('Error loading model configs:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 根据模型ID推断分类\n   */\n  private inferCategory(modelId: string): ModelConfig['category'] {\n    const treeModels = ['DecisionTreeRegressor', 'RandomForestRegressor', 'GradientBoostingRegressor', 'XGBoost'];\n    const linearModels = ['LinearRegression', 'Ridge', 'Lasso', 'ElasticNet'];\n    \n    if (treeModels.includes(modelId)) return 'tree';\n    if (linearModels.includes(modelId)) return 'linear';\n    return 'ml';\n  }\n\n  /**\n   * 构建显示名称映射\n   */\n  private buildDisplayNameMap(): void {\n    this.modelDisplayNameMap.clear();\n    \n    this.modelConfigs.forEach((metadata, id) => {\n      this.modelDisplayNameMap.set(id, metadata.config.displayName);\n    });\n  }\n\n  /**\n   * 获取所有模型配置\n   */\n  public getAllModels(): ModelMetadata[] {\n    return Array.from(this.modelConfigs.values());\n  }\n\n  /**\n   * 根据ID获取模型配置\n   */\n  public getModelById(id: string): ModelMetadata | undefined {\n    return this.modelConfigs.get(id);\n  }\n\n  /**\n   * 获取模型显示名称\n   */\n  public getModelDisplayName(modelId: string): string {\n    // 如果没有初始化或找不到配置，使用默认映射\n    if (!this.initialized || this.modelDisplayNameMap.size === 0) {\n      return this.getDefaultDisplayName(modelId);\n    }\n    return this.modelDisplayNameMap.get(modelId) || this.getDefaultDisplayName(modelId);\n  }\n\n  /**\n   * 获取默认显示名称（当配置文件不可用时的降级方案）\n   */\n  private getDefaultDisplayName(modelId: string): string {\n    const defaultMap: Record<string, string> = {\n      'DecisionTreeRegressor': '决策树',\n      'RandomForestRegressor': '随机森林',\n      'XGBoost': 'XGBoost',\n      'GradientBoostingRegressor': '梯度提升回归',\n      'SVR': '支持向量机',\n      'MLPRegressor': '人工神经网络',\n      'LinearRegression': '多元线性回归',\n      'Ridge': '岭回归',\n      'Lasso': 'Lasso回归',\n      'ElasticNet': '弹性网络回归'\n    };\n    return defaultMap[modelId] || modelId;\n  }\n\n  /**\n   * 获取所有模型的显示名称映射\n   */\n  public getModelDisplayNameMap(): Record<string, string> {\n    const map: Record<string, string> = {};\n    this.modelDisplayNameMap.forEach((displayName, id) => {\n      map[id] = displayName;\n    });\n    return map;\n  }\n\n  /**\n   * 检查模型是否为ML模型（非线性模型）\n   */\n  public isMLModel(modelId: string): boolean {\n    const metadata = this.getModelById(modelId);\n    return metadata ? metadata.config.category !== 'linear' : false;\n  }\n\n  /**\n   * 根据分类获取模型\n   */\n  public getModelsByCategory(category: string): ModelMetadata[] {\n    return Array.from(this.modelConfigs.values()).filter(\n      metadata => metadata.config.category === category\n    );\n  }\n\n  /**\n   * 重新加载所有配置\n   */\n  public async reload(): Promise<void> {\n    this.modelConfigs.clear();\n    this.modelDisplayNameMap.clear();\n    this.initialized = false;\n    await this.initialize();\n  }\n\n  /**\n   * 获取初始化状态\n   */\n  public isInitialized(): boolean {\n    return this.initialized;\n  }\n}\n\n// 导出单例实例\nexport const nodeModelConfigManager = NodeModelConfigManager.getInstance();\n\n// 便捷函数\nexport const initializeNodeModelManager = () => nodeModelConfigManager.initialize();\nexport const getNodeModelDisplayName = (modelId: string) => nodeModelConfigManager.getModelDisplayName(modelId);\nexport const getNodeModelDisplayNameMap = () => nodeModelConfigManager.getModelDisplayNameMap();\nexport const getNodeModelById = (id: string) => nodeModelConfigManager.getModelById(id);\nexport const isNodeMLModel = (modelId: string) => nodeModelConfigManager.isMLModel(modelId);\n", "import amqp, { Connection, Channel, ConsumeMessage } from 'amqplib';\nimport { BrowserWindow, Notification } from 'electron';\nimport { nodeModelConfigManager, getNodeModelDisplayName } from '../utils/modelConfigLoader';\n\nexport interface MLModelNotification {\n  taskId: string;\n  modelType: string;\n  status: 'completed' | 'failed';\n  message: string;\n  result?: {\n    accuracy?: number;\n    metrics?: Record<string, any>;\n    modelPath?: string;\n    resultUrl?: string; // 用于打开结果页面的URL\n  };\n  error?: string;\n  timestamp: string;\n}\n\nexport class RabbitMQNotificationService {\n  private connection: Connection | null = null;\n  private channel: Channel | null = null;\n  private isConnected = false;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectDelay = 5000; // 5 seconds\n  private mainWindow: BrowserWindow | null = null;\n\n  // Queue names - 只监听通知队列\n  private readonly ML_MODEL_NOTIFICATION_QUEUE = 'ml_model_notifications';\n\n  constructor(mainWindow?: BrowserWindow) {\n    this.mainWindow = mainWindow || null;\n    // 初始化模型配置管理器\n    this.initializeModelConfig();\n  }\n\n  // 初始化模型配置管理器\n  private async initializeModelConfig() {\n    try {\n      await nodeModelConfigManager.initialize();\n      console.log('RabbitMQ: Model config manager initialized');\n    } catch (error) {\n      console.error('RabbitMQ: Failed to initialize model config manager:', error);\n    }\n  }\n\n  // 获取模型显示名称（带降级处理）\n  private getModelDisplayName(modelType: string): string {\n    try {\n      return getNodeModelDisplayName(modelType);\n    } catch (error) {\n      console.warn('Failed to get model display name, using fallback:', error);\n      // 降级到默认映射\n      const defaultMap: Record<string, string> = {\n        'DecisionTreeRegressor': '决策树',\n        'RandomForestRegressor': '随机森林',\n        'XGBoost': 'XGBoost',\n        'GradientBoostingRegressor': '梯度提升回归',\n        'SVR': '支持向量机',\n        'MLPRegressor': '人工神经网络'\n      };\n      return defaultMap[modelType] || modelType;\n    }\n  }\n\n  // 设置主窗口引用\n  setMainWindow(window: BrowserWindow) {\n    this.mainWindow = window;\n  }\n\n  // 连接到 RabbitMQ\n  async connect(url: string = 'amqp://localhost'): Promise<boolean> {\n    try {\n      console.log('Connecting to RabbitMQ...');\n      this.connection = await amqp.connect(url);\n      this.channel = await this.connection.createChannel();\n      \n      // 设置连接事件监听\n      this.connection.on('error', this.handleConnectionError.bind(this));\n      this.connection.on('close', this.handleConnectionClose.bind(this));\n\n      // 声明队列\n      await this.setupQueues();\n      \n      // 开始监听通知\n      await this.startNotificationListener();\n      \n      this.isConnected = true;\n      this.reconnectAttempts = 0;\n      \n      console.log('RabbitMQ connected successfully');\n      return true;\n    } catch (error) {\n      console.error('Failed to connect to RabbitMQ:', error);\n      this.isConnected = false;\n      \n      // 尝试重连\n      if (this.reconnectAttempts < this.maxReconnectAttempts) {\n        this.reconnectAttempts++;\n        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);\n        setTimeout(() => this.connect(url), this.reconnectDelay);\n      }\n      \n      return false;\n    }\n  }\n\n  // 设置队列\n  private async setupQueues() {\n    if (!this.channel) throw new Error('Channel not available');\n\n    // 声明通知队列（持久化）\n    await this.channel.assertQueue(this.ML_MODEL_NOTIFICATION_QUEUE, {\n      durable: true,\n      arguments: {\n        'x-message-ttl': 86400000, // 24 hours TTL\n      }\n    });\n\n    console.log('RabbitMQ notification queue setup completed');\n  }\n\n  // 开始监听通知\n  private async startNotificationListener() {\n    if (!this.channel) return;\n\n    // 监听模型完成通知\n    await this.channel.consume(this.ML_MODEL_NOTIFICATION_QUEUE, (msg: ConsumeMessage | null) => {\n      if (msg) {\n        try {\n          const notification: MLModelNotification = JSON.parse(msg.content.toString());\n          this.handleModelNotification(notification);\n          this.channel?.ack(msg);\n        } catch (error) {\n          console.error('Error processing ML model notification:', error);\n          this.channel?.nack(msg, false, false);\n        }\n      }\n    });\n\n    console.log('ML model notification listener started');\n  }\n\n  // 处理模型完成通知\n  private handleModelNotification(notification: MLModelNotification) {\n    console.log('Received ML model notification:', notification);\n\n    // 显示系统通知\n    this.showSystemNotification(notification);\n\n    // 通知前端\n    this.notifyFrontend('ml-model-notification', notification);\n  }\n\n  // 显示系统通知\n  private showSystemNotification(notification: MLModelNotification) {\n    try {\n      // 使用动态模型配置管理器获取显示名称，如果失败则使用默认名称\n      const modelName = this.getModelDisplayName(notification.modelType);\n\n      let title: string;\n      let body: string;\n\n      if (notification.status === 'completed') {\n        title = `${modelName}模型构建完成`;\n        body = notification.message || '模型训练已完成，点击查看结果';\n\n        if (notification.result?.accuracy) {\n          body += `\\n准确率: ${(notification.result.accuracy * 100).toFixed(2)}%`;\n        }\n      } else {\n        title = `${modelName}模型构建失败`;\n        body = notification.error || notification.message || '模型训练过程中出现错误';\n      }\n\n      const systemNotification = new Notification({\n        title,\n        body,\n        icon: this.getNotificationIcon(notification.status),\n        urgency: notification.status === 'failed' ? 'critical' : 'normal',\n        timeoutType: 'never', // 不自动消失\n        actions: notification.status === 'completed' ? [\n          {\n            type: 'button',\n            text: '查看结果'\n          }\n        ] : []\n      });\n\n      // 标记通知是否已被关闭\n      let notificationClosed = false;\n\n      // 处理通知关闭事件（必须在click事件之前注册）\n      systemNotification.on('close', () => {\n        console.log('Notification closed by user');\n        notificationClosed = true;\n      });\n\n      // 处理通知点击事件（排除关闭按钮）\n      systemNotification.on('click', () => {\n        console.log('Notification clicked, closed status:', notificationClosed);\n\n        // 使用短暂延迟来确保close事件已经触发\n        setTimeout(() => {\n          if (!notificationClosed) {\n            console.log('Processing notification click');\n            this.handleNotificationClick(notification);\n          } else {\n            console.log('Ignoring click - notification was closed');\n          }\n        }, 50);\n      });\n\n      // 处理操作按钮点击\n      systemNotification.on('action', (_event, index) => {\n        if (index === 0) { // 查看结果按钮\n          console.log('Action button clicked');\n          this.handleNotificationClick(notification);\n        }\n      });\n\n      // 处理通知失败事件\n      systemNotification.on('failed', (error) => {\n        console.error('Notification failed:', error);\n      });\n\n      systemNotification.show();\n\n    } catch (error) {\n      console.error('Error showing system notification:', error);\n    }\n  }\n\n  // 获取通知图标\n  private getNotificationIcon(_status: string): string | undefined {\n    // 这里可以根据状态返回不同的图标路径\n    // 暂时返回 undefined 使用默认图标\n    return undefined;\n  }\n\n  // 处理通知点击\n  private handleNotificationClick(notification: MLModelNotification) {\n    if (this.mainWindow && !this.mainWindow.isDestroyed()) {\n      // 聚焦主窗口\n      if (this.mainWindow.isMinimized()) {\n        this.mainWindow.restore();\n      }\n      this.mainWindow.focus();\n\n      // 通知前端打开结果页面\n      this.mainWindow.webContents.send('open-model-result', {\n        taskId: notification.taskId,\n        modelType: notification.modelType,\n        result: notification.result,\n        resultUrl: notification.result?.resultUrl\n      });\n    }\n  }\n\n  // 通知前端\n  private notifyFrontend(event: string, data: any) {\n    if (this.mainWindow && !this.mainWindow.isDestroyed()) {\n      this.mainWindow.webContents.send(event, data);\n    }\n  }\n\n  // 测试通知功能（开发用）\n  async testNotification() {\n    const testNotification: MLModelNotification = {\n      taskId: 'test-' + Date.now(),\n      modelType: 'DecisionTree',\n      status: 'completed',\n      message: '这是一个测试通知',\n      result: {\n        accuracy: 0.95,\n        metrics: { precision: 0.94, recall: 0.96 }\n      },\n      timestamp: new Date().toISOString()\n    };\n\n    this.handleModelNotification(testNotification);\n  }\n\n  // 处理连接错误\n  private handleConnectionError(error: Error) {\n    console.error('RabbitMQ connection error:', error);\n    this.isConnected = false;\n  }\n\n  // 处理连接关闭\n  private handleConnectionClose() {\n    console.log('RabbitMQ connection closed');\n    this.isConnected = false;\n    \n    // 尝试重连\n    if (this.reconnectAttempts < this.maxReconnectAttempts) {\n      this.reconnectAttempts++;\n      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);\n      setTimeout(() => this.connect(), this.reconnectDelay);\n    }\n  }\n\n  // 断开连接\n  async disconnect() {\n    try {\n      if (this.channel) {\n        await this.channel.close();\n        this.channel = null;\n      }\n      \n      if (this.connection) {\n        await this.connection.close();\n        this.connection = null;\n      }\n      \n      this.isConnected = false;\n      console.log('RabbitMQ disconnected');\n    } catch (error) {\n      console.error('Error disconnecting from RabbitMQ:', error);\n    }\n  }\n\n  // 检查连接状态\n  isConnectionActive(): boolean {\n    return this.isConnected && this.connection !== null && this.channel !== null;\n  }\n\n  // 获取连接信息\n  getConnectionInfo() {\n    return {\n      isConnected: this.isConnected,\n      reconnectAttempts: this.reconnectAttempts,\n      maxReconnectAttempts: this.maxReconnectAttempts\n    };\n  }\n}\n", "import { release } from \"node:os\";\r\nimport { fileURLToPath } from \"node:url\";\r\nimport { join, dirname } from \"node:path\";\r\nimport {\r\n  type MenuItem,\r\n  type MenuItemConstructorOptions,\r\n  app,\r\n  Menu,\r\n  shell,\r\n  ipcMain,\r\n  BrowserWindow,\r\n  dialog\r\n} from \"electron\";\r\n\r\n// 在文件顶部添加 fs 和 path 的引入\r\n// import { existsSync, mkdirSync, readFileSync, writeFileSync, readdirSync, Stats, statSync } from 'node:fs'\r\n// import { dialog } from 'electron'\r\n\r\n// The built directory structure\r\n//\r\n// ├─┬ dist-electron\r\n// │ ├─┬ main\r\n// │ │ └── index.js    > Electron-Main\r\n// │ └─┬ preload\r\n// │   └── index.mjs    > Preload-Scripts\r\n// ├─┬ dist\r\n// │ └── index.html    > Electron-Renderer\r\n//\r\nconst __filename = fileURLToPath(import.meta.url);\r\nconst __dirname = dirname(__filename);\r\nprocess.env.DIST_ELECTRON = join(__dirname, \"..\");\r\nprocess.env.DIST = join(process.env.DIST_ELECTRON, \"../dist\");\r\nprocess.env.PUBLIC = process.env.VITE_DEV_SERVER_URL\r\n  ? join(process.env.DIST_ELECTRON, \"../public\")\r\n  : process.env.DIST;\r\n// 是否为开发环境\r\nconst isDev = process.env[\"NODE_ENV\"] === \"development\";\r\n\r\n// Disable GPU Acceleration for Windows 7\r\nif (release().startsWith(\"6.1\")) app.disableHardwareAcceleration();\r\n\r\n// Set application name for Windows 10+ notifications\r\nif (process.platform === \"win32\") app.setAppUserModelId(app.getName());\r\n\r\nif (!app.requestSingleInstanceLock()) {\r\n  app.quit();\r\n  process.exit(0);\r\n}\r\n\r\n// Remove electron security warnings\r\n// This warning only shows in development mode\r\n// Read more on https://www.electronjs.org/docs/latest/tutorial/security\r\n// process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'\r\n\r\nlet win: BrowserWindow | null = null; // Will become mainWindow\r\nlet splashWin: BrowserWindow | null = null; // For the splash screen\r\n\r\n// RabbitMQ 通知服务实例\r\nlet rabbitMQService: RabbitMQNotificationService | null = null;\r\n\r\n// Here, you can also use other preload\r\nconst preload = join(__dirname, \"../preload/index.mjs\");\r\nconst url = process.env.VITE_DEV_SERVER_URL;\r\nconst indexHtml = join(process.env.DIST, \"index.html\");\r\n\r\n// 创建菜单\r\nfunction createMenu(label = \"进入全屏幕\") {\r\n  const menu = Menu.buildFromTemplate(\r\n    appMenu(label) as (MenuItemConstructorOptions | MenuItem)[]\r\n  );\r\n  Menu.setApplicationMenu(menu);\r\n}\r\n\r\nasync function createMainWindow(initialRoute?: string) { // Renamed from createWindow, added initialRoute\r\n  win = new BrowserWindow({\r\n    width: 1024,\r\n    height: 768,\r\n    minWidth: 1024,\r\n    minHeight: 768,\r\n    title: \"Main window\",\r\n    icon: join(process.env.PUBLIC, \"favicon.ico\"),\r\n    webPreferences: {\r\n      preload,\r\n      nodeIntegration: false, // Recommended for security\r\n      contextIsolation: true, // Required for contextBridge\r\n    }\r\n  });\r\n\r\n  const targetUrl = initialRoute ? `${url}#${initialRoute}` : url;\r\n  const targetIndexHtml = initialRoute ? { pathname: indexHtml, hash: initialRoute } : indexHtml;\r\n\r\n  if (process.env.VITE_DEV_SERVER_URL) {\r\n    // electron-vite-vue#298\r\n    win.loadURL(targetUrl); // MainWindow loads the main content\r\n    // Open devTool if the app is not packaged\r\n    win.webContents.openDevTools({ mode: 'bottom' }); // 打开开发者工具\r\n  } else {\r\n    win.loadFile(typeof targetIndexHtml === 'string' ? targetIndexHtml : targetIndexHtml.pathname, typeof targetIndexHtml === 'string' ? {} : { hash: targetIndexHtml.hash }); // MainWindow loads the main content\r\n  }\r\n\r\n  createMenu();\r\n\r\n  // Test actively push message to the Electron-Renderer\r\n  win.webContents.on(\"did-finish-load\", () => {\r\n    win?.webContents.send(\"main-process-message\", new Date().toLocaleString());\r\n  });\r\n\r\n  // Make all links open with the browser, not with the application\r\n  win.webContents.setWindowOpenHandler(({ url }) => {\r\n    // if (url.startsWith(\"https:\")) shell.openExternal(url);\r\n    // return { action: \"deny\" };\r\n    // 这里用于新开一个页面--------->必须要把这个设置成允许，否则将不能使用路由打开一个子页面\r\n\r\n    // 创建自定义窗口，隐藏菜单栏防止出现bug\r\n    const childWindow = new BrowserWindow({\r\n      autoHideMenuBar: true, // 隐藏菜单栏，防止出现bug\r\n      webPreferences: {\r\n        preload,\r\n        nodeIntegration: false,\r\n        contextIsolation: true\r\n      }\r\n    });\r\n\r\n    childWindow.loadURL(url);\r\n\r\n    return { action: \"deny\" }; // 阻止默认行为，使用我们自定义的窗口\r\n  });\r\n  // win.webContents.on('will-navigate', (event, url) => { }) #344\r\n\r\n  // 窗口进入全屏状态时触发\r\n  win.on(\"enter-full-screen\", () => {\r\n    createMenu(\"退出全屏幕\");\r\n  });\r\n\r\n  // 窗口离开全屏状态时触发\r\n  win.on(\"leave-full-screen\", () => {\r\n    createMenu();\r\n  });\r\n\r\n  // 检查是否启用RabbitMQ功能\r\n  // 在开发环境中，环境变量可能需要从不同的地方读取\r\n  const rabbitmqEnabled = process.env.VITE_RABBITMQ_ENABLED === 'true' ||\r\n                          process.env.NODE_ENV === 'development'; // 开发环境默认启用\r\n\r\n  console.log('RabbitMQ环境变量检查:', {\r\n    VITE_RABBITMQ_ENABLED: process.env.VITE_RABBITMQ_ENABLED,\r\n    NODE_ENV: process.env.NODE_ENV,\r\n    rabbitmqEnabled\r\n  });\r\n\r\n  if (rabbitmqEnabled) {\r\n    console.log('RabbitMQ功能已启用，开始初始化...');\r\n    initializeRabbitMQ();\r\n  } else {\r\n    console.log('RabbitMQ功能已禁用');\r\n  }\r\n}\r\n\r\nasync function createSplashWindow() {\r\n  splashWin = new BrowserWindow({\r\n    width: 600, // Smaller size for splash\r\n    height: 400, // Smaller size for splash\r\n    frame: false, // No window frame\r\n    resizable: false,\r\n    movable: true, // Or false if you want it perfectly centered and static\r\n    center: true,\r\n    title: \"Loading...\", // Optional, won't be visible\r\n    icon: join(process.env.PUBLIC, \"favicon.ico\"), // Keep icon consistent\r\n    webPreferences: {\r\n      preload,\r\n      nodeIntegration: false, // Recommended for security\r\n      contextIsolation: true, // Required for contextBridge\r\n    },\r\n  });\r\n\r\n  const splashTargetUrl = `${url}#/start`; // Always load /start for splash\r\n  const splashIndexHtml = { pathname: indexHtml, hash: '/start' }; // Always load /start for splash\r\n\r\n  if (process.env.VITE_DEV_SERVER_URL) {\r\n    splashWin.loadURL(splashTargetUrl); \r\n  } else {\r\n    splashWin.loadFile(splashIndexHtml.pathname, { hash: splashIndexHtml.hash });\r\n  }\r\n\r\n  // No menu for splash screen\r\n  // No dev tools for splash screen by default, can be enabled for debugging\r\n}\r\n\r\napp.whenReady().then(createSplashWindow); // Start with splash screen\r\n\r\napp.on(\"window-all-closed\", () => {\r\n  // Splash window might be closed before main window, ensure 'win' refers to mainWindow\r\n  if (BrowserWindow.getAllWindows().length === 0) { // Check if all windows are closed\r\n    app.quit();\r\n  }\r\n});\r\n\r\napp.on(\"second-instance\", () => {\r\n  // This logic might need adjustment depending on whether splash or main window should get focus\r\n  if (win && !win.isDestroyed()) { // If main window exists and is not destroyed\r\n    // Focus on the main window if the user tried to open another\r\n    if (win.isMinimized()) win.restore();\r\n    win.focus();\r\n  } else if (splashWin && !splashWin.isDestroyed()) { // If only splash window exists and is not destroyed\r\n    if (splashWin.isMinimized()) splashWin.restore();\r\n    splashWin.focus();\r\n  }\r\n});\r\n\r\napp.on(\"activate\", () => {\r\n  // On macOS it's common to re-create a window in the app when the\r\n  // dock icon is clicked and there are no other windows open.\r\n  const allWindows = BrowserWindow.getAllWindows();\r\n  if (allWindows.length === 0) {\r\n    createSplashWindow(); // If no windows, always start with splash\r\n  } else {\r\n    // If windows exist, try to focus the main one, then splash\r\n    if (win && !win.isDestroyed()) {\r\n      if (win.isMinimized()) win.restore();\r\n      win.focus();\r\n    } else if (splashWin && !splashWin.isDestroyed()) {\r\n      if (splashWin.isMinimized()) splashWin.restore();\r\n      splashWin.focus();\r\n    } else {\r\n      // Fallback if recorded windows are destroyed for some reason\r\n      allWindows[0].focus();\r\n    }\r\n  }\r\n});\r\n\r\n// Listener to close splash and open main window\r\nipcMain.on('APP_READY_TO_SHOW_MAIN_WINDOW', (event, args: {\r\n  targetRoute?: string,\r\n  openedFilePath?: string,\r\n  singleFileMode?: boolean,\r\n  clearCache?: boolean\r\n} = {}) => {\r\n  if (splashWin) {\r\n    splashWin.close();\r\n    splashWin = null;\r\n  }\r\n  createMainWindow(args.targetRoute); // Pass targetRoute to createMainWindow\r\n\r\n  // If there's a file to open, send it to the main window\r\n  if (args.openedFilePath) {\r\n    console.log('Main process: Preparing to send file data:', {\r\n      filePath: args.openedFilePath,\r\n      targetRoute: args.targetRoute,\r\n      singleFileMode: args.singleFileMode\r\n    });\r\n\r\n    const sendFileData = () => {\r\n      console.log('Main process: Sending file data events');\r\n\r\n      if (args.targetRoute?.includes('/dataManagement/imandex')) {\r\n        // For Excel files\r\n        console.log('Main process: Sending excel-file-selected event');\r\n        win?.webContents.send('excel-file-selected', args.openedFilePath);\r\n      } else {\r\n        // For other files\r\n        console.log('Main process: Sending workspace-file-selected event');\r\n        win?.webContents.send('workspace-file-selected', args.openedFilePath);\r\n      }\r\n\r\n      if (args.singleFileMode) {\r\n        console.log('Main process: Sending set-single-file-mode event');\r\n        win?.webContents.send('set-single-file-mode', args.openedFilePath);\r\n      }\r\n    };\r\n\r\n    win?.webContents.once('did-finish-load', sendFileData);\r\n    win?.webContents.once('dom-ready', sendFileData);\r\n    setTimeout(sendFileData, 1000);\r\n  }\r\n});\r\n\r\n// 菜单栏 https://www.electronjs.org/zh/docs/latest/api/menu-item#%E8%8F%9C%E5%8D%95%E9%A1%B9\r\nconst appMenu = (fullscreenLabel: string) => {\r\n  const menuItems: MenuItemConstructorOptions[] = [\r\n    { label: \"关于\", role: \"about\" },\r\n    { label: \"开发者工具\", role: \"toggleDevTools\" },\r\n    { label: \"强制刷新\", role: \"forceReload\" },\r\n    // Quit is moved to File menu\r\n  ];\r\n  if (!isDev) {\r\n    const devToolsIndex = menuItems.findIndex(item => item.role === 'toggleDevTools');\r\n    if (devToolsIndex > -1) menuItems.splice(devToolsIndex, 1);\r\n    const forceReloadIndex = menuItems.findIndex(item => item.role === 'forceReload');\r\n    if (forceReloadIndex > -1) menuItems.splice(forceReloadIndex, 1);\r\n  }\r\n\r\n  const template: Array<MenuItemConstructorOptions | MenuItem> = [\r\n    {\r\n      label: app.name,\r\n      submenu: menuItems\r\n    },\r\n    {\r\n      label: \"文件\",\r\n      submenu: [\r\n        {\r\n          label: \"导入项目...\",\r\n          accelerator: \"CmdOrCtrl+Shift+O\",\r\n          click: async () => {\r\n            if (win && !win.isDestroyed()) {\r\n              win.focus();\r\n              win.webContents.send('menu-triggered-import-project');\r\n            } else {\r\n              // Determine parent for dialog if only splash is open\r\n              const parentWindow = (splashWin && !splashWin.isDestroyed()) ? splashWin : undefined;\r\n              const dialogOptions: Electron.OpenDialogOptions = { properties: ['openDirectory'] };\r\n              const directoryPathResult = parentWindow \r\n                ? await dialog.showOpenDialog(parentWindow, dialogOptions)\r\n                : await dialog.showOpenDialog(dialogOptions);\r\n\r\n              if (!directoryPathResult.canceled && directoryPathResult.filePaths.length > 0) {\r\n                const projectPath = directoryPathResult.filePaths[0];\r\n                if (splashWin && !splashWin.isDestroyed()) { splashWin.close(); splashWin = null; }\r\n                createMainWindow(`/workspace/${encodeURIComponent(projectPath)}`);\r\n              }\r\n            }\r\n          }\r\n        },\r\n        {\r\n          label: \"打开文件...\",\r\n          accelerator: \"CmdOrCtrl+O\",\r\n          click: async () => {\r\n            if (win && !win.isDestroyed()) {\r\n              win.focus();\r\n              win.webContents.send('menu-triggered-open-file');\r\n            } else {\r\n              const parentWindow = (splashWin && !splashWin.isDestroyed()) ? splashWin : undefined;\r\n              const dialogOptions: Electron.OpenDialogOptions = { properties: ['openFile'] };\r\n              const filePathResult = parentWindow\r\n                ? await dialog.showOpenDialog(parentWindow, dialogOptions)\r\n                : await dialog.showOpenDialog(dialogOptions);\r\n\r\n              if (!filePathResult.canceled && filePathResult.filePaths.length > 0) {\r\n                const filePath = filePathResult.filePaths[0];\r\n                if (splashWin && !splashWin.isDestroyed()) { splashWin.close(); splashWin = null; }\r\n\r\n                // Check if it's an Excel file\r\n                const isExcelFile = /\\.(xlsx|xls|csv)$/i.test(filePath);\r\n\r\n                if (isExcelFile) {\r\n                  // For Excel files, navigate to dataImandEx page\r\n                  createMainWindow(`/dataManagement/imandex`);\r\n                  // Send the file path to be opened\r\n                  const sendExcelFile = () => {\r\n                    win?.webContents.send('excel-file-selected', filePath);\r\n                    win?.webContents.send('set-single-file-mode', filePath);\r\n                  };\r\n                  win?.webContents.once('did-finish-load', sendExcelFile);\r\n                  win?.webContents.once('dom-ready', sendExcelFile);\r\n                  setTimeout(sendExcelFile, 1000);\r\n                } else {\r\n                  // For other files, create a workspace with the file\r\n                  const fileDir = filePath.substring(0, filePath.lastIndexOf('/') || filePath.lastIndexOf('\\\\'));\r\n                  createMainWindow(`/workspace/${encodeURIComponent(fileDir)}`);\r\n                  // Use multiple event listeners to ensure the message is received\r\n                  const sendWorkspaceFile = () => {\r\n                    win?.webContents.send('workspace-file-selected', filePath);\r\n                    win?.webContents.send('set-single-file-mode', filePath);\r\n                  };\r\n                  win?.webContents.once('did-finish-load', sendWorkspaceFile);\r\n                  win?.webContents.once('dom-ready', sendWorkspaceFile);\r\n                  // Also send after a delay to ensure components are mounted\r\n                  setTimeout(sendWorkspaceFile, 1000);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        { type: \"separator\" },\r\n        { label: \"退出\", role: \"quit\" }\r\n      ]\r\n    },\r\n    {\r\n      label: \"编辑\",\r\n      submenu: [\r\n        { label: \"撤销\", role: \"undo\" },\r\n        { label: \"重做\", role: \"redo\" },\r\n        { type: \"separator\" },\r\n        { label: \"剪切\", role: \"cut\" },\r\n        { label: \"复制\", role: \"copy\" },\r\n        { label: \"粘贴\", role: \"paste\" },\r\n        { label: \"删除\", role: \"delete\" },\r\n        { label: \"全选\", role: \"selectAll\" }\r\n      ]\r\n    },\r\n    {\r\n      label: \"显示\",\r\n      submenu: [\r\n        { label: \"加大\", role: \"zoomIn\" },\r\n        { label: \"默认大小\", role: \"resetZoom\" },\r\n        { label: \"缩小\", role: \"zoomOut\" },\r\n        { type: \"separator\" },\r\n        {\r\n          label: fullscreenLabel,\r\n          role: \"togglefullscreen\"\r\n        }\r\n      ]\r\n    }\r\n  ];\r\n\r\n  // Ensure app.name submenu doesn't also have Quit if it's in File menu\r\n  const appNameSubmenu = template[0].submenu as MenuItemConstructorOptions[] | undefined;\r\n  if (Array.isArray(appNameSubmenu)) {\r\n    const quitItemIndex = appNameSubmenu.findIndex(item => item.role === 'quit');\r\n    const fileSubMenu = template[1].submenu as MenuItemConstructorOptions[] | undefined;\r\n    if (quitItemIndex > -1 && Array.isArray(fileSubMenu) && fileSubMenu.find(item => item.role === 'quit')) {\r\n      appNameSubmenu.splice(quitItemIndex, 1);\r\n    }\r\n  }\r\n  return template;\r\n};\r\n\r\n// New window example arg: new windows url\r\nipcMain.handle(\"open-win\", (_, arg) => {\r\n  const childWindow = new BrowserWindow({\r\n    webPreferences: {\r\n      preload,\r\n      nodeIntegration: false, // Then these should also be updated\r\n      contextIsolation: true\r\n    }\r\n  });\r\n\r\n  if (process.env.VITE_DEV_SERVER_URL) {\r\n    childWindow.loadURL(`${url}#${arg}`);\r\n  } else {\r\n    childWindow.loadFile(indexHtml, { hash: arg });\r\n  }\r\n});\r\n\r\n// // 在文件末尾添加以下 IPC 事件监听器\r\n// ipcMain.handle('select-directory', async () => {\r\n//   const result = await dialog.showOpenDialog({\r\n//     properties: ['openDirectory']\r\n//   })\r\n//   return result.filePaths[0]\r\n// })\r\n\r\n// ipcMain.handle('create-directory', (_, path: string) => {\r\n//   if (!existsSync(path)) {\r\n//     mkdirSync(path, { recursive: true })\r\n//     return true\r\n//   }\r\n//   return false\r\n// })\r\n\r\n// ipcMain.handle('read-file', (_, filePath: string) => {\r\n//   return readFileSync(filePath, 'utf-8')\r\n// })\r\n\r\n// ipcMain.handle('write-file', (_, filePath: string, content: string) => {\r\n//   writeFileSync(filePath, content)\r\n//   return true\r\n// })\r\n\r\n// ipcMain.handle('read-directory', (_, dirPath: string) => {\r\n//   try {\r\n//     return readdirSync(dirPath).map(file => {\r\n//       const fullPath = join(dirPath, file)\r\n//       const stats: Stats = statSync(fullPath)\r\n//       return {\r\n//         name: file,\r\n//         path: fullPath,\r\n//         isDirectory: stats.isDirectory(),\r\n//         size: stats.size,\r\n//         modified: stats.mtime\r\n//       }\r\n//     })\r\n//   } catch (error) {\r\n//     console.error('Error reading directory:', error)\r\n//     return []\r\n//   }\r\n// })\r\n\r\n// 在文件顶部导入所需模块\r\nimport fs from 'fs';\r\nimport path from 'path';\r\nimport { createRequire } from 'node:module';\r\nimport { RabbitMQNotificationService } from '../services/rabbitmq';\r\n\r\n// Create require function for dynamic imports\r\nconst require = createRequire(import.meta.url);\r\n\r\n// 在文件末尾（ipcMain.handle(\"open-win\"...)之后）添加以下代码\r\n\r\n// 选择目录对话框\r\nipcMain.handle('dialog:openDirectory', async () => {\r\n  const result = await dialog.showOpenDialog({\r\n    properties: ['openDirectory']\r\n  });\r\n  return result.filePaths[0]; // 返回选择的第一个路径\r\n});\r\n\r\n// 选择文件对话框\r\nipcMain.handle('dialog:openFile', async () => {\r\n  const result = await dialog.showOpenDialog({\r\n    properties: ['openFile'],\r\n    // You can add filters, e.g., for specific file types\r\n    // filters: [\r\n    //   { name: 'Text Files', extensions: ['txt', 'md'] },\r\n    //   { name: 'All Files', extensions: ['*'] }\r\n    // ]\r\n  });\r\n  if (result.canceled || result.filePaths.length === 0) {\r\n    return null; // Or handle as preferred\r\n  }\r\n  return result.filePaths[0]; // 返回选择的第一个路径\r\n});\r\n\r\n// 读取目录内容\r\nipcMain.handle('fs:readDirectory', async (_, dirPath: string) => {\r\n  const files = await fs.promises.readdir(dirPath, { withFileTypes: true });\r\n  return files.map(dirent => ({\r\n    name: dirent.name,\r\n    isDirectory: dirent.isDirectory(),\r\n    path: path.join(dirPath, dirent.name)\r\n  }));\r\n});\r\n\r\n// 创建新目录\r\nipcMain.handle('fs:createDirectory', async (_, targetPath: string) => {\r\n  await fs.promises.mkdir(targetPath, { recursive: true });\r\n  return { success: true };\r\n});\r\n\r\n// 创建新文件\r\nipcMain.handle('fs:createFile', async (_, filePath: string) => {\r\n  await fs.promises.writeFile(filePath, '');\r\n  return { success: true };\r\n});\r\n\r\n// 删除文件/目录\r\nipcMain.handle('fs:deletePath', async (_, targetPath: string) => {\r\n  const stats = await fs.promises.stat(targetPath);\r\n  if (stats.isDirectory()) {\r\n    await fs.promises.rmdir(targetPath, { recursive: true });\r\n  } else {\r\n    await fs.promises.unlink(targetPath);\r\n  }\r\n  return { success: true };\r\n});\r\n\r\n// 检测文件类型\r\nconst getFileType = (filePath: string): { type: string; category: string; supported: boolean } => {\r\n  const ext = path.extname(filePath).toLowerCase();\r\n\r\n  // 文本文件\r\n  const textExtensions = ['.txt', '.md', '.json', '.xml', '.html', '.css', '.js', '.ts', '.vue', '.py', '.java', '.cpp', '.c', '.h', '.sql', '.log', '.ini', '.cfg', '.conf', '.yaml', '.yml'];\r\n\r\n  // Office 文档\r\n  const officeExtensions = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'];\r\n\r\n  // PDF 文档\r\n  const pdfExtensions = ['.pdf'];\r\n\r\n  // 图片文件\r\n  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.ico'];\r\n\r\n  // 压缩文件\r\n  const archiveExtensions = ['.zip', '.rar', '.7z', '.tar', '.gz'];\r\n\r\n  // 可执行文件\r\n  const executableExtensions = ['.exe', '.msi', '.dmg', '.app', '.deb', '.rpm'];\r\n\r\n  // 音视频文件\r\n  const mediaExtensions = ['.mp3', '.mp4', '.avi', '.mov', '.wav', '.flac'];\r\n\r\n  if (textExtensions.includes(ext)) {\r\n    return { type: ext, category: 'text', supported: true };\r\n  } else if (officeExtensions.includes(ext)) {\r\n    return { type: ext, category: 'office', supported: true };\r\n  } else if (pdfExtensions.includes(ext)) {\r\n    return { type: ext, category: 'pdf', supported: true };\r\n  } else if (imageExtensions.includes(ext)) {\r\n    return { type: ext, category: 'image', supported: true };\r\n  } else if (archiveExtensions.includes(ext)) {\r\n    return { type: ext, category: 'archive', supported: false };\r\n  } else if (executableExtensions.includes(ext)) {\r\n    return { type: ext, category: 'executable', supported: false };\r\n  } else if (mediaExtensions.includes(ext)) {\r\n    return { type: ext, category: 'media', supported: false };\r\n  } else {\r\n    return { type: ext, category: 'unknown', supported: false };\r\n  }\r\n};\r\n\r\n// 读取文件内容\r\nipcMain.handle('fs:readFile', async (_, filePath: string) => {\r\n  try {\r\n    const content = await fs.promises.readFile(filePath, 'utf-8');\r\n    return content;\r\n  } catch (error) {\r\n    console.error('Error reading file:', error);\r\n    throw error;\r\n  }\r\n});\r\n\r\n// 检测文件类型和读取内容\r\nipcMain.handle('fs:readFileWithType', async (_, filePath: string) => {\r\n  try {\r\n    const fileInfo = getFileType(filePath);\r\n\r\n    if (!fileInfo.supported) {\r\n      return {\r\n        success: false,\r\n        fileInfo,\r\n        error: `不支持的文件类型: ${fileInfo.type}`,\r\n        message: getUnsupportedMessage(fileInfo)\r\n      };\r\n    }\r\n\r\n    let content = '';\r\n    let imageData = null;\r\n\r\n    if (fileInfo.category === 'text') {\r\n      // 直接读取文本文件\r\n      content = await fs.promises.readFile(filePath, 'utf-8');\r\n    } else if (fileInfo.category === 'office') {\r\n      // Office 文档需要特殊处理\r\n      if (fileInfo.type === '.docx') {\r\n        content = await extractDocxText(filePath);\r\n      } else if (fileInfo.type === '.doc') {\r\n        content = '暂不支持 .doc 格式，请转换为 .docx 格式';\r\n      } else {\r\n        content = `不支持的Office 文档 (${fileInfo.type})，请使用Office工具进行编辑`;\r\n      }\r\n    } else if (fileInfo.category === 'pdf') {\r\n      content = 'PDF 文档，暂不支持文本提取';\r\n    } else if (fileInfo.category === 'image') {\r\n      // 读取图像文件为 base64\r\n      const imageBuffer = await fs.promises.readFile(filePath);\r\n      const base64Data = imageBuffer.toString('base64');\r\n      const mimeType = getMimeType(fileInfo.type);\r\n      imageData = `data:${mimeType};base64,${base64Data}`;\r\n      content = ''; // 图像文件不需要文本内容\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      fileInfo,\r\n      content,\r\n      imageData\r\n    };\r\n  } catch (error: any) {\r\n    console.error('Error reading file with type:', error);\r\n    return {\r\n      success: false,\r\n      fileInfo: getFileType(filePath),\r\n      error: error?.message || 'Unknown error',\r\n      message: '读取文件时发生错误'\r\n    };\r\n  }\r\n});\r\n\r\n// 获取图像文件的 MIME 类型\r\nconst getMimeType = (extension: string): string => {\r\n  const mimeTypes: Record<string, string> = {\r\n    '.jpg': 'image/jpeg',\r\n    '.jpeg': 'image/jpeg',\r\n    '.png': 'image/png',\r\n    '.gif': 'image/gif',\r\n    '.bmp': 'image/bmp',\r\n    '.svg': 'image/svg+xml',\r\n    '.ico': 'image/x-icon',\r\n    '.webp': 'image/webp'\r\n  };\r\n  return mimeTypes[extension.toLowerCase()] || 'image/jpeg';\r\n};\r\n\r\n// 获取不支持文件类型的提示信息\r\nconst getUnsupportedMessage = (fileInfo: { type: string; category: string }) => {\r\n  switch (fileInfo.category) {\r\n    case 'image':\r\n      return '图片文件不支持文本编辑，请使用图片查看器打开';\r\n    case 'archive':\r\n      return '压缩文件不支持直接编辑，请先解压缩';\r\n    case 'executable':\r\n      return '可执行文件不支持编辑';\r\n    case 'media':\r\n      return '音视频文件不支持文本编辑，请使用媒体播放器打开';\r\n    default:\r\n      return '不支持的文件类型，无法在文本编辑器中打开';\r\n  }\r\n};\r\n\r\n// 提取 DOCX 文档的文本内容\r\nconst extractDocxText = async (filePath: string): Promise<string> => {\r\n  try {\r\n    // Try to use mammoth if available\r\n    try {\r\n      const mammoth = require('mammoth');\r\n      const result = await mammoth.extractRawText({ path: filePath });\r\n      return result.value || '无法提取文档内容';\r\n    } catch (mammothError) {\r\n      console.log('Mammoth not available, using fallback method');\r\n\r\n      // Fallback: Try to read as zip and extract document.xml\r\n      try {\r\n        const AdmZip = require('adm-zip');\r\n        const zip = new AdmZip(filePath);\r\n        const documentXml = zip.readAsText('word/document.xml');\r\n\r\n        if (documentXml) {\r\n          // Simple XML text extraction (removes tags)\r\n          const textContent = documentXml\r\n            .replace(/<[^>]*>/g, ' ')  // Remove XML tags\r\n            .replace(/\\s+/g, ' ')      // Normalize whitespace\r\n            .trim();\r\n\r\n          return textContent || '文档内容为空';\r\n        }\r\n      } catch (zipError: any) {\r\n        console.log('ZIP extraction failed:', zipError?.message || 'Unknown error');\r\n      }\r\n\r\n      return `Word 文档预览\\n\\n文件路径: ${filePath}\\n\\n注意：无法提取文档内容，请使用 Microsoft Word 或其他兼容软件打开此文件。\\n\\n要完整支持 Word 文档，请安装 mammoth 库：npm install mammoth`;\r\n    }\r\n  } catch (error: any) {\r\n    console.error('Error extracting DOCX text:', error);\r\n    return `Word 文档预览\\n\\n文件路径: ${filePath}\\n\\n错误：无法读取文档内容 - ${error?.message || 'Unknown error'}`;\r\n  }\r\n};\r\n\r\n// 读取文件内容为Buffer (用于Excel等二进制文件)\r\nipcMain.handle('fs:readFileBuffer', async (_, filePath: string) => {\r\n  try {\r\n    const buffer = await fs.promises.readFile(filePath);\r\n    return buffer;\r\n  } catch (error) {\r\n    console.error('Error reading file buffer:', error);\r\n    throw error;\r\n  }\r\n});\r\n\r\n// 写入文件内容\r\nipcMain.handle('fs:writeFile', async (_, filePath: string, content: string) => {\r\n  try {\r\n    await fs.promises.writeFile(filePath, content, 'utf-8');\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error('Error writing file:', error);\r\n    throw error;\r\n  }\r\n});\r\n\r\n// Handle workspace file selection communication\r\nipcMain.on('workspace-file-selected', (event, filePath: string) => {\r\n  // Forward the event to all renderer processes (in case there are multiple windows)\r\n  if (win && !win.isDestroyed()) {\r\n    win.webContents.send('workspace-file-selected', filePath);\r\n  }\r\n});\r\n\r\n// Handle Excel file selection from workspace\r\nipcMain.on('excel-file-selected', (event, filePath: string) => {\r\n  // Forward the event to all renderer processes\r\n  if (win && !win.isDestroyed()) {\r\n    win.webContents.send('excel-file-selected', filePath);\r\n  }\r\n});\r\n\r\n// Handle app quit request\r\nipcMain.on('app-quit', () => {\r\n  app.quit();\r\n});\r\n\r\n// 初始化 RabbitMQ 通知服务\r\nasync function initializeRabbitMQ() {\r\n  try {\r\n    if (!win) {\r\n      throw new Error('Main window not available for RabbitMQ service');\r\n    }\r\n    rabbitMQService = new RabbitMQNotificationService(win);\r\n\r\n    // 从环境变量构建RabbitMQ连接URL\r\n    const host = process.env.VITE_RABBITMQ_HOST;\r\n    const port = process.env.VITE_RABBITMQ_PORT;\r\n    const username = process.env.VITE_RABBITMQ_USERNAME;\r\n    const password = process.env.VITE_RABBITMQ_PASSWORD;\r\n    const vhost = process.env.VITE_RABBITMQ_VHOST;\r\n\r\n    const rabbitmqUrl = `amqp://${username}:${password}@${host}:${port}${vhost === '/' ? '' : '/' + vhost}`;\r\n    console.log('Connecting to RabbitMQ at:', `amqp://${username}:***@${host}:${port}${vhost === '/' ? '' : '/' + vhost}`);\r\n\r\n    // 尝试连接到 RabbitMQ\r\n    const connected = await rabbitMQService.connect(rabbitmqUrl);\r\n\r\n    if (connected) {\r\n      console.log('RabbitMQ notification service initialized successfully');\r\n\r\n      // 通知前端 RabbitMQ 已连接\r\n      if (win && !win.isDestroyed()) {\r\n        win.webContents.send('rabbitmq-connected', { connected: true });\r\n      }\r\n    } else {\r\n      console.warn('Failed to connect to RabbitMQ, notifications may not work');\r\n\r\n      // 通知前端 RabbitMQ 连接失败\r\n      if (win && !win.isDestroyed()) {\r\n        win.webContents.send('rabbitmq-connected', {\r\n          connected: false,\r\n          error: 'Failed to connect to RabbitMQ server'\r\n        });\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('Error initializing RabbitMQ notification service:', error);\r\n\r\n    // 通知前端初始化失败\r\n    if (win && !win.isDestroyed()) {\r\n      win.webContents.send('rabbitmq-connected', {\r\n        connected: false,\r\n        error: (error as Error).message || 'Unknown error'\r\n      });\r\n    }\r\n  }\r\n}\r\n\r\n// RabbitMQ 通知相关的 IPC 处理程序\r\n\r\n// 获取 RabbitMQ 连接状态\r\nipcMain.handle('rabbitmq:get-connection-status', async () => {\r\n  try {\r\n    if (!rabbitMQService) {\r\n      return {\r\n        connected: false,\r\n        error: 'RabbitMQ notification service not initialized'\r\n      };\r\n    }\r\n\r\n    const connectionInfo = rabbitMQService.getConnectionInfo();\r\n\r\n    return {\r\n      connected: rabbitMQService.isConnectionActive(),\r\n      ...connectionInfo\r\n    };\r\n  } catch (error) {\r\n    console.error('Error getting connection status:', error);\r\n    return {\r\n      connected: false,\r\n      error: (error as Error).message || 'Unknown error'\r\n    };\r\n  }\r\n});\r\n\r\n// 测试通知功能（开发用）\r\nipcMain.handle('rabbitmq:test-notification', async () => {\r\n  try {\r\n    if (!rabbitMQService) {\r\n      throw new Error('RabbitMQ notification service not available');\r\n    }\r\n\r\n    await rabbitMQService.testNotification();\r\n\r\n    return {\r\n      success: true,\r\n      message: '测试通知已发送'\r\n    };\r\n  } catch (error) {\r\n    console.error('Error sending test notification:', error);\r\n    return {\r\n      success: false,\r\n      error: (error as Error).message || 'Unknown error'\r\n    };\r\n  }\r\n});\r\n\r\n// 应用退出时清理 RabbitMQ 连接\r\napp.on('before-quit', async () => {\r\n  if (rabbitMQService) {\r\n    console.log('Disconnecting from RabbitMQ...');\r\n    await rabbitMQService.disconnect();\r\n  }\r\n});"], "names": ["querystringify", "querystringify_1", "require$$0", "require$$1", "url", "global", "path", "suffix", "channel", "codec", "defs", "frame", "self", "format", "require$$2", "format_1", "require$$4", "require$$5", "require$$6", "require$$7", "require$$8", "require$$9", "error", "connection", "require$$3", "credentials", "connect", "connect_1", "fields", "__filename", "__dirname", "window", "fileURLToPath", "require"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,iBAAiB,SAAS,SAAS,MAAM,UAAU;AACjD,eAAW,SAAS,MAAM,GAAG,EAAE,CAAC;AAChC,WAAO,CAAC;AAER,QAAI,CAAC,KAAM,QAAO;AAElB,YAAQ,UAAQ;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AACL,eAAO,SAAS;AAAA,MAEhB,KAAK;AAAA,MACL,KAAK;AACL,eAAO,SAAS;AAAA,MAEhB,KAAK;AACL,eAAO,SAAS;AAAA,MAEhB,KAAK;AACL,eAAO,SAAS;AAAA,MAEhB,KAAK;AACL,eAAO;AAAA,IACX;AAEE,WAAO,SAAS;AAAA,EACjB;;;;;;;;ACnCD,MAAI,MAAM,OAAO,UAAU,gBACvB;AASJ,WAAS,OAAO,OAAO;AACrB,QAAI;AACF,aAAO,mBAAmB,MAAM,QAAQ,OAAO,GAAG,CAAC;AAAA,IACpD,SAAQ,GAAG;AACV,aAAO;AAAA,IACX;AAAA,EACA;AASA,WAAS,OAAO,OAAO;AACrB,QAAI;AACF,aAAO,mBAAmB,KAAK;AAAA,IAChC,SAAQ,GAAG;AACV,aAAO;AAAA,IACX;AAAA,EACA;AASA,WAAS,YAAY,OAAO;AAC1B,QAAI,SAAS,wBACT,SAAS,CAAA,GACT;AAEJ,WAAO,OAAO,OAAO,KAAK,KAAK,GAAG;AAChC,UAAI,MAAM,OAAO,KAAK,CAAC,CAAC,GACpB,QAAQ,OAAO,KAAK,CAAC,CAAC;AAU1B,UAAI,QAAQ,QAAQ,UAAU,QAAQ,OAAO,OAAQ;AACrD,aAAO,GAAG,IAAI;AAAA,IAClB;AAEE,WAAO;AAAA,EACT;AAUA,WAASA,iBAAe,KAAK,QAAQ;AACnC,aAAS,UAAU;AAEnB,QAAI,QAAQ,CAAA,GACR,OACA;AAKJ,QAAI,aAAa,OAAO,OAAQ,UAAS;AAEzC,SAAK,OAAO,KAAK;AACf,UAAI,IAAI,KAAK,KAAK,GAAG,GAAG;AACtB,gBAAQ,IAAI,GAAG;AAMf,YAAI,CAAC,UAAU,UAAU,QAAQ,UAAU,SAAS,MAAM,KAAK,IAAI;AACjE,kBAAQ;AAAA,QAChB;AAEM,cAAM,OAAO,GAAG;AAChB,gBAAQ,OAAO,KAAK;AAMpB,YAAI,QAAQ,QAAQ,UAAU,KAAM;AACpC,cAAM,KAAK,MAAK,MAAK,KAAK;AAAA,MAChC;AAAA,IACA;AAEE,WAAO,MAAM,SAAS,SAAS,MAAM,KAAK,GAAG,IAAI;AAAA,EACnD;AAKAC,iBAAA,YAAoBD;AACpBC,iBAAA,QAAgB;;;;;;;;ACnHhB,MAAI,WAAWC,oBAAA,GACX,KAAKC,sBAAA,GACL,sBAAsB,8EACtB,SAAS,aACT,UAAU,iCACV,OAAO,SACP,aAAa,oDACb,qBAAqB;AAUzB,WAAS,SAAS,KAAK;AACrB,YAAQ,MAAM,MAAM,IAAI,SAAQ,EAAG,QAAQ,qBAAqB,EAAE;AAAA,EACpE;AAcA,MAAI,QAAQ;AAAA,IACV,CAAC,KAAK,MAAM;AAAA;AAAA,IACZ,CAAC,KAAK,OAAO;AAAA;AAAA,IACb,SAAS,SAAS,SAASC,MAAK;AAC9B,aAAO,UAAUA,KAAI,QAAQ,IAAI,QAAQ,QAAQ,OAAO,GAAG,IAAI;AAAA,IAChE;AAAA,IACD,CAAC,KAAK,UAAU;AAAA;AAAA,IAChB,CAAC,KAAK,QAAQ,CAAC;AAAA;AAAA,IACf,CAAC,KAAK,QAAQ,QAAW,GAAG,CAAC;AAAA;AAAA,IAC7B,CAAC,WAAW,QAAQ,QAAW,CAAC;AAAA;AAAA,IAChC,CAAC,KAAK,YAAY,QAAW,GAAG,CAAC;AAAA;AAAA,EAClC;AAUD,MAAI,SAAS,EAAE,MAAM,GAAG,OAAO,EAAG;AAclC,WAAS,UAAU,KAAK;AACtB,QAAI;AAEJ,QAAI,OAAO,WAAW,YAAa,aAAY;AAAA,aACtC,OAAOC,mBAAW,YAAa,aAAYA;AAAAA,aAC3C,OAAO,SAAS,YAAa,aAAY;AAAA,QAC7C,aAAY,CAAE;AAEnB,QAAI,WAAW,UAAU,YAAY,CAAE;AACvC,UAAM,OAAO;AAEb,QAAI,mBAAmB,CAAA,GACnB,OAAO,OAAO,KACd;AAEJ,QAAI,YAAY,IAAI,UAAU;AAC5B,yBAAmB,IAAI,IAAI,SAAS,IAAI,QAAQ,GAAG,EAAE;AAAA,IACzD,WAAa,aAAa,MAAM;AAC5B,yBAAmB,IAAI,IAAI,KAAK,EAAE;AAClC,WAAK,OAAO,OAAQ,QAAO,iBAAiB,GAAG;AAAA,IACnD,WAAa,aAAa,MAAM;AAC5B,WAAK,OAAO,KAAK;AACf,YAAI,OAAO,OAAQ;AACnB,yBAAiB,GAAG,IAAI,IAAI,GAAG;AAAA,MACrC;AAEI,UAAI,iBAAiB,YAAY,QAAW;AAC1C,yBAAiB,UAAU,QAAQ,KAAK,IAAI,IAAI;AAAA,MACtD;AAAA,IACA;AAEE,WAAO;AAAA,EACT;AASA,WAAS,UAAU,QAAQ;AACzB,WACE,WAAW,WACX,WAAW,UACX,WAAW,WACX,WAAW,YACX,WAAW,SACX,WAAW;AAAA,EAEf;AAkBA,WAAS,gBAAgB,SAAS,UAAU;AAC1C,cAAU,SAAS,OAAO;AAC1B,cAAU,QAAQ,QAAQ,QAAQ,EAAE;AACpC,eAAW,YAAY,CAAE;AAEzB,QAAI,QAAQ,WAAW,KAAK,OAAO;AACnC,QAAI,WAAW,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,YAAW,IAAK;AACnD,QAAI,iBAAiB,CAAC,CAAC,MAAM,CAAC;AAC9B,QAAI,eAAe,CAAC,CAAC,MAAM,CAAC;AAC5B,QAAI,eAAe;AACnB,QAAI;AAEJ,QAAI,gBAAgB;AAClB,UAAI,cAAc;AAChB,eAAO,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC;AACpC,uBAAe,MAAM,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE;AAAA,MAChD,OAAW;AACL,eAAO,MAAM,CAAC,IAAI,MAAM,CAAC;AACzB,uBAAe,MAAM,CAAC,EAAE;AAAA,MAC9B;AAAA,IACA,OAAS;AACL,UAAI,cAAc;AAChB,eAAO,MAAM,CAAC,IAAI,MAAM,CAAC;AACzB,uBAAe,MAAM,CAAC,EAAE;AAAA,MAC9B,OAAW;AACL,eAAO,MAAM,CAAC;AAAA,MACpB;AAAA,IACA;AAEE,QAAI,aAAa,SAAS;AACxB,UAAI,gBAAgB,GAAG;AACrB,eAAO,KAAK,MAAM,CAAC;AAAA,MACzB;AAAA,IACA,WAAa,UAAU,QAAQ,GAAG;AAC9B,aAAO,MAAM,CAAC;AAAA,IACf,WAAU,UAAU;AACnB,UAAI,gBAAgB;AAClB,eAAO,KAAK,MAAM,CAAC;AAAA,MACzB;AAAA,IACA,WAAa,gBAAgB,KAAK,UAAU,SAAS,QAAQ,GAAG;AAC5D,aAAO,MAAM,CAAC;AAAA,IAClB;AAEE,WAAO;AAAA,MACL;AAAA,MACA,SAAS,kBAAkB,UAAU,QAAQ;AAAA,MAC7C;AAAA,MACA;AAAA,IACD;AAAA,EACH;AAUA,WAAS,QAAQ,UAAU,MAAM;AAC/B,QAAI,aAAa,GAAI,QAAO;AAE5B,QAAIC,SAAQ,QAAQ,KAAK,MAAM,GAAG,EAAE,MAAM,GAAG,EAAE,EAAE,OAAO,SAAS,MAAM,GAAG,CAAC,GACvE,IAAIA,MAAK,QACT,OAAOA,MAAK,IAAI,CAAC,GACjB,UAAU,OACV,KAAK;AAET,WAAO,KAAK;AACV,UAAIA,MAAK,CAAC,MAAM,KAAK;AACnB,QAAAA,MAAK,OAAO,GAAG,CAAC;AAAA,MACjB,WAAUA,MAAK,CAAC,MAAM,MAAM;AAC3B,QAAAA,MAAK,OAAO,GAAG,CAAC;AAChB;AAAA,MACD,WAAU,IAAI;AACb,YAAI,MAAM,EAAG,WAAU;AACvB,QAAAA,MAAK,OAAO,GAAG,CAAC;AAChB;AAAA,MACN;AAAA,IACA;AAEE,QAAI,QAAS,CAAAA,MAAK,QAAQ,EAAE;AAC5B,QAAI,SAAS,OAAO,SAAS,KAAM,CAAAA,MAAK,KAAK,EAAE;AAE/C,WAAOA,MAAK,KAAK,GAAG;AAAA,EACtB;AAgBA,WAAS,IAAI,SAAS,UAAU,QAAQ;AACtC,cAAU,SAAS,OAAO;AAC1B,cAAU,QAAQ,QAAQ,QAAQ,EAAE;AAEpC,QAAI,EAAE,gBAAgB,MAAM;AAC1B,aAAO,IAAI,IAAI,SAAS,UAAU,MAAM;AAAA,IAC5C;AAEE,QAAI,UAAU,WAAW,OAAO,aAAa,OAAO,KAChD,eAAe,MAAM,MAAK,GAC1B,OAAO,OAAO,UACdF,OAAM,MACN,IAAI;AAaR,QAAI,aAAa,QAAQ,aAAa,MAAM;AAC1C,eAAS;AACT,iBAAW;AAAA,IACf;AAEE,QAAI,UAAU,eAAe,OAAO,OAAQ,UAAS,GAAG;AAExD,eAAW,UAAU,QAAQ;AAK7B,gBAAY,gBAAgB,WAAW,IAAI,QAAQ;AACnD,eAAW,CAAC,UAAU,YAAY,CAAC,UAAU;AAC7C,IAAAA,KAAI,UAAU,UAAU,WAAW,YAAY,SAAS;AACxD,IAAAA,KAAI,WAAW,UAAU,YAAY,SAAS,YAAY;AAC1D,cAAU,UAAU;AAMpB,QACE,UAAU,aAAa,YACrB,UAAU,iBAAiB,KAAK,mBAAmB,KAAK,OAAO,MAChE,CAAC,UAAU,YACT,UAAU,YACT,UAAU,eAAe,KACzB,CAAC,UAAUA,KAAI,QAAQ,IAC3B;AACA,mBAAa,CAAC,IAAI,CAAC,QAAQ,UAAU;AAAA,IACzC;AAEE,WAAO,IAAI,aAAa,QAAQ,KAAK;AACnC,oBAAc,aAAa,CAAC;AAE5B,UAAI,OAAO,gBAAgB,YAAY;AACrC,kBAAU,YAAY,SAASA,IAAG;AAClC;AAAA,MACN;AAEI,cAAQ,YAAY,CAAC;AACrB,YAAM,YAAY,CAAC;AAEnB,UAAI,UAAU,OAAO;AACnB,QAAAA,KAAI,GAAG,IAAI;AAAA,MACjB,WAAe,aAAa,OAAO,OAAO;AACpC,gBAAQ,UAAU,MACd,QAAQ,YAAY,KAAK,IACzB,QAAQ,QAAQ,KAAK;AAEzB,YAAI,CAAC,OAAO;AACV,cAAI,aAAa,OAAO,YAAY,CAAC,GAAG;AACtC,YAAAA,KAAI,GAAG,IAAI,QAAQ,MAAM,GAAG,KAAK;AACjC,sBAAU,QAAQ,MAAM,QAAQ,YAAY,CAAC,CAAC;AAAA,UACxD,OAAe;AACL,YAAAA,KAAI,GAAG,IAAI,QAAQ,MAAM,KAAK;AAC9B,sBAAU,QAAQ,MAAM,GAAG,KAAK;AAAA,UAC1C;AAAA,QACA;AAAA,MACK,WAAW,QAAQ,MAAM,KAAK,OAAO,GAAI;AACxC,QAAAA,KAAI,GAAG,IAAI,MAAM,CAAC;AAClB,kBAAU,QAAQ,MAAM,GAAG,MAAM,KAAK;AAAA,MAC5C;AAEI,MAAAA,KAAI,GAAG,IAAIA,KAAI,GAAG,MAChB,YAAY,YAAY,CAAC,IAAI,SAAS,GAAG,KAAK,KAAK;AAOrD,UAAI,YAAY,CAAC,EAAG,CAAAA,KAAI,GAAG,IAAIA,KAAI,GAAG,EAAE,YAAa;AAAA,IACzD;AAOE,QAAI,OAAQ,CAAAA,KAAI,QAAQ,OAAOA,KAAI,KAAK;AAKxC,QACI,YACC,SAAS,WACTA,KAAI,SAAS,OAAO,CAAC,MAAM,QAC1BA,KAAI,aAAa,MAAM,SAAS,aAAa,KACjD;AACA,MAAAA,KAAI,WAAW,QAAQA,KAAI,UAAU,SAAS,QAAQ;AAAA,IAC1D;AAME,QAAIA,KAAI,SAAS,OAAO,CAAC,MAAM,OAAO,UAAUA,KAAI,QAAQ,GAAG;AAC7D,MAAAA,KAAI,WAAW,MAAMA,KAAI;AAAA,IAC7B;AAOE,QAAI,CAAC,SAASA,KAAI,MAAMA,KAAI,QAAQ,GAAG;AACrC,MAAAA,KAAI,OAAOA,KAAI;AACf,MAAAA,KAAI,OAAO;AAAA,IACf;AAKE,IAAAA,KAAI,WAAWA,KAAI,WAAW;AAE9B,QAAIA,KAAI,MAAM;AACZ,cAAQA,KAAI,KAAK,QAAQ,GAAG;AAE5B,UAAI,CAAC,OAAO;AACV,QAAAA,KAAI,WAAWA,KAAI,KAAK,MAAM,GAAG,KAAK;AACtC,QAAAA,KAAI,WAAW,mBAAmB,mBAAmBA,KAAI,QAAQ,CAAC;AAElE,QAAAA,KAAI,WAAWA,KAAI,KAAK,MAAM,QAAQ,CAAC;AACvC,QAAAA,KAAI,WAAW,mBAAmB,mBAAmBA,KAAI,QAAQ,CAAC;AAAA,MACxE,OAAW;AACL,QAAAA,KAAI,WAAW,mBAAmB,mBAAmBA,KAAI,IAAI,CAAC;AAAA,MACpE;AAEI,MAAAA,KAAI,OAAOA,KAAI,WAAWA,KAAI,WAAU,MAAKA,KAAI,WAAWA,KAAI;AAAA,IACpE;AAEE,IAAAA,KAAI,SAASA,KAAI,aAAa,WAAW,UAAUA,KAAI,QAAQ,KAAKA,KAAI,OACpEA,KAAI,WAAU,OAAMA,KAAI,OACxB;AAKJ,IAAAA,KAAI,OAAOA,KAAI,SAAU;AAAA,EAC3B;AAeA,WAAS,IAAI,MAAM,OAAO,IAAI;AAC5B,QAAIA,OAAM;AAEV,YAAQ,MAAI;AAAA,MACV,KAAK;AACH,YAAI,aAAa,OAAO,SAAS,MAAM,QAAQ;AAC7C,mBAAS,MAAM,GAAG,OAAO,KAAK;AAAA,QACtC;AAEM,QAAAA,KAAI,IAAI,IAAI;AACZ;AAAA,MAEF,KAAK;AACH,QAAAA,KAAI,IAAI,IAAI;AAEZ,YAAI,CAAC,SAAS,OAAOA,KAAI,QAAQ,GAAG;AAClC,UAAAA,KAAI,OAAOA,KAAI;AACf,UAAAA,KAAI,IAAI,IAAI;AAAA,QACb,WAAU,OAAO;AAChB,UAAAA,KAAI,OAAOA,KAAI,WAAU,MAAK;AAAA,QACtC;AAEM;AAAA,MAEF,KAAK;AACH,QAAAA,KAAI,IAAI,IAAI;AAEZ,YAAIA,KAAI,KAAM,UAAS,MAAKA,KAAI;AAChC,QAAAA,KAAI,OAAO;AACX;AAAA,MAEF,KAAK;AACH,QAAAA,KAAI,IAAI,IAAI;AAEZ,YAAI,KAAK,KAAK,KAAK,GAAG;AACpB,kBAAQ,MAAM,MAAM,GAAG;AACvB,UAAAA,KAAI,OAAO,MAAM,IAAK;AACtB,UAAAA,KAAI,WAAW,MAAM,KAAK,GAAG;AAAA,QACrC,OAAa;AACL,UAAAA,KAAI,WAAW;AACf,UAAAA,KAAI,OAAO;AAAA,QACnB;AAEM;AAAA,MAEF,KAAK;AACH,QAAAA,KAAI,WAAW,MAAM,YAAa;AAClC,QAAAA,KAAI,UAAU,CAAC;AACf;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AACH,YAAI,OAAO;AACT,cAAI,OAAO,SAAS,aAAa,MAAM;AACvC,UAAAA,KAAI,IAAI,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO,OAAO,QAAQ;AAAA,QAC9D,OAAa;AACL,UAAAA,KAAI,IAAI,IAAI;AAAA,QACpB;AACM;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AACH,QAAAA,KAAI,IAAI,IAAI,mBAAmB,KAAK;AACpC;AAAA,MAEF,KAAK;AACH,YAAI,QAAQ,MAAM,QAAQ,GAAG;AAE7B,YAAI,CAAC,OAAO;AACV,UAAAA,KAAI,WAAW,MAAM,MAAM,GAAG,KAAK;AACnC,UAAAA,KAAI,WAAW,mBAAmB,mBAAmBA,KAAI,QAAQ,CAAC;AAElE,UAAAA,KAAI,WAAW,MAAM,MAAM,QAAQ,CAAC;AACpC,UAAAA,KAAI,WAAW,mBAAmB,mBAAmBA,KAAI,QAAQ,CAAC;AAAA,QAC1E,OAAa;AACL,UAAAA,KAAI,WAAW,mBAAmB,mBAAmB,KAAK,CAAC;AAAA,QACnE;AAAA,IACA;AAEE,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,MAAM,MAAM,CAAC;AAEjB,UAAI,IAAI,CAAC,EAAG,CAAAA,KAAI,IAAI,CAAC,CAAC,IAAIA,KAAI,IAAI,CAAC,CAAC,EAAE,YAAa;AAAA,IACvD;AAEE,IAAAA,KAAI,OAAOA,KAAI,WAAWA,KAAI,WAAU,MAAKA,KAAI,WAAWA,KAAI;AAEhE,IAAAA,KAAI,SAASA,KAAI,aAAa,WAAW,UAAUA,KAAI,QAAQ,KAAKA,KAAI,OACpEA,KAAI,WAAU,OAAMA,KAAI,OACxB;AAEJ,IAAAA,KAAI,OAAOA,KAAI,SAAU;AAEzB,WAAOA;AAAA,EACT;AASA,WAAS,SAAS,WAAW;AAC3B,QAAI,CAAC,aAAa,eAAe,OAAO,UAAW,aAAY,GAAG;AAElE,QAAI,OACAA,OAAM,MACN,OAAOA,KAAI,MACX,WAAWA,KAAI;AAEnB,QAAI,YAAY,SAAS,OAAO,SAAS,SAAS,CAAC,MAAM,IAAK,aAAY;AAE1E,QAAI,SACF,YACEA,KAAI,YAAYA,KAAI,WAAY,UAAUA,KAAI,QAAQ,IAAI,OAAO;AAErE,QAAIA,KAAI,UAAU;AAChB,gBAAUA,KAAI;AACd,UAAIA,KAAI,SAAU,WAAU,MAAKA,KAAI;AACrC,gBAAU;AAAA,IACd,WAAaA,KAAI,UAAU;AACvB,gBAAU,MAAKA,KAAI;AACnB,gBAAU;AAAA,IACd,WACIA,KAAI,aAAa,WACjB,UAAUA,KAAI,QAAQ,KACtB,CAAC,QACDA,KAAI,aAAa,KACjB;AAKA,gBAAU;AAAA,IACd;AAOE,QAAI,KAAK,KAAK,SAAS,CAAC,MAAM,OAAQ,KAAK,KAAKA,KAAI,QAAQ,KAAK,CAACA,KAAI,MAAO;AAC3E,cAAQ;AAAA,IACZ;AAEE,cAAU,OAAOA,KAAI;AAErB,YAAQ,aAAa,OAAOA,KAAI,QAAQ,UAAUA,KAAI,KAAK,IAAIA,KAAI;AACnE,QAAI,MAAO,WAAU,QAAQ,MAAM,OAAO,CAAC,IAAI,MAAK,QAAQ;AAE5D,QAAIA,KAAI,KAAM,WAAUA,KAAI;AAE5B,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,EAAE,KAAU,SAAoB;AAMhD,MAAI,kBAAkB;AACtB,MAAI,WAAW;AACf,MAAI,WAAW;AACf,MAAI,KAAK;AAET,aAAiB;;;;;;;;;;;;;ACzkBjB,QAAI,iBAAiB,KAAK,OAAO,KAAK;AACtC,QAAI,iBAAiB,IAAI;AAGzB,QAAI,UAAU;AAEd,aAAS,gBAAgB,KAAK;AAC1B,aAAO,OAAO,WAAW,OAAO;AAAA,IACpC;AAEA,aAAS,oBAAoB,KAAK;AAC9B,UAAI,CAAC,gBAAgB,GAAG,GAAG;AACvB,cAAM,IAAI,UAAU,sDAAsD;AAAA,MAClF;AAAA,IACA;AAEA,WAAA,QAAA,kBAAiC;AACjC,WAAA,QAAA,sBAAqC;AAGrC,KAAC,QAAQ,KAAK,EAAE,QAAQ,SAAU,MAAM;AACtC,UAAI,SAAS,OAAO;AACpB,aAAO,QAAQ,SAAS,MAAM,IAC5B,OAAO,UAAU,SAAS,MAAM,EAAE;AACpC,aAAO,QAAQ,UAAU,MAAM,IAC7B,OAAO,UAAU,UAAU,MAAM,EAAE;AAErC,OAAC,MAAM,IAAI,EAAE,QAAQ,SAAU,MAAM;AACnC,SAAC,MAAM,IAAI,EAAE,QAAQ,SAAU,QAAQ;AACrC,cAAIG,UAAS,OAAO,OAAO;AAC3B,cAAI,OAAO,OAAO,UAAU,SAASA,OAAM;AAC3C,iBAAO,QAAQ,SAASA,OAAM,IAC5B,SAAU,KAAK,QAAQ;AACrB,mBAAO,KAAK,KAAK,KAAK,MAAM;AAAA,UAC7B;AACH,cAAI,QAAQ,OAAO,UAAU,UAAUA,OAAM;AAC7C,iBAAO,QAAQ,UAAUA,OAAM,IAC7B,SAAU,KAAK,KAAK,QAAQ;AAC1B,mBAAO,MAAM,KAAK,KAAK,KAAK,MAAM;AAAA,UACnC;AAAA,QACT,CAAK;AAAA,MACL,CAAG;AAAA,IACH,CAAC;AAGD,aAAS,YAAY,KAAK,KAAK,KAAK;AAChC,YAAM,CAAC;AACP,UAAI,OAAO,OAAQ,YAAY,MAAM,OAAO,MAAM,OAAO,KAAK,MAAM,GAAG,MAAM,KAAK;AAC9E,cAAM,IAAI,UAAU,mCAAqC;AAAA,MACjE;AACI,aAAO;AAAA,IACX;AAGA,aAAS,aAAa,KAAK,QAAQ,KAAK;AACpC,UAAI,SAAS,KAAK,SAAS,MAAM,IAAI,QAAQ;AACzC,cAAM,IAAI,WAAW,oBAAoB;AAAA,MACjD;AAAA,IACA;AAEA,aAAS,aAAa,KAAK,QAAQ;AACjC,aAAO,IAAI,UAAU,MAAM,KAAK,KAAK,IAAI,aAAa,SAAS,CAAC;AAAA,IAClE;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,cAAc,KAAK,KAAK,QAAQ;AACrC,YAAM,YAAY,KAAK,GAAG,QAAQ;AAClC,mBAAa,KAAK,QAAQ,CAAC;AAC3B,UAAI,WAAW,QAAQ,IAAI,MAAM;AACjC,UAAI,cAAc,MAAM,OAAQ,SAAS,CAAC;AAAA,IAC9C;AACA,WAAA,QAAA,gBAA+B;AAE/B,aAAS,aAAa,KAAK,QAAQ;AAC/B,cAAQ,IAAI,UAAU,MAAM,KAAK,KAAK,gBAAgB,IAAI,aAAa,SAAS,CAAC;AAAA,IACrF;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,cAAc,KAAK,KAAK,QAAQ;AACrC,YAAM,YAAY,KAAK,GAAG,aAAY;AACtC,mBAAa,KAAK,QAAQ,CAAC;AAC3B,UAAI,WAAW,KAAK,MAAM,MAAM,cAAc,GAAG,MAAM;AACvD,UAAI,aAAa,MAAM,IAAI,SAAS,CAAC;AAAA,IACzC;AACA,WAAA,QAAA,gBAA+B;AAE/B,aAAS,aAAa,KAAK,QAAQ;AAC/B,aAAO,IAAI,aAAa,MAAM,IAAI,gBAAgB,IAAI,aAAa,SAAS,CAAC;AAAA,IACjF;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,cAAc,KAAK,KAAK,QAAQ;AACrC,YAAM,YAAY,KAAK,GAAG,eAAc;AACxC,mBAAa,KAAK,QAAQ,CAAC;AAC3B,UAAI,cAAc,KAAK,MAAM,MAAM,cAAc,GAAG,MAAM;AAC1D,UAAI,aAAa,MAAM,IAAI,SAAS,CAAC;AAAA,IACzC;AACA,WAAA,QAAA,gBAA+B;AAE/B,aAAS,aAAa,KAAK,QAAQ;AAC/B,eAAS,IAAI,UAAU,MAAM,KAAK,MAAM,KAAK,IAAI,aAAa,SAAS,CAAC,KAAK,gBAAgB,IAAI,aAAa,SAAS,CAAC;AAAA,IAC5H;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,cAAc,KAAK,KAAK,QAAQ;AACrC,YAAM,YAAY,KAAK,GAAG,iBAAgB;AAC1C,mBAAa,KAAK,QAAQ,CAAC;AAE3B,UAAI,MAAM,mBAAmB;AACzB,YAAI,KAAK,KAAK,MAAM,MAAM,cAAc;AACxC,YAAI,WAAW,OAAO,IAAI,MAAM;AAChC,YAAI,cAAc,KAAK,OAAQ,SAAS,CAAC;AACzC,YAAI,aAAa,MAAM,IAAI,SAAS,CAAC;AAAA,MAC7C,OAAW;AAEH,YAAI,MAAM,IAAI;AACd,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAAA,MACxB;AAAA,IACA;AACA,WAAA,QAAA,gBAA+B;AAE/B,aAAS,aAAa,KAAK,QAAQ;AAC/B,aAAO,IAAI,aAAa,MAAM,IAAI,gBAAgB,IAAI,aAAa,SAAS,CAAC;AAAA,IACjF;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,cAAc,KAAK,KAAK,QAAQ;AACrC,YAAM,YAAY,KAAK,GAAG,mBAAkB;AAC5C,mBAAa,KAAK,QAAQ,CAAC;AAE3B,UAAI,MAAM,qBAAqB;AAC3B,YAAI,cAAc,KAAK,MAAM,MAAM,cAAc,GAAG,MAAM;AAC1D,YAAI,aAAa,MAAM,IAAI,SAAS,CAAC;AAAA,MAC7C,OAAW;AAEH,YAAI,MAAM,IAAI;AACd,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAAA,MACxB;AAAA,IACA;AACA,WAAA,QAAA,gBAA+B;AAE/B,aAAS,aAAa,KAAK,QAAQ;AAC/B,aAAO,IAAI,UAAU,SAAS,CAAC,KAAK,KAAK,IAAI,aAAa,MAAM;AAAA,IACpE;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,cAAc,KAAK,KAAK,QAAQ;AACrC,YAAM,YAAY,KAAK,GAAG,QAAQ;AAClC,mBAAa,KAAK,QAAQ,CAAC;AAE3B,UAAI,cAAc,MAAM,OAAQ,MAAM;AACtC,UAAI,WAAW,QAAQ,IAAI,SAAS,CAAC;AAAA,IACzC;AACA,WAAA,QAAA,gBAA+B;AAE/B,aAAS,aAAa,KAAK,QAAQ;AAC/B,cAAQ,IAAI,UAAU,SAAS,CAAC,KAAK,KAAK,gBAAgB,IAAI,aAAa,MAAM;AAAA,IACrF;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,cAAc,KAAK,KAAK,QAAQ;AACrC,YAAM,YAAY,KAAK,GAAG,aAAY;AACtC,mBAAa,KAAK,QAAQ,CAAC;AAC3B,UAAI,aAAa,MAAM,IAAI,MAAM;AACjC,UAAI,WAAW,KAAK,MAAM,MAAM,cAAc,GAAG,SAAS,CAAC;AAAA,IAC/D;AACA,WAAA,QAAA,gBAA+B;AAE/B,aAAS,aAAa,KAAK,QAAQ;AAC/B,aAAO,IAAI,aAAa,SAAS,CAAC,IAAI,gBAAgB,IAAI,aAAa,MAAM;AAAA,IACjF;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,cAAc,KAAK,KAAK,QAAQ;AACrC,YAAM,YAAY,KAAK,GAAG,eAAc;AACxC,mBAAa,KAAK,QAAQ,CAAC;AAC3B,UAAI,aAAa,MAAM,IAAI,MAAM;AACjC,UAAI,cAAc,KAAK,MAAM,MAAM,cAAc,GAAG,SAAS,CAAC;AAAA,IAClE;AACA,WAAA,QAAA,gBAA+B;AAE/B,aAAS,aAAa,KAAK,QAAQ;AAC/B,eAAS,IAAI,UAAU,SAAS,CAAC,KAAK,MAAM,KAAK,IAAI,aAAa,SAAS,CAAC,KAAK,gBAAgB,IAAI,aAAa,MAAM;AAAA,IAC5H;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,cAAc,KAAK,KAAK,QAAQ;AACrC,YAAM,YAAY,KAAK,GAAG,iBAAgB;AAC1C,mBAAa,KAAK,QAAQ,CAAC;AAE3B,UAAI,MAAM,mBAAmB;AACzB,YAAI,aAAa,MAAM,IAAI,MAAM;AACjC,YAAI,KAAK,KAAK,MAAM,MAAM,cAAc;AACxC,YAAI,cAAc,KAAK,OAAQ,SAAS,CAAC;AACzC,YAAI,WAAW,OAAO,IAAI,SAAS,CAAC;AAAA,MAC5C,OAAW;AAEH,YAAI,MAAM,IAAI;AACd,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAAA,MACxB;AAAA,IACA;AACA,WAAA,QAAA,gBAA+B;AAE/B,aAAS,aAAa,KAAK,QAAQ;AAC/B,aAAO,IAAI,aAAa,SAAS,CAAC,IAAI,gBAAgB,IAAI,aAAa,MAAM;AAAA,IACjF;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,cAAc,KAAK,KAAK,QAAQ;AACrC,YAAM,YAAY,KAAK,GAAG,mBAAkB;AAC5C,mBAAa,KAAK,QAAQ,CAAC;AAE3B,UAAI,MAAM,qBAAqB;AAC3B,YAAI,aAAa,MAAM,IAAI,MAAM;AACjC,YAAI,cAAc,KAAK,MAAM,MAAM,cAAc,GAAG,SAAS,CAAC;AAAA,MACtE,OAAW;AAEH,YAAI,MAAM,IAAI;AACd,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAAA,MACxB;AAAA,IACA;AACA,WAAA,QAAA,gBAA+B;AAG/B,aAAS,YAAY,KAAK,QAAQ;AAC9B,cAAQ,IAAI,SAAS,MAAM,KAAK,MAAM,IAAI,aAAa,SAAS,CAAC;AAAA,IACrE;AACA,WAAA,QAAA,cAA6B;AAE7B,aAAS,aAAa,KAAK,KAAK,QAAQ;AACpC,YAAM,YAAY,KAAK,UAAW,OAAQ;AAC1C,mBAAa,KAAK,QAAQ,CAAC;AAC3B,UAAI,UAAU,OAAO,IAAI,MAAM;AAC/B,UAAI,cAAc,MAAM,OAAQ,SAAS,CAAC;AAAA,IAC9C;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,YAAY,KAAK,QAAQ;AAC9B,cAAQ,IAAI,SAAS,MAAM,KAAK,KAAK,gBAAgB,IAAI,aAAa,SAAS,CAAC;AAAA,IACpF;AACA,WAAA,QAAA,cAA6B;AAE7B,aAAS,aAAa,KAAK,KAAK,QAAQ;AACpC,YAAM,YAAY,KAAK,eAAe,YAAY;AAClD,mBAAa,KAAK,QAAQ,CAAC;AAC3B,UAAI,UAAU,KAAK,MAAM,MAAM,cAAc,GAAG,MAAM;AACtD,UAAI,aAAa,MAAM,IAAI,SAAS,CAAC;AAAA,IACzC;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,YAAY,KAAK,QAAQ;AAC9B,aAAO,IAAI,YAAY,MAAM,IAAI,gBAAgB,IAAI,aAAa,SAAS,CAAC;AAAA,IAChF;AACA,WAAA,QAAA,cAA6B;AAE7B,aAAS,aAAa,KAAK,KAAK,QAAQ;AACpC,YAAM,YAAY,KAAK,kBAAiB,eAAc;AACtD,mBAAa,KAAK,QAAQ,CAAC;AAC3B,UAAI,aAAa,KAAK,MAAM,MAAM,cAAc,GAAG,MAAM;AACzD,UAAI,aAAa,MAAM,IAAI,SAAS,CAAC;AAAA,IACzC;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,YAAY,KAAK,QAAQ;AAC9B,gBAAU,IAAI,SAAS,MAAM,KAAK,MAAM,MAAM,IAAI,aAAa,SAAS,CAAC,KAAK,gBAAgB,IAAI,aAAa,SAAS,CAAC;AAAA,IAC7H;AACA,WAAA,QAAA,cAA6B;AAE7B,aAAS,aAAa,KAAK,KAAK,QAAQ;AACpC,YAAM,YAAY,KAAK,qBAAoB,iBAAgB;AAC3D,mBAAa,KAAK,QAAQ,CAAC;AAE3B,UAAI,MAAM,mBAAkB;AACxB,YAAI,KAAK,KAAK,MAAM,MAAM,cAAc;AACxC,YAAI,UAAU,MAAM,IAAI,MAAM;AAC9B,YAAI,cAAc,KAAK,OAAQ,SAAS,CAAC;AACzC,YAAI,aAAa,MAAM,IAAI,SAAS,CAAC;AAAA,MAC7C,OAAW;AAEH,YAAI,MAAM,IAAI;AACd,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAAA,MACxB;AAAA,IACA;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,YAAY,KAAK,QAAQ;AAC9B,aAAO,IAAI,YAAY,MAAM,IAAI,gBAAgB,IAAI,aAAa,SAAS,CAAC;AAAA,IAChF;AACA,WAAA,QAAA,cAA6B;AAE7B,aAAS,aAAa,KAAK,KAAK,QAAQ;AACpC,YAAM,YAAY,KAAK,sBAAuB,kBAAkB;AAChE,mBAAa,KAAK,QAAQ,CAAC;AAE3B,UAAI,MAAM,oBAAoB;AAC1B,YAAI,aAAa,KAAK,MAAM,MAAM,cAAc,GAAG,MAAM;AACzD,YAAI,aAAa,MAAM,IAAI,SAAS,CAAC;AAAA,MAC7C,OAAW;AAEH,YAAI,MAAM,IAAI;AACd,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAAA,MACxB;AAAA,IACA;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,YAAY,KAAK,QAAQ;AAC9B,cAAQ,IAAI,SAAS,SAAS,CAAC,KAAK,MAAM,IAAI,aAAa,MAAM;AAAA,IACrE;AACA,WAAA,QAAA,cAA6B;AAE7B,aAAS,aAAa,KAAK,KAAK,QAAQ;AACpC,YAAM,YAAY,KAAK,UAAW,OAAQ;AAC1C,mBAAa,KAAK,QAAQ,CAAC;AAC3B,UAAI,cAAc,MAAM,OAAQ,MAAM;AACtC,UAAI,UAAU,OAAO,IAAI,SAAS,CAAC;AAAA,IACvC;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,YAAY,KAAK,QAAQ;AAC9B,cAAQ,IAAI,SAAS,SAAS,CAAC,KAAK,KAAK,gBAAgB,IAAI,aAAa,MAAM;AAAA,IACpF;AACA,WAAA,QAAA,cAA6B;AAE7B,aAAS,aAAa,KAAK,KAAK,QAAQ;AACpC,YAAM,YAAY,KAAK,eAAe,YAAY;AAClD,mBAAa,KAAK,QAAQ,CAAC;AAC3B,UAAI,aAAa,MAAM,IAAI,MAAM;AACjC,UAAI,UAAU,KAAK,MAAM,MAAM,cAAc,GAAG,SAAS,CAAC;AAAA,IAC9D;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,YAAY,KAAK,QAAQ;AAC9B,aAAO,IAAI,YAAY,SAAS,CAAC,IAAI,gBAAgB,IAAI,aAAa,MAAM;AAAA,IAChF;AACA,WAAA,QAAA,cAA6B;AAE7B,aAAS,aAAa,KAAK,KAAK,QAAQ;AACpC,YAAM,YAAY,KAAK,kBAAiB,eAAc;AACtD,mBAAa,KAAK,QAAQ,CAAC;AAC3B,UAAI,aAAa,MAAM,IAAI,MAAM;AACjC,UAAI,aAAa,KAAK,MAAM,MAAM,cAAc,GAAG,SAAS,CAAC;AAAA,IACjE;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,YAAY,KAAK,QAAQ;AAC9B,gBAAU,IAAI,SAAS,SAAS,CAAC,KAAK,MAAM,MAAM,IAAI,aAAa,SAAS,CAAC,KAAK,gBAAgB,IAAI,aAAa,MAAM;AAAA,IAC7H;AACA,WAAA,QAAA,cAA6B;AAE7B,aAAS,aAAa,KAAK,KAAK,QAAQ;AACpC,YAAM,YAAY,KAAK,oBAAmB,iBAAgB;AAC1D,mBAAa,KAAK,QAAQ,CAAC;AAE3B,UAAI,MAAM,mBAAkB;AACxB,YAAI,aAAa,MAAM,IAAI,MAAM;AACjC,YAAI,KAAK,KAAK,MAAM,MAAM,cAAc;AACxC,YAAI,cAAc,KAAK,OAAQ,SAAS,CAAC;AACzC,YAAI,UAAU,MAAM,IAAI,SAAS,CAAC;AAAA,MAC1C,OAAW;AAEH,YAAI,MAAM,IAAI;AACd,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAAA,MACxB;AAAA,IACA;AACA,WAAA,QAAA,eAA8B;AAE9B,aAAS,YAAY,KAAK,QAAQ;AAC9B,aAAO,IAAI,YAAY,SAAS,CAAC,IAAI,gBAAgB,IAAI,aAAa,MAAM;AAAA,IAChF;AACA,WAAA,QAAA,cAA6B;AAE7B,aAAS,aAAa,KAAK,KAAK,QAAQ;AACpC,YAAM,YAAY,KAAK,qBAAqB,kBAAkB;AAC9D,mBAAa,KAAK,QAAQ,CAAC;AAE3B,UAAI,MAAM,oBAAoB;AAC1B,YAAI,aAAa,MAAM,IAAI,MAAM;AACjC,YAAI,aAAa,KAAK,MAAM,MAAM,cAAc,GAAG,SAAS,CAAC;AAAA,MACrE,OAAW;AAEH,YAAI,MAAM,IAAI;AACd,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAChB,YAAI,SAAO,CAAC,IAAI;AAAA,MACxB;AAAA,IACA;AACA,WAAA,QAAA,eAA8B;AAAA;;;;;;;ACxX9B,MAAI,OAAOL,sBAA2B;AAetC,WAAS,gBAAgB,GAAG;AACxB,WAAO,KAAK,sBACP,KAAK,IAAI,CAAC,IAAI,oBACX,KAAK,MAAM,CAAC,MAAM;AAAA,EAC9B;AAEA,WAAS,YAAY,QAAQ,KAAK,QAAQ;AACtC,QAAI,QAAQ;AACZ,cAAU;AACV,aAAS,OAAO,KAAK;AACjB,UAAI,IAAI,GAAG,MAAM,QAAW;AAC1B,YAAI,MAAM,OAAO,WAAW,GAAG;AAC/B,eAAO,WAAW,KAAK,MAAM;AAAG;AAChC,eAAO,MAAM,KAAK,QAAQ,MAAM;AAAG,kBAAU;AAC7C,kBAAU,iBAAiB,QAAQ,IAAI,GAAG,GAAG,MAAM;AAAA,MAC7D;AAAA,IACA;AACI,QAAI,OAAO,SAAS;AACpB,WAAO,cAAc,OAAO,GAAG,KAAK;AACpC,WAAO;AAAA,EACX;AAEA,WAAS,YAAY,QAAQ,KAAK,QAAQ;AACtC,QAAI,QAAQ;AACZ,cAAU;AACV,aAAS,IAAE,GAAG,MAAI,IAAI,QAAQ,IAAI,KAAK,KAAK;AACxC,gBAAU,iBAAiB,QAAQ,IAAI,CAAC,GAAG,MAAM;AAAA,IACzD;AACI,QAAI,OAAO,SAAS;AACpB,WAAO,cAAc,OAAO,GAAG,KAAK;AACpC,WAAO;AAAA,EACX;AAEA,WAAS,iBAAiB,QAAQ,OAAO,QAAQ;AAC7C,QAAI,QAAQ;AACZ,QAAI,OAAO,OAAO,OAAO,MAAM;AAE/B,QAAI,SAAS,SAAS,YAAY,MAAM,eAAe,GAAG,GAAG;AACzD,YAAM,MAAM;AACZ,aAAO,MAAM,GAAG;AAAA,IACxB;AAII,QAAI,QAAQ,UAAU;AAUlB,UAAI,gBAAgB,GAAG,GAAG;AACtB,eAAO;AAAA,MACnB,OACa;AAID,YAAI,MAAM,OAAO,OAAO,MAAM;AAC1B,iBAAO;AAAA,QACvB,WACqB,OAAO,UAAW,MAAM,OAAQ;AACrC,iBAAO;AAAA,QACvB,WACqB,OAAO,eAAe,MAAM,YAAY;AAC7C,iBAAO;AAAA,QACvB,OACiB;AACD,iBAAO;AAAA,QACvB;AAAA,MACA;AAAA,IACA;AAEI,aAAS,IAAI,GAAG;AAAE,aAAO,MAAM,GAAG,MAAM;AAAG;AAAA,IAAS;AAEpD,YAAQ,MAAI;AAAA,MACZ,KAAK;AACD,YAAI,MAAM,OAAO,WAAW,KAAK,MAAM;AACvC,YAAI,GAAG;AACP,eAAO,cAAc,KAAK,MAAM;AAAG,kBAAU;AAC7C,eAAO,MAAM,KAAK,QAAQ,MAAM;AAAG,kBAAU;AAC7C;AAAA,MACJ,KAAK;AACD,YAAI,QAAQ,MAAM;AACd,cAAI,GAAG;AAAA,QACnB,WACiB,MAAM,QAAQ,GAAG,GAAG;AACzB,cAAI,GAAG;AACP,oBAAU,YAAY,QAAQ,KAAK,MAAM;AAAA,QACrD,WACiB,OAAO,SAAS,GAAG,GAAG;AAC3B,cAAI,GAAG;AACP,iBAAO,cAAc,IAAI,QAAQ,MAAM;AAAG,oBAAU;AACpD,cAAI,KAAK,QAAQ,MAAM;AAAG,oBAAU,IAAI;AAAA,QACpD,OACa;AACD,cAAI,GAAG;AACP,oBAAU,YAAY,QAAQ,KAAK,MAAM;AAAA,QACrD;AACQ;AAAA,MACJ,KAAK;AACD,YAAI,GAAG;AACP,eAAO,WAAY,MAAO,IAAI,GAAG,MAAM;AAAG;AAC1C;AAAA;AAAA;AAAA,MAGJ,KAAK;AAAA,MACL,KAAK;AACD,YAAI,GAAG;AACP,eAAO,cAAc,KAAK,MAAM;AAChC,kBAAU;AACV;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,YAAI,GAAG;AACP,eAAO,UAAU,KAAK,MAAM;AAAG;AAC/B;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,YAAI,GAAG;AACP,eAAO,WAAW,KAAK,MAAM;AAAG;AAChC;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,YAAI,GAAG;AACP,eAAO,aAAa,KAAK,MAAM;AAAG,kBAAU;AAC5C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,YAAI,GAAG;AACP,eAAO,cAAc,KAAK,MAAM;AAAG,kBAAU;AAC7C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,YAAI,GAAG;AACP,eAAO,aAAa,KAAK,MAAM;AAAG,kBAAU;AAC5C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,YAAI,GAAG;AACP,eAAO,cAAc,KAAK,MAAM;AAAG,kBAAU;AAC7C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,YAAI,GAAG;AACP,aAAK,aAAa,QAAQ,KAAK,MAAM;AAAG,kBAAU;AAClD;AAAA;AAAA;AAAA,MAIJ,KAAK;AACD,YAAI,GAAG;AACP,aAAK,cAAc,QAAQ,KAAK,MAAM;AAAG,kBAAU;AACnD;AAAA,MACJ,KAAK;AACD,YAAI,GAAG;AACP,eAAO,aAAa,KAAK,MAAM;AAAG,kBAAU;AAC5C;AAAA,MACJ,KAAK;AACD,YAAI,GAAG;AACP,YAAI,IAAI,eAAe,QAAQ,KAAK,IAAI,eAAe,QAAQ,KACxD,IAAI,UAAU,KAAK,IAAI,SAAS,KAAK;AACxC,iBAAO,MAAM,IAAI,IAAI;AAAQ;AAC7B,iBAAO,cAAc,IAAI,QAAQ,MAAM;AAAG,oBAAU;AAAA,QAChE,MACa,OAAM,IAAI;AAAA,UACX,qEACa,KAAK,UAAU,GAAG;AAAA,QAAC;AACpC;AAAA,MACJ;AACI,cAAM,IAAI,UAAU,6BAA6B,IAAI;AAAA,IAC7D;AACI,WAAO,SAAS;AAAA,EACpB;AAIA,WAAS,aAAa,OAAO;AACzB,QAAI,SAAS,CAAA,GAAI,SAAS,GAAG,OAAO,MAAM;AAC1C,QAAI,KAAK,KAAK;AAEd,aAAS,mBAAmB;AACxB,UAAI,MAAM,OAAO,aAAa,MAAM,MAAM,CAAC;AAAG;AAC9C,cAAQ,KAAG;AAAA,QACX,KAAK;AACD,gBAAM,MAAM,SAAS,MAAM;AAAG;AAC9B;AAAA,QACJ,KAAK;AACD,gBAAM,MAAM,UAAU,MAAM;AAAG;AAC/B;AAAA,QACJ,KAAK;AACD,gBAAM,MAAM,aAAa,MAAM;AAAG,oBAAU;AAC5C,gBAAM,MAAM,SAAS,QAAQ,QAAQ,SAAS,GAAG;AACjD,oBAAU;AACV;AAAA,QACJ,KAAK;AACD,gBAAM,MAAM,YAAY,MAAM;AAAG,oBAAU;AAC3C;AAAA,QACJ,KAAK;AACD,gBAAM,MAAM,aAAa,MAAM;AAAG,oBAAU;AAC5C;AAAA,QACJ,KAAK;AACD,cAAI,SAAS,MAAM,MAAM;AAAG;AAC5B,cAAI,SAAS,MAAM,aAAa,MAAM;AAAG,oBAAU;AACnD,gBAAM,EAAC,KAAK,WAAW,OAAO,EAAC,QAAgB,OAAc,EAAC;AAC9D;AAAA,QACJ,KAAK;AACD,gBAAM,KAAK,aAAa,OAAO,MAAM;AAAG,oBAAU;AAClD,gBAAM,EAAC,KAAK,aAAa,OAAO,IAAG;AACnC;AAAA,QACJ,KAAK;AACD,gBAAM,MAAM,aAAa,MAAM;AAAG,oBAAU;AAC5C,gBAAM,aAAa,MAAM,SAAS,QAAQ,SAAS,GAAG,CAAC;AACvD,oBAAU;AACV;AAAA,QACJ,KAAK;AACD,gBAAM,MAAM,aAAa,MAAM;AAAG,oBAAU;AAC5C,sBAAY,SAAS,GAAG;AAExB;AAAA,QACJ,KAAK;AACD,gBAAM,MAAM,aAAa,MAAM;AAAG,oBAAU;AAC5C;AAAA,QACJ,KAAK;AACD,gBAAM,MAAM,YAAY,MAAM;AAAG,oBAAU;AAC3C;AAAA,QACJ,KAAK;AACD,gBAAM,KAAK,YAAY,OAAO,MAAM;AAAG,oBAAU;AACjD;AAAA,QACJ,KAAK;AACD,gBAAM,MAAM,YAAY,MAAM;AAAG,oBAAU;AAC3C;AAAA,QACJ,KAAK;AACD,gBAAM,MAAM,aAAa,MAAM;AAAG,oBAAU;AAC5C;AAAA,QACJ,KAAK;AACD,gBAAM,MAAM,MAAM,KAAK;AAAG;AAC1B;AAAA,QACJ,KAAK;AACD,gBAAM;AACN;AAAA,QACJ,KAAK;AACD,gBAAM,MAAM,aAAa,MAAM;AAAG,oBAAU;AAC5C,gBAAM,MAAM,SAAS,QAAQ,SAAS,GAAG;AACzC,oBAAU;AACV;AAAA,QACJ;AACI,gBAAM,IAAI,UAAU,0BAA0B,MAAK,GAAG;AAAA,MAClE;AAAA,IACA;AAEI,aAAS,YAAY,OAAO;AACxB,UAAI,OAAO,CAAE;AACb,aAAO,SAAS,OAAO;AACnB,yBAAkB;AAClB,aAAK,KAAK,GAAG;AAAA,MACzB;AACQ,YAAM;AAAA,IACd;AAEI,WAAO,SAAS,MAAM;AAClB,YAAM,MAAM,UAAU,MAAM;AAAG;AAC/B,YAAM,MAAM,SAAS,QAAQ,QAAQ,SAAS,GAAG;AACjD,gBAAU;AACV,uBAAkB;AAClB,aAAO,GAAG,IAAI;AAAA,IACtB;AACI,WAAO;AAAA,EACX;AAEA,QAAA,cAA6B;AAC7B,QAAA,eAA8B;;;;;;;;;;;;ACjV9B,WAAS,eAAe,QAAQ;AAC9B,QAAI,KAAK,SAAS,GAAG,SAAS;AAAA,MAC5B,cAAc;AAAA,MACd,eAAe;AAAA,MACf,QAAQ;AAAA,IACT;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,eAAe;AACtB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,gBAAgB;AACvB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,WAAS,eAAeM,UAAS,QAAQ;AACvC,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,SAAS,OAAO,MAAM,EAAE;AAC9D,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,wEAAwE;AACxK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,yEAAyE;AACzK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB,QAAQ;AAChC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,iBAAiBA,UAAS,QAAQ;AACzC,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmB,QAAQ;AAClC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,MACT,OAAO;AAAA,MACP,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,QAAQ;AACf,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,cAAc;AACrB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,UAAU;AACjB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,QAAQ;AACf,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,YAAY;AACnB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,SAAS;AAChB;AACA,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,aAAa,OAAO,SAAS,QAAQ,SAAS,GAAG,CAAC;AACxD,cAAU;AACV,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmBA,UAAS,QAAQ;AAC3C,QAAI,KAAK,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc,GAAG,gBAAgB;AAC5E,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,qEAAqE;AAC3L,QAAI,YAAY,OAAO,WAAW,KAAK,MAAM;AAC7C,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,2EAA2E;AACjM,QAAI,kBAAkB,OAAO,WAAW,KAAK,MAAM;AACnD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;aAAa,YAAY,OAAO,IAAK,OAAM,IAAI,UAAU,wDAAwD;AAC3I,UAAM,YAAY,SAAS,KAAK,aAAa;AAC7C,QAAI,oBAAoB,QAAQ,MAAM,eAAe,gBAAgB,GAAG;AACxE,qBAAiB;AACjB,mBAAe,kBAAkB;AACjC,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kEAAkE;AAClK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO;AACP,cAAU,kBAAkB,KAAK,QAAQ,MAAM;AAC/C,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqB,QAAQ;AACpC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,aAAa;AAAA,IACd;AACD,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,cAAc;AACrB,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqBA,UAAS,QAAQ;AAC7C,QAAI,SAAS,GAAG,MAAM,MAAM,cAAc;AAC1C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,iDAAiD;AACrF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,2EAA2E;AAC9J,QAAI,kBAAkB,OAAO,WAAW,KAAK,MAAM;AACnD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkB,QAAQ;AACjC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,aAAa;AAAA,MACb,QAAQ;AAAA,IACT;AACD,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,cAAc;AACrB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkBA,UAAS,QAAQ;AAC1C,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc;AACpD,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,iDAAiD;AACrF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,2EAA2E;AAC9J,QAAI,kBAAkB,OAAO,WAAW,KAAK,MAAM;AACnD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoB,QAAQ;AACnC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,aAAa;AAAA,IACd;AACD,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,cAAc;AACrB,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoBA,UAAS,QAAQ;AAC5C,QAAI,SAAS,GAAG,MAAM,MAAM,cAAc;AAC1C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,iDAAiD;AACrF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,2EAA2E;AAC9J,QAAI,kBAAkB,OAAO,WAAW,KAAK,MAAM;AACnD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmB,QAAQ;AAClC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,WAAW;AAClB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,aAAa;AACpB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,YAAY;AACnB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmBA,UAAS,QAAQ;AAC3C,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc;AACpD,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,wEAAwE;AAC9L,QAAI,eAAe,OAAO,WAAW,KAAK,MAAM;AAChD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,0EAA0E;AAChM,QAAI,iBAAiB,OAAO,WAAW,KAAK,MAAM;AAClD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kEAAkE;AAClK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkB,QAAQ;AACjC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,YAAY;AAAA,IACb;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,YAAY;AACnB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,YAAY;AACnB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,WAAW;AAClB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,aAAa;AACpB,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkBA,UAAS,QAAQ;AAC1C,QAAI,SAAS,GAAG,MAAM,MAAM,cAAc;AAC1C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,yEAAyE;AAC/L,QAAI,gBAAgB,OAAO,WAAW,KAAK,MAAM;AACjD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,8CAA8C;AAClF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,wEAAwE;AAC3J,QAAI,eAAe,OAAO,WAAW,KAAK,MAAM;AAChD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,gDAAgD;AACpF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,0EAA0E;AAC7J,QAAI,iBAAiB,OAAO,WAAW,KAAK,MAAM;AAClD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,+CAA+C;AACnF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,qEAAqE;AACnI,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmB,QAAQ;AAClC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,aAAa;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,MACb,UAAU;AAAA,MACV,YAAY;AAAA,IACb;AACD,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,cAAc;AACrB,UAAM,KAAK,aAAa,QAAQ,MAAM;AACtC,cAAU;AACV,WAAO,cAAc;AACrB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,cAAc;AACrB;AACA,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,WAAW;AAClB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,aAAa;AACpB,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmBA,UAAS,QAAQ;AAC3C,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc;AACpD,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,iDAAiD;AACrF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,2EAA2E;AAC9J,QAAI,kBAAkB,OAAO,WAAW,KAAK,MAAM;AACnD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,8CAA8C;AAClF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,wEAAwE;AAC3J,QAAI,eAAe,OAAO,WAAW,KAAK,MAAM;AAChD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,gDAAgD;AACpF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,0EAA0E;AAC7J,QAAI,iBAAiB,OAAO,WAAW,KAAK,MAAM;AAClD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,iDAAiD;AACrF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,uEAAuE;AACrI,SAAK,cAAc,QAAQ,KAAK,MAAM;AACtC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO;AACP,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,eAAe,QAAQ;AAC9B,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,IACR;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,QAAQ;AACf,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,QAAQ;AACf,WAAO;AAAA,EACT;AAEA,WAAS,eAAeA,UAAS,QAAQ;AACvC,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc;AACpD,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,qEAAqE;AAC3L,QAAI,YAAY,OAAO,WAAW,KAAK,MAAM;AAC7C,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kEAAkE;AAClK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB,QAAQ;AAChC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,aAAa;AAAA,MACb,aAAa;AAAA,MACb,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,cAAc;AAAA,IACf;AACD,UAAM,KAAK,aAAa,QAAQ,MAAM;AACtC,cAAU;AACV,WAAO,cAAc;AACrB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,cAAc;AACrB;AACA,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,WAAW;AAClB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,aAAa;AACpB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,eAAe;AACtB,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiBA,UAAS,QAAQ;AACzC,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc;AACpD,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,8CAA8C;AAClF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,wEAAwE;AAC3J,QAAI,eAAe,OAAO,WAAW,KAAK,MAAM;AAChD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,gDAAgD;AACpF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,0EAA0E;AAC7J,QAAI,iBAAiB,OAAO,WAAW,KAAK,MAAM;AAClD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,iDAAiD;AACrF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,uEAAuE;AACrI,SAAK,cAAc,QAAQ,KAAK,MAAM;AACtC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO;AACP,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,kDAAkD;AACtF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,wEAAwE;AACtI,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoB,QAAQ;AACnC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoBA,UAAS,QAAQ;AAC5C,QAAI,SAAS,GAAG,MAAM,MAAM,cAAc;AAC1C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,yEAAyE;AAC/L,QAAI,gBAAgB,OAAO,WAAW,KAAK,MAAM;AACjD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,eAAe,QAAQ;AAC9B,QAAI,KAAK,SAAS,GAAG,SAAS;AAAA,MAC5B,aAAa;AAAA,MACb,UAAU;AAAA,IACX;AACD,UAAM,KAAK,aAAa,QAAQ,MAAM;AACtC,cAAU;AACV,WAAO,cAAc;AACrB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,WAAW;AAClB,WAAO;AAAA,EACT;AAEA,WAAS,eAAeA,UAAS,QAAQ;AACvC,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,SAAS,OAAO,MAAM,EAAE;AAC9D,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,uEAAuE;AACvK,SAAK,cAAc,QAAQ,KAAK,MAAM;AACtC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkB,QAAQ;AACjC,QAAI,KAAK,SAAS,GAAG,SAAS;AAAA,MAC5B,aAAa;AAAA,MACb,SAAS;AAAA,IACV;AACD,UAAM,KAAK,aAAa,QAAQ,MAAM;AACtC,cAAU;AACV,WAAO,cAAc;AACrB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkBA,UAAS,QAAQ;AAC1C,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,SAAS,OAAO,MAAM,EAAE;AAC9D,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,iDAAiD;AACrF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,uEAAuE;AACrI,SAAK,cAAc,QAAQ,KAAK,MAAM;AACtC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,wBAAwB,QAAQ;AACvC,QAAI,KAAK,SAAS;AAAA,MAChB,SAAS;AAAA,IACV;AACD,UAAM,CAAC,EAAE,IAAI,OAAO,CAAC;AACrB,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,WAAS,wBAAwBA,UAAS,QAAQ;AAChD,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,SAAS,OAAO,MAAM,EAAE;AAC9D,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmB,QAAQ;AAClC,QAAI,KAAK,SAAS;AAAA,MAChB,SAAS;AAAA,IACV;AACD,UAAM,CAAC,EAAE,IAAI,OAAO,CAAC;AACrB,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmBA,UAAS,QAAQ;AAC3C,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,SAAS,OAAO,MAAM,EAAE;AAC9D,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqB,QAAQ;AACpC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,qBAAqBA,UAAS,QAAQ;AAC7C,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,gBAAgB,QAAQ;AAC/B,QAAI,KAAK,SAAS,GAAG,SAAS;AAAA,MAC5B,aAAa;AAAA,MACb,UAAU;AAAA,MACV,SAAS;AAAA,IACV;AACD,UAAM,KAAK,aAAa,QAAQ,MAAM;AACtC,cAAU;AACV,WAAO,cAAc;AACrB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,WAAW;AAClB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,WAAS,gBAAgBA,UAAS,QAAQ;AACxC,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,SAAS,OAAO,MAAM,EAAE;AAC9D,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,uEAAuE;AACvK,SAAK,cAAc,QAAQ,KAAK,MAAM;AACtC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,sBAAsB,QAAQ;AACrC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,cAAc;AAAA,MACd,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,YAAY;AAAA,MACZ,SAAS;AAAA,IACV;AACD,UAAM,OAAO,MAAM;AACnB;AACA,WAAO,eAAe;AACtB,UAAM,OAAO,MAAM;AACnB;AACA,WAAO,eAAe;AACtB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,aAAa,OAAO,SAAS,QAAQ,SAAS,GAAG,CAAC;AACxD,cAAU;AACV,WAAO,mBAAmB;AAC1B,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,OAAO,SAAS,QAAQ,SAAS,GAAG;AAC1C,cAAU;AACV,WAAO,aAAa;AACpB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,OAAO,SAAS,QAAQ,SAAS,GAAG;AAC1C,cAAU;AACV,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,WAAS,sBAAsBA,UAAS,QAAQ;AAC9C,QAAI,KAAK,SAAS,GAAG,MAAM,MAAM,cAAc,GAAG,gBAAgB;AAClE,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,sDAAsD;AAC1F,QAAI,YAAY,OAAO,IAAK,OAAM,IAAI,UAAU,+DAA+D;AAC/G,UAAM,YAAY,SAAS,KAAK,aAAa;AAC7C,QAAI,2BAA2B,QAAQ,MAAM,eAAe,gBAAgB,GAAG;AAC/E,qBAAiB;AACjB,mBAAe,yBAAyB;AACxC,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,OAAO,KAAK,OAAO;AAAA,aAAY,CAAC,OAAO,SAAS,GAAG,EAAG,OAAM,IAAI,UAAU,wDAAwD;AAC5J,mBAAe,IAAI;AACnB,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,OAAO,KAAK,OAAO;AAAA,aAAY,CAAC,OAAO,SAAS,GAAG,EAAG,OAAM,IAAI,UAAU,qDAAqD;AACzJ,mBAAe,IAAI;AACnB,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,wEAAwE;AACxK,WAAO,WAAW,KAAK,MAAM;AAC7B;AACA,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,wEAAwE;AACxK,WAAO,WAAW,KAAK,MAAM;AAC7B;AACA,cAAU,yBAAyB,KAAK,QAAQ,MAAM;AACtD,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM,OAAO,KAAK,OAAO;AAC5C,UAAM,IAAI;AACV,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,QAAI,KAAK,QAAQ,MAAM;AACvB,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM,OAAO,KAAK,OAAO;AAC5C,UAAM,IAAI;AACV,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,QAAI,KAAK,QAAQ,MAAM;AACvB,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,wBAAwB,QAAQ;AACvC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,IACT;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,aAAa,OAAO,SAAS,QAAQ,SAAS,GAAG,CAAC;AACxD,cAAU;AACV,WAAO,mBAAmB;AAC1B,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,YAAY;AACnB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,OAAO,SAAS,QAAQ,SAAS,GAAG;AAC1C,cAAU;AACV,WAAO,WAAW;AAClB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,WAAS,wBAAwBA,UAAS,QAAQ;AAChD,QAAI,KAAK,SAAS,GAAG,MAAM,MAAM,cAAc,GAAG,gBAAgB;AAClE,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,sDAAsD;AAC1F,QAAI,YAAY,OAAO,IAAK,OAAM,IAAI,UAAU,+DAA+D;AAC/G,UAAM,YAAY,SAAS,KAAK,aAAa;AAC7C,QAAI,2BAA2B,QAAQ,MAAM,eAAe,gBAAgB,GAAG;AAC/E,qBAAiB;AACjB,mBAAe,yBAAyB;AACxC,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAkB,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,yEAAyE;AACpM,QAAI,gBAAgB,OAAO,WAAW,KAAK,MAAM;AACjD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,8CAA8C;AAClF,QAAI,CAAC,OAAO,SAAS,GAAG,EAAG,OAAM,IAAI,UAAU,sDAAsD;AACrG,mBAAe,IAAI;AACnB,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAkB,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,sEAAsE;AACjM,QAAI,aAAa,OAAO,WAAW,KAAK,MAAM;AAC9C,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,cAAU,yBAAyB,KAAK,QAAQ,MAAM;AACtD,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM,OAAO,KAAK,MAAM;AAC3C,UAAM,IAAI;AACV,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,QAAI,KAAK,QAAQ,MAAM;AACvB,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,uBAAuB,QAAQ;AACtC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,OAAO,SAAS,QAAQ,SAAS,GAAG;AAC1C,cAAU;AACV,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,uBAAuBA,UAAS,QAAQ;AAC/C,QAAI,KAAK,SAAS,GAAG,MAAM,MAAM,cAAc;AAC/C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,+CAA+C;AACnF,QAAI,CAAC,OAAO,SAAS,GAAG,EAAG,OAAM,IAAI,UAAU,uDAAuD;AACtG,mBAAe,IAAI;AACnB,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM,OAAO,KAAK,MAAM;AAC3C,UAAM,IAAI;AACV,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,QAAI,KAAK,QAAQ,MAAM;AACvB,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,yBAAyB,QAAQ;AACxC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,UAAU;AAAA,IACX;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,OAAO,SAAS,QAAQ,SAAS,GAAG;AAC1C,cAAU;AACV,WAAO,WAAW;AAClB,WAAO;AAAA,EACT;AAEA,WAAS,yBAAyBA,UAAS,QAAQ;AACjD,QAAI,KAAK,SAAS,GAAG,MAAM,MAAM,cAAc;AAC/C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,8CAA8C;AAClF,QAAI,CAAC,OAAO,SAAS,GAAG,EAAG,OAAM,IAAI,UAAU,sDAAsD;AACrG,mBAAe,IAAI;AACnB,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM,OAAO,KAAK,MAAM;AAC3C,UAAM,IAAI;AACV,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,QAAI,KAAK,QAAQ,MAAM;AACvB,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqB,QAAQ;AACpC,QAAI,KAAK,SAAS,GAAG,SAAS;AAAA,MAC5B,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,aAAa;AACpB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,WAAW;AAClB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqBA,UAAS,QAAQ;AAC7C,QAAI,SAAS,GAAG,MAAM,MAAM,SAAS,OAAO,MAAM,EAAE;AACpD,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,sEAAsE;AACtK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,oEAAoE;AACpK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,qEAAqE;AACrK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,uBAAuB,QAAQ;AACtC,QAAI,KAAK,SAAS,GAAG,SAAS;AAAA,MAC5B,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,aAAa;AACpB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,WAAW;AAClB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,uBAAuBA,UAAS,QAAQ;AAC/C,QAAI,SAAS,GAAG,MAAM,MAAM,SAAS,OAAO,MAAM,EAAE;AACpD,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,sEAAsE;AACtK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,oEAAoE;AACpK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,qEAAqE;AACrK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqB,QAAQ;AACpC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,aAAa;AAAA,MACb,cAAc;AAAA,MACd,QAAQ;AAAA,IACT;AACD,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,cAAc;AACrB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,eAAe;AACtB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqBA,UAAS,QAAQ;AAC7C,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc;AACpD,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAc,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,2EAA2E;AAClM,QAAI,kBAAkB,OAAO,WAAW,KAAK,MAAM;AACnD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,4EAA4E;AAClM,QAAI,mBAAmB,OAAO,WAAW,KAAK,MAAM;AACpD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,uBAAuB,QAAQ;AACtC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,YAAY;AAAA,IACb;AACD,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,aAAa;AACpB,WAAO;AAAA,EACT;AAEA,WAAS,uBAAuBA,UAAS,QAAQ;AAC/C,QAAI,SAAS,GAAG,MAAM,MAAM,cAAc;AAC1C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,0EAA0E;AAChM,QAAI,iBAAiB,OAAO,WAAW,KAAK,MAAM;AAClD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,sBAAsB,QAAQ;AACrC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,WAAW;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,MACT,UAAU;AAAA,IACX;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,YAAY;AACnB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,YAAY;AACnB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,UAAU;AACjB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,WAAW;AAClB,WAAO;AAAA,EACT;AAEA,WAAS,sBAAsBA,UAAS,QAAQ;AAC9C,QAAI,SAAS,GAAG,MAAM,MAAM,cAAc;AAC1C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,yEAAyE;AAC/L,QAAI,gBAAgB,OAAO,WAAW,KAAK,MAAM;AACjD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,+CAA+C;AACnF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,qEAAqE;AACnI,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,6CAA6C;AACjF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,mEAAmE;AACjI,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,8CAA8C;AAClF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,oEAAoE;AAClI,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,wBAAwB,QAAQ;AACvC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,wBAAwBA,UAAS,QAAQ;AAChD,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,wBAAwB,QAAQ;AACvC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,QAAQ;AAAA,IACT;AACD,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,WAAS,wBAAwBA,UAAS,QAAQ;AAChD,QAAI,SAAS,GAAG,MAAM,MAAM,cAAc;AAC1C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,sEAAsE;AAC5L,QAAI,aAAa,OAAO,WAAW,KAAK,MAAM;AAC9C,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,0BAA0B,QAAQ;AACzC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,0BAA0BA,UAAS,QAAQ;AAClD,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,6BAA6B,QAAQ;AAC5C,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,WAAW;AAAA,MACX,QAAQ;AAAA,IACT;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,OAAO,SAAS,QAAQ,SAAS,GAAG;AAC1C,cAAU;AACV,WAAO,YAAY;AACnB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,WAAS,6BAA6BA,UAAS,QAAQ;AACrD,QAAI,KAAK,SAAS,GAAG,MAAM,MAAM,cAAc;AAC/C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,+CAA+C;AACnF,QAAI,CAAC,OAAO,SAAS,GAAG,EAAG,OAAM,IAAI,UAAU,uDAAuD;AACtG,mBAAe,IAAI;AACnB,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,4CAA4C;AAChF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,sEAAsE;AACzJ,QAAI,aAAa,OAAO,WAAW,KAAK,MAAM;AAC9C,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM,OAAO,KAAK,MAAM;AAC3C,UAAM,IAAI;AACV,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,QAAI,KAAK,QAAQ,MAAM;AACvB,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,+BAA+B,QAAQ;AAC9C,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,+BAA+BA,UAAS,QAAQ;AACvD,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,QAAQ,CAAC;AAC9B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkB,QAAQ;AACjC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkBA,UAAS,QAAQ;AAC1C,QAAI,SAAS,GAAG,MAAM,MAAM,cAAc;AAC1C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,yEAAyE;AAC/L,QAAI,gBAAgB,OAAO,WAAW,KAAK,MAAM;AACjD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoB,QAAQ;AACnC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,OAAO,SAAS,QAAQ,SAAS,GAAG;AAC1C,cAAU;AACV,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoBA,UAAS,QAAQ;AAC5C,QAAI,KAAK,SAAS,GAAG,MAAM,MAAM,cAAc;AAC/C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,OAAO,KAAK,EAAE;AAAA,aAAY,CAAC,OAAO,SAAS,GAAG,EAAG,OAAM,IAAI,UAAU,uDAAuD;AACtJ,mBAAe,IAAI;AACnB,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM,OAAO,KAAK,EAAE;AACvC,UAAM,IAAI;AACV,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,QAAI,KAAK,QAAQ,MAAM;AACvB,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkB,QAAQ;AACjC,QAAI,KAAK,SAAS;AAAA,MAChB,QAAQ;AAAA,IACT;AACD,UAAM,CAAC,EAAE,IAAI,OAAO,CAAC;AACrB,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkBA,UAAS,QAAQ;AAC1C,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,SAAS,OAAO,MAAM,EAAE;AAC9D,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,4CAA4C;AAChF,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoB,QAAQ;AACnC,QAAI,KAAK,SAAS;AAAA,MAChB,QAAQ;AAAA,IACT;AACD,UAAM,CAAC,EAAE,IAAI,OAAO,CAAC;AACrB,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoBA,UAAS,QAAQ;AAC5C,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,SAAS,OAAO,MAAM,EAAE;AAC9D,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,4CAA4C;AAChF,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmB,QAAQ;AAClC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,WAAW;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,MACT,UAAU;AAAA,IACX;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,YAAY;AACnB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,YAAY;AACnB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,UAAU;AACjB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,WAAW;AAClB,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmBA,UAAS,QAAQ;AAC3C,QAAI,SAAS,GAAG,MAAM,MAAM,cAAc;AAC1C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,yEAAyE;AAC/L,QAAI,gBAAgB,OAAO,WAAW,KAAK,MAAM;AACjD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,+CAA+C;AACnF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,qEAAqE;AACnI,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,6CAA6C;AACjF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,mEAAmE;AACjI,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,8CAA8C;AAClF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,oEAAoE;AAClI,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqB,QAAQ;AACpC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,qBAAqBA,UAAS,QAAQ;AAC7C,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoB,QAAQ;AACnC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,OAAO;AAAA,MACP,WAAW;AAAA,MACX,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,IACP;AACD,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,QAAQ;AACf,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,YAAY;AACnB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,UAAU;AACjB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,SAAS;AAChB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,QAAQ;AACf,UAAM,CAAC,EAAE,KAAK,OAAO,MAAM;AAC3B,WAAO,OAAO;AACd,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoBA,UAAS,QAAQ;AAC5C,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc;AACpD,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAkB,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,qEAAqE;AAChM,QAAI,YAAY,OAAO,WAAW,KAAK,MAAM;AAC7C,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,sBAAsB,QAAQ;AACrC,QAAI,KAAK,SAAS,GAAG,SAAS;AAAA,MAC5B,QAAQ;AAAA,IACT;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,WAAS,sBAAsBA,UAAS,QAAQ;AAC9C,QAAI,SAAS,GAAG,MAAM,MAAM,SAAS,OAAO,MAAM,EAAE;AACpD,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kEAAkE;AAClK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,sBAAsB,QAAQ;AACrC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,WAAW;AAClB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,OAAO;AACd,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,UAAU;AACjB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,UAAU;AACjB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,aAAa;AACpB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,WAAW;AAClB,UAAM,CAAC,EAAE,KAAK,OAAO,MAAM;AAC3B,WAAO,SAAS;AAChB;AACA,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,aAAa,OAAO,SAAS,QAAQ,SAAS,GAAG,CAAC;AACxD,cAAU;AACV,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,sBAAsBA,UAAS,QAAQ;AAC9C,QAAI,KAAK,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc,GAAG,gBAAgB;AAC5E,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,8CAA8C;AAClF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,wEAAwE;AAC3J,QAAI,eAAe,OAAO,WAAW,KAAK,MAAM;AAChD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAmB,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,oEAAoE;AAChM,QAAI,WAAW,OAAO,WAAW,KAAK,MAAM;AAC5C,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;aAAa,YAAY,OAAO,IAAK,OAAM,IAAI,UAAU,wDAAwD;AAC3I,UAAM,YAAY,SAAS,KAAK,aAAa;AAC7C,QAAI,oBAAoB,QAAQ,MAAM,eAAe,gBAAgB,GAAG;AACxE,qBAAiB;AACjB,mBAAe,kBAAkB;AACjC,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kEAAkE;AAClK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO;AACP,cAAU,kBAAkB,KAAK,QAAQ,MAAM;AAC/C,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,wBAAwB,QAAQ;AACvC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,wBAAwBA,UAAS,QAAQ;AAChD,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqB,QAAQ;AACpC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,IACT;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,WAAW;AAClB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,WAAW;AAClB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqBA,UAAS,QAAQ;AAC7C,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc;AACpD,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,8CAA8C;AAClF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,wEAAwE;AAC3J,QAAI,eAAe,OAAO,WAAW,KAAK,MAAM;AAChD,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kEAAkE;AAClK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,uBAAuB,QAAQ;AACtC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,uBAAuBA,UAAS,QAAQ;AAC/C,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmB,QAAQ;AAClC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,cAAc;AACrB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,aAAa;AACpB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,SAAS;AAChB;AACA,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,aAAa,OAAO,SAAS,QAAQ,SAAS,GAAG,CAAC;AACxD,cAAU;AACV,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmBA,UAAS,QAAQ;AAC3C,QAAI,KAAK,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc,GAAG,gBAAgB;AAC5E,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,iDAAiD;AACrF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,2EAA2E;AAC9J,QAAI,kBAAkB,OAAO,WAAW,KAAK,MAAM;AACnD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,4CAA4C;AAChF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,sEAAsE;AACzJ,QAAI,aAAa,OAAO,WAAW,KAAK,MAAM;AAC9C,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,0EAA0E;AAChM,QAAI,iBAAiB,OAAO,WAAW,KAAK,MAAM;AAClD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;aAAa,YAAY,OAAO,IAAK,OAAM,IAAI,UAAU,wDAAwD;AAC3I,UAAM,YAAY,SAAS,KAAK,aAAa;AAC7C,QAAI,oBAAoB,QAAQ,MAAM,eAAe,gBAAgB,GAAG;AACxE,qBAAiB;AACjB,mBAAe,kBAAkB;AACjC,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kEAAkE;AAClK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO;AACP,cAAU,kBAAkB,KAAK,QAAQ,MAAM;AAC/C,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqB,QAAQ;AACpC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,qBAAqBA,UAAS,QAAQ;AAC7C,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqB,QAAQ;AACpC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,cAAc;AACrB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,aAAa;AACpB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,SAAS;AAChB;AACA,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,aAAa,OAAO,SAAS,QAAQ,SAAS,GAAG,CAAC;AACxD,cAAU;AACV,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqBA,UAAS,QAAQ;AAC7C,QAAI,KAAK,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc,GAAG,gBAAgB;AAC5E,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,iDAAiD;AACrF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,2EAA2E;AAC9J,QAAI,kBAAkB,OAAO,WAAW,KAAK,MAAM;AACnD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,4CAA4C;AAChF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,sEAAsE;AACzJ,QAAI,aAAa,OAAO,WAAW,KAAK,MAAM;AAC9C,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,0EAA0E;AAChM,QAAI,iBAAiB,OAAO,WAAW,KAAK,MAAM;AAClD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;aAAa,YAAY,OAAO,IAAK,OAAM,IAAI,UAAU,wDAAwD;AAC3I,UAAM,YAAY,SAAS,KAAK,aAAa;AAC7C,QAAI,oBAAoB,QAAQ,MAAM,eAAe,gBAAgB,GAAG;AACxE,qBAAiB;AACjB,mBAAe,kBAAkB;AACjC,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kEAAkE;AAClK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO;AACP,cAAU,kBAAkB,KAAK,QAAQ,MAAM;AAC/C,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,uBAAuB,QAAQ;AACtC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,uBAAuBA,UAAS,QAAQ;AAC/C,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmB,QAAQ;AAClC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,QAAQ;AACf,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,UAAU;AACjB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,UAAU;AACjB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,YAAY;AACnB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,aAAa;AACpB,UAAM,CAAC,EAAE,KAAK,OAAO,MAAM;AAC3B,WAAO,SAAS;AAChB;AACA,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,aAAa,OAAO,SAAS,QAAQ,SAAS,GAAG,CAAC;AACxD,cAAU;AACV,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmBA,UAAS,QAAQ;AAC3C,QAAI,KAAK,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc,GAAG,gBAAgB;AAC5E,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,qEAAqE;AAC3L,QAAI,YAAY,OAAO,WAAW,KAAK,MAAM;AAC7C,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;aAAa,YAAY,OAAO,IAAK,OAAM,IAAI,UAAU,wDAAwD;AAC3I,UAAM,YAAY,SAAS,KAAK,aAAa;AAC7C,QAAI,oBAAoB,QAAQ,MAAM,eAAe,gBAAgB,GAAG;AACxE,qBAAiB;AACjB,mBAAe,kBAAkB;AACjC,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kEAAkE;AAClK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO;AACP,cAAU,kBAAkB,KAAK,QAAQ,MAAM;AAC/C,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqB,QAAQ;AACpC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,OAAO;AAAA,MACP,cAAc;AAAA,MACd,eAAe;AAAA,IAChB;AACD,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,QAAQ;AACf,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,eAAe;AACtB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,gBAAgB;AACvB,WAAO;AAAA,EACT;AAEA,WAAS,qBAAqBA,UAAS,QAAQ;AAC7C,QAAI,SAAS,GAAG,MAAM,MAAM,cAAc;AAC1C,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,2CAA2C;AAC/E,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,qEAAqE;AACxJ,QAAI,YAAY,OAAO,WAAW,KAAK,MAAM;AAC7C,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,kDAAkD;AACtF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,wEAAwE;AACtI,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,mDAAmD;AACvF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,yEAAyE;AACvI,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,gBAAgB,QAAQ;AAC/B,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,QAAQ;AACf,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,WAAW;AAClB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,aAAa;AACpB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,SAAS;AAChB;AACA,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,aAAa,OAAO,SAAS,QAAQ,SAAS,GAAG,CAAC;AACxD,cAAU;AACV,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,gBAAgBA,UAAS,QAAQ;AACxC,QAAI,KAAK,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc,GAAG,gBAAgB;AAC5E,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,qEAAqE;AAC3L,QAAI,YAAY,OAAO,WAAW,KAAK,MAAM;AAC7C,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,8CAA8C;AAClF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,wEAAwE;AAC3J,QAAI,eAAe,OAAO,WAAW,KAAK,MAAM;AAChD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,0EAA0E;AAChM,QAAI,iBAAiB,OAAO,WAAW,KAAK,MAAM;AAClD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;aAAa,YAAY,OAAO,IAAK,OAAM,IAAI,UAAU,wDAAwD;AAC3I,UAAM,YAAY,SAAS,KAAK,aAAa;AAC7C,QAAI,oBAAoB,QAAQ,MAAM,eAAe,gBAAgB,GAAG;AACxE,qBAAiB;AACjB,mBAAe,kBAAkB;AACjC,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kEAAkE;AAClK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO;AACP,cAAU,kBAAkB,KAAK,QAAQ,MAAM;AAC/C,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkB,QAAQ;AACjC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,kBAAkBA,UAAS,QAAQ;AAC1C,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB,QAAQ;AAChC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,QAAQ;AACf,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiBA,UAAS,QAAQ;AACzC,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc;AACpD,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,qEAAqE;AAC3L,QAAI,YAAY,OAAO,WAAW,KAAK,MAAM;AAC7C,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kEAAkE;AAClK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmB,QAAQ;AAClC,QAAI,KAAK,SAAS,GAAG,SAAS;AAAA,MAC5B,cAAc;AAAA,IACf;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,eAAe;AACtB,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmBA,UAAS,QAAQ;AAC3C,QAAI,SAAS,GAAG,MAAM,MAAM,SAAS,OAAO,MAAM,EAAE;AACpD,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,kDAAkD;AACtF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,wEAAwE;AACtI,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkB,QAAQ;AACjC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,IACT;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,QAAQ;AACf,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,WAAW;AAClB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,UAAU;AACjB,UAAM,CAAC,EAAE,IAAI,OAAO,MAAM;AAC1B,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkBA,UAAS,QAAQ;AAC1C,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,cAAc;AACpD,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,qEAAqE;AAC3L,QAAI,YAAY,OAAO,WAAW,KAAK,MAAM;AAC7C,mBAAe;AACf,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kEAAkE;AAClK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoB,QAAQ;AACnC,QAAI,KAAK,SAAS,GAAG,SAAS;AAAA,MAC5B,cAAc;AAAA,IACf;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,eAAe;AACtB,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoBA,UAAS,QAAQ;AAC5C,QAAI,SAAS,GAAG,MAAM,MAAM,SAAS,OAAO,MAAM,EAAE;AACpD,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,kDAAkD;AACtF,QAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,wEAAwE;AACtI,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkB,QAAQ;AACjC,QAAI,KAAK,KAAK,SAAS,GAAG,SAAS;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW;AAAA,IACZ;AACD,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,WAAO,SAAS;AAChB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,QAAQ;AACf,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,WAAW;AAClB,UAAM,OAAO,UAAU,MAAM;AAC7B;AACA,UAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,cAAU;AACV,WAAO,aAAa;AACpB,UAAM,OAAO,aAAa,MAAM;AAChC,cAAU;AACV,UAAM,aAAa,OAAO,SAAS,QAAQ,SAAS,GAAG,CAAC;AACxD,cAAU;AACV,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkBA,UAAS,QAAQ;AAC1C,QAAI,KAAK,SAAS,GAAG,MAAM,MAAM,cAAc,GAAG,gBAAgB;AAClE,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,qEAAqE;AAC3L,QAAI,YAAY,OAAO,WAAW,KAAK,MAAM;AAC7C,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM,IAAI,MAAM,8CAA8C;AAClF,QAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,wEAAwE;AAC3J,QAAI,eAAe,OAAO,WAAW,KAAK,MAAM;AAChD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAa,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,0EAA0E;AAChM,QAAI,iBAAiB,OAAO,WAAW,KAAK,MAAM;AAClD,mBAAe;AACf,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;aAAa,YAAY,OAAO,IAAK,OAAM,IAAI,UAAU,wDAAwD;AAC3I,UAAM,YAAY,SAAS,KAAK,aAAa;AAC7C,QAAI,oBAAoB,QAAQ,MAAM,eAAe,gBAAgB,GAAG;AACxE,qBAAiB;AACjB,mBAAe,kBAAkB;AACjC,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,QAAI,WAAW,IAAK,OAAM;AAAA,aAAY,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kEAAkE;AAClK,WAAO,cAAc,KAAK,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,cAAU;AACV,cAAU,kBAAkB,KAAK,QAAQ,MAAM;AAC/C,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoB,QAAQ;AACnC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,oBAAoBA,UAAS,QAAQ;AAC5C,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,eAAe,QAAQ;AAC9B,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,eAAeA,UAAS,QAAQ;AACvC,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB,QAAQ;AAChC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,iBAAiBA,UAAS,QAAQ;AACzC,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,eAAe,QAAQ;AAC9B,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,eAAeA,UAAS,QAAQ;AACvC,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB,QAAQ;AAChC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,iBAAiBA,UAAS,QAAQ;AACzC,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB,QAAQ;AAChC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,iBAAiBA,UAAS,QAAQ;AACzC,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmB,QAAQ;AAClC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,mBAAmBA,UAAS,QAAQ;AAC3C,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoB,QAAQ;AACnC,QAAI,KAAK,SAAS;AAAA,MAChB,QAAQ;AAAA,IACT;AACD,UAAM,CAAC,EAAE,IAAI,OAAO,CAAC;AACrB,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoBA,UAAS,QAAQ;AAC5C,QAAI,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG,SAAS,OAAO,MAAM,EAAE;AAC9D,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,UAAM,OAAO;AACb,eAAW,QAAQ,MAAM;AACzB,YAAQ,QAAQ;AAChB,WAAO,MAAM,IAAI;AACjB;AACA,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,sBAAsB,QAAQ;AACrC,WAAO,CAAE;AAAA,EACX;AAEA,WAAS,sBAAsBA,UAAS,QAAQ;AAC9C,QAAI,SAAS,GAAG,SAAS,OAAO,MAAM,EAAE;AACxC,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,aAAS;AACT,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,sBAAsBA,UAAS,MAAM,QAAQ;AACpD,QAAI,KAAK,KAAK,SAAS,GAAG,QAAQ,GAAG,gBAAgB,GAAG,cAAc;AACtE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,2EAA2E;AAC9J,UAAI,kBAAkB,OAAO,WAAW,KAAK,MAAM;AACnD,qBAAe;AACf,qBAAe;AAAA,IACnB;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,+EAA+E;AAClK,UAAI,sBAAsB,OAAO,WAAW,KAAK,MAAM;AACvD,qBAAe;AACf,qBAAe;AAAA,IACnB;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,YAAY,OAAO,IAAK,OAAM,IAAI,UAAU,sDAAsD;AACtG,YAAM,YAAY,SAAS,KAAK,aAAa;AAC7C,UAAI,kBAAkB,QAAQ,MAAM,eAAe,gBAAgB,GAAG;AACtE,uBAAiB;AACjB,qBAAe,gBAAgB;AAAA,IACnC;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,wEAAwE;AACtI,qBAAe;AAAA,IACnB;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,oEAAoE;AAClI,qBAAe;AAAA,IACnB;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,6EAA6E;AAChK,UAAI,oBAAoB,OAAO,WAAW,KAAK,MAAM;AACrD,qBAAe;AACf,qBAAe;AAAA,IACnB;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,uEAAuE;AAC1J,UAAI,cAAc,OAAO,WAAW,KAAK,MAAM;AAC/C,qBAAe;AACf,qBAAe;AAAA,IACnB;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,0EAA0E;AAC7J,UAAI,iBAAiB,OAAO,WAAW,KAAK,MAAM;AAClD,qBAAe;AACf,qBAAe;AAAA,IACnB;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,yEAAyE;AAC5J,UAAI,gBAAgB,OAAO,WAAW,KAAK,MAAM;AACjD,qBAAe;AACf,qBAAe;AAAA,IACnB;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,YAAY,OAAO,OAAO,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,qEAAqE;AACnI,qBAAe;AAAA,IACnB;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,oEAAoE;AACvJ,UAAI,WAAW,OAAO,WAAW,KAAK,MAAM;AAC5C,qBAAe;AACf,qBAAe;AAAA,IACnB;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,sEAAsE;AACzJ,UAAI,aAAa,OAAO,WAAW,KAAK,MAAM;AAC9C,qBAAe;AACf,qBAAe;AAAA,IACnB;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,qEAAqE;AACxJ,UAAI,YAAY,OAAO,WAAW,KAAK,MAAM;AAC7C,qBAAe;AACf,qBAAe;AAAA,IACnB;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,UAAI,EAAE,YAAY,OAAO,OAAO,OAAO,WAAW,GAAG,IAAI,KAAM,OAAM,IAAI,UAAU,yEAAyE;AAC5J,UAAI,gBAAgB,OAAO,WAAW,KAAK,MAAM;AACjD,qBAAe;AACf,qBAAe;AAAA,IACnB;AACE,QAAI,SAAS,OAAO,MAAM,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAI;AACZ,WAAO,cAAcA,UAAS,CAAC;AAC/B,WAAO,cAAc,SAAS,CAAC;AAC/B,SAAK,cAAc,QAAQ,MAAM,EAAE;AACnC,YAAQ;AACR,aAAS;AACT,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,aAAO,MAAM,IAAI;AACjB;AACA,aAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,gBAAU;AAAA,IACd;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,aAAO,MAAM,IAAI;AACjB;AACA,aAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,gBAAU;AAAA,IACd;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,gBAAU,gBAAgB,KAAK,QAAQ,MAAM;AAAA,IACjD;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,aAAO,WAAW,KAAK,MAAM;AAC7B;AAAA,IACJ;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,aAAO,WAAW,KAAK,MAAM;AAC7B;AAAA,IACJ;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,aAAO,MAAM,IAAI;AACjB;AACA,aAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,gBAAU;AAAA,IACd;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,aAAO,MAAM,IAAI;AACjB;AACA,aAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,gBAAU;AAAA,IACd;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,aAAO,MAAM,IAAI;AACjB;AACA,aAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,gBAAU;AAAA,IACd;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,aAAO,MAAM,IAAI;AACjB;AACA,aAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,gBAAU;AAAA,IACd;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,WAAK,cAAc,QAAQ,KAAK,MAAM;AACtC,gBAAU;AAAA,IACd;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,aAAO,MAAM,IAAI;AACjB;AACA,aAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,gBAAU;AAAA,IACd;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,aAAO,MAAM,IAAI;AACjB;AACA,aAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,gBAAU;AAAA,IACd;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,aAAO,MAAM,IAAI;AACjB;AACA,aAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,gBAAU;AAAA,IACd;AACE,UAAM,OAAO;AACb,QAAI,UAAU,KAAK;AACjB,eAAS;AACT,aAAO,MAAM,IAAI;AACjB;AACA,aAAO,MAAM,KAAK,QAAQ,MAAM;AAChC,gBAAU;AAAA,IACd;AACE,WAAO,MAAM,IAAI;AACjB,WAAO,cAAc,SAAS,GAAG,CAAC;AAClC,WAAO,cAAc,OAAO,EAAE;AAC9B,WAAO,OAAO,SAAS,GAAG,SAAS,CAAC;AAAA,EACtC;AAEA,WAAS,sBAAsB,QAAQ;AACrC,QAAI,OAAO,KAAK,KAAK,SAAS;AAC9B,YAAQ,OAAO,aAAa,CAAC;AAC7B,QAAI,MAAM,MAAO,QAAO,CAAE;AAC1B,QAAI,SAAS;AAAA,MACX,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,UAAU;AAAA,MACV,eAAe;AAAA,MACf,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,IACZ;AACD,QAAI,QAAQ,OAAO;AACjB,YAAM,OAAO,UAAU,MAAM;AAC7B;AACA,YAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,gBAAU;AACV,aAAO,cAAc;AAAA,IACzB;AACE,QAAI,QAAQ,OAAO;AACjB,YAAM,OAAO,UAAU,MAAM;AAC7B;AACA,YAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,gBAAU;AACV,aAAO,kBAAkB;AAAA,IAC7B;AACE,QAAI,OAAO,OAAO;AAChB,YAAM,OAAO,aAAa,MAAM;AAChC,gBAAU;AACV,YAAM,aAAa,OAAO,SAAS,QAAQ,SAAS,GAAG,CAAC;AACxD,gBAAU;AACV,aAAO,UAAU;AAAA,IACrB;AACE,QAAI,OAAO,OAAO;AAChB,YAAM,OAAO,MAAM;AACnB;AACA,aAAO,eAAe;AAAA,IAC1B;AACE,QAAI,OAAO,OAAO;AAChB,YAAM,OAAO,MAAM;AACnB;AACA,aAAO,WAAW;AAAA,IACtB;AACE,QAAI,OAAO,OAAO;AAChB,YAAM,OAAO,UAAU,MAAM;AAC7B;AACA,YAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,gBAAU;AACV,aAAO,gBAAgB;AAAA,IAC3B;AACE,QAAI,MAAM,OAAO;AACf,YAAM,OAAO,UAAU,MAAM;AAC7B;AACA,YAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,gBAAU;AACV,aAAO,UAAU;AAAA,IACrB;AACE,QAAI,MAAM,OAAO;AACf,YAAM,OAAO,UAAU,MAAM;AAC7B;AACA,YAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,gBAAU;AACV,aAAO,aAAa;AAAA,IACxB;AACE,QAAI,MAAM,OAAO;AACf,YAAM,OAAO,UAAU,MAAM;AAC7B;AACA,YAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,gBAAU;AACV,aAAO,YAAY;AAAA,IACvB;AACE,QAAI,KAAK,OAAO;AACd,YAAM,KAAK,aAAa,QAAQ,MAAM;AACtC,gBAAU;AACV,aAAO,YAAY;AAAA,IACvB;AACE,QAAI,KAAK,OAAO;AACd,YAAM,OAAO,UAAU,MAAM;AAC7B;AACA,YAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,gBAAU;AACV,aAAO,OAAO;AAAA,IAClB;AACE,QAAI,KAAK,OAAO;AACd,YAAM,OAAO,UAAU,MAAM;AAC7B;AACA,YAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,gBAAU;AACV,aAAO,SAAS;AAAA,IACpB;AACE,QAAI,IAAI,OAAO;AACb,YAAM,OAAO,UAAU,MAAM;AAC7B;AACA,YAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,gBAAU;AACV,aAAO,QAAQ;AAAA,IACnB;AACE,QAAI,IAAI,OAAO;AACb,YAAM,OAAO,UAAU,MAAM;AAC7B;AACA,YAAM,OAAO,SAAS,QAAQ,QAAQ,SAAS,GAAG;AAClD,gBAAU;AACV,aAAO,YAAY;AAAA,IACvB;AACE,WAAO;AAAA,EACT;AAEA,MAAIC,SAAQP,gBAAoB,OAAOC,sBAA2B,GAAE,cAAcM,OAAM,aAAa,eAAeA,OAAM,cAAc,UAAU,OAAO,MAAM,KAAK;AAEpK,OAAA,YAA2B;AAAA,IACzB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,cAAc;AAAA,IACd,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,EACjB;AAED,OAAA,gBAA+B;AAAA,IAC7B,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,EACT;AAED,OAAA,iBAAgC;AAEhC,OAAA,SAAwB,SAAS,IAAI,KAAK;AACxC,YAAQ,IAAE;AAAA,MACT,KAAK;AACJ,eAAO,eAAe,GAAG;AAAA,MAE1B,KAAK;AACJ,eAAO,iBAAoB;AAAA,MAE5B,KAAK;AACJ,eAAO,mBAAmB,GAAG;AAAA,MAE9B,KAAK;AACJ,eAAO,qBAAqB,GAAG;AAAA,MAEhC,KAAK;AACJ,eAAO,kBAAkB,GAAG;AAAA,MAE7B,KAAK;AACJ,eAAO,oBAAoB,GAAG;AAAA,MAE/B,KAAK;AACJ,eAAO,mBAAmB,GAAG;AAAA,MAE9B,KAAK;AACJ,eAAO,kBAAkB,GAAG;AAAA,MAE7B,KAAK;AACJ,eAAO,mBAAmB,GAAG;AAAA,MAE9B,KAAK;AACJ,eAAO,eAAe,GAAG;AAAA,MAE1B,KAAK;AACJ,eAAO,iBAAiB,GAAG;AAAA,MAE5B,KAAK;AACJ,eAAO,oBAAoB,GAAG;AAAA,MAE/B,KAAK;AACJ,eAAO,eAAe,GAAG;AAAA,MAE1B,KAAK;AACJ,eAAO,kBAAkB,GAAG;AAAA,MAE7B,KAAK;AACJ,eAAO,wBAAwB,GAAG;AAAA,MAEnC,KAAK;AACJ,eAAO,mBAAmB,GAAG;AAAA,MAE9B,KAAK;AACJ,eAAO,qBAAwB;AAAA,MAEhC,KAAK;AACJ,eAAO,gBAAgB,GAAG;AAAA,MAE3B,KAAK;AACJ,eAAO,sBAAsB,GAAG;AAAA,MAEjC,KAAK;AACJ,eAAO,wBAAwB,GAAG;AAAA,MAEnC,KAAK;AACJ,eAAO,uBAAuB,GAAG;AAAA,MAElC,KAAK;AACJ,eAAO,yBAAyB,GAAG;AAAA,MAEpC,KAAK;AACJ,eAAO,qBAAqB,GAAG;AAAA,MAEhC,KAAK;AACJ,eAAO,uBAAuB,GAAG;AAAA,MAElC,KAAK;AACJ,eAAO,qBAAqB,GAAG;AAAA,MAEhC,KAAK;AACJ,eAAO,uBAAuB,GAAG;AAAA,MAElC,KAAK;AACJ,eAAO,sBAAsB,GAAG;AAAA,MAEjC,KAAK;AACJ,eAAO,wBAA2B;AAAA,MAEnC,KAAK;AACJ,eAAO,wBAAwB,GAAG;AAAA,MAEnC,KAAK;AACJ,eAAO,0BAA6B;AAAA,MAErC,KAAK;AACJ,eAAO,6BAA6B,GAAG;AAAA,MAExC,KAAK;AACJ,eAAO,+BAAkC;AAAA,MAE1C,KAAK;AACJ,eAAO,kBAAkB,GAAG;AAAA,MAE7B,KAAK;AACJ,eAAO,oBAAoB,GAAG;AAAA,MAE/B,KAAK;AACJ,eAAO,kBAAkB,GAAG;AAAA,MAE7B,KAAK;AACJ,eAAO,oBAAoB,GAAG;AAAA,MAE/B,KAAK;AACJ,eAAO,mBAAmB,GAAG;AAAA,MAE9B,KAAK;AACJ,eAAO,qBAAwB;AAAA,MAEhC,KAAK;AACJ,eAAO,oBAAoB,GAAG;AAAA,MAE/B,KAAK;AACJ,eAAO,sBAAsB,GAAG;AAAA,MAEjC,KAAK;AACJ,eAAO,sBAAsB,GAAG;AAAA,MAEjC,KAAK;AACJ,eAAO,wBAA2B;AAAA,MAEnC,KAAK;AACJ,eAAO,qBAAqB,GAAG;AAAA,MAEhC,KAAK;AACJ,eAAO,uBAA0B;AAAA,MAElC,KAAK;AACJ,eAAO,mBAAmB,GAAG;AAAA,MAE9B,KAAK;AACJ,eAAO,qBAAwB;AAAA,MAEhC,KAAK;AACJ,eAAO,qBAAqB,GAAG;AAAA,MAEhC,KAAK;AACJ,eAAO,uBAA0B;AAAA,MAElC,KAAK;AACJ,eAAO,mBAAmB,GAAG;AAAA,MAE9B,KAAK;AACJ,eAAO,qBAAqB,GAAG;AAAA,MAEhC,KAAK;AACJ,eAAO,gBAAgB,GAAG;AAAA,MAE3B,KAAK;AACJ,eAAO,kBAAqB;AAAA,MAE7B,KAAK;AACJ,eAAO,iBAAiB,GAAG;AAAA,MAE5B,KAAK;AACJ,eAAO,mBAAmB,GAAG;AAAA,MAE9B,KAAK;AACJ,eAAO,kBAAkB,GAAG;AAAA,MAE7B,KAAK;AACJ,eAAO,oBAAoB,GAAG;AAAA,MAE/B,KAAK;AACJ,eAAO,kBAAkB,GAAG;AAAA,MAE7B,KAAK;AACJ,eAAO,oBAAuB;AAAA,MAE/B,KAAK;AACJ,eAAO,eAAkB;AAAA,MAE1B,KAAK;AACJ,eAAO,iBAAoB;AAAA,MAE5B,KAAK;AACJ,eAAO,eAAkB;AAAA,MAE1B,KAAK;AACJ,eAAO,iBAAoB;AAAA,MAE5B,KAAK;AACJ,eAAO,iBAAoB;AAAA,MAE5B,KAAK;AACJ,eAAO,mBAAsB;AAAA,MAE9B,KAAK;AACJ,eAAO,oBAAoB,GAAG;AAAA,MAE/B,KAAK;AACJ,eAAO,sBAAyB;AAAA,MAEjC,KAAK;AACJ,eAAO,sBAAsB,GAAG;AAAA,MAEjC;AACC,cAAM,IAAI,MAAM,yBAAyB;AAAA,IAC7C;AAAA,EACC;AAED,OAAA,eAA8B,SAAS,IAAID,UAAS,QAAQ;AAC1D,YAAQ,IAAE;AAAA,MACT,KAAK;AACJ,eAAO,eAAeA,UAAS,MAAM;AAAA,MAEtC,KAAK;AACJ,eAAO,iBAAiBA,QAAe;AAAA,MAExC,KAAK;AACJ,eAAO,mBAAmBA,UAAS,MAAM;AAAA,MAE1C,KAAK;AACJ,eAAO,qBAAqBA,UAAS,MAAM;AAAA,MAE5C,KAAK;AACJ,eAAO,kBAAkBA,UAAS,MAAM;AAAA,MAEzC,KAAK;AACJ,eAAO,oBAAoBA,UAAS,MAAM;AAAA,MAE3C,KAAK;AACJ,eAAO,mBAAmBA,UAAS,MAAM;AAAA,MAE1C,KAAK;AACJ,eAAO,kBAAkBA,UAAS,MAAM;AAAA,MAEzC,KAAK;AACJ,eAAO,mBAAmBA,UAAS,MAAM;AAAA,MAE1C,KAAK;AACJ,eAAO,eAAeA,UAAS,MAAM;AAAA,MAEtC,KAAK;AACJ,eAAO,iBAAiBA,UAAS,MAAM;AAAA,MAExC,KAAK;AACJ,eAAO,oBAAoBA,UAAS,MAAM;AAAA,MAE3C,KAAK;AACJ,eAAO,eAAeA,UAAS,MAAM;AAAA,MAEtC,KAAK;AACJ,eAAO,kBAAkBA,UAAS,MAAM;AAAA,MAEzC,KAAK;AACJ,eAAO,wBAAwBA,UAAS,MAAM;AAAA,MAE/C,KAAK;AACJ,eAAO,mBAAmBA,UAAS,MAAM;AAAA,MAE1C,KAAK;AACJ,eAAO,qBAAqBA,QAAe;AAAA,MAE5C,KAAK;AACJ,eAAO,gBAAgBA,UAAS,MAAM;AAAA,MAEvC,KAAK;AACJ,eAAO,sBAAsBA,UAAS,MAAM;AAAA,MAE7C,KAAK;AACJ,eAAO,wBAAwBA,UAAS,MAAM;AAAA,MAE/C,KAAK;AACJ,eAAO,uBAAuBA,UAAS,MAAM;AAAA,MAE9C,KAAK;AACJ,eAAO,yBAAyBA,UAAS,MAAM;AAAA,MAEhD,KAAK;AACJ,eAAO,qBAAqBA,UAAS,MAAM;AAAA,MAE5C,KAAK;AACJ,eAAO,uBAAuBA,UAAS,MAAM;AAAA,MAE9C,KAAK;AACJ,eAAO,qBAAqBA,UAAS,MAAM;AAAA,MAE5C,KAAK;AACJ,eAAO,uBAAuBA,UAAS,MAAM;AAAA,MAE9C,KAAK;AACJ,eAAO,sBAAsBA,UAAS,MAAM;AAAA,MAE7C,KAAK;AACJ,eAAO,wBAAwBA,QAAe;AAAA,MAE/C,KAAK;AACJ,eAAO,wBAAwBA,UAAS,MAAM;AAAA,MAE/C,KAAK;AACJ,eAAO,0BAA0BA,QAAe;AAAA,MAEjD,KAAK;AACJ,eAAO,6BAA6BA,UAAS,MAAM;AAAA,MAEpD,KAAK;AACJ,eAAO,+BAA+BA,QAAe;AAAA,MAEtD,KAAK;AACJ,eAAO,kBAAkBA,UAAS,MAAM;AAAA,MAEzC,KAAK;AACJ,eAAO,oBAAoBA,UAAS,MAAM;AAAA,MAE3C,KAAK;AACJ,eAAO,kBAAkBA,UAAS,MAAM;AAAA,MAEzC,KAAK;AACJ,eAAO,oBAAoBA,UAAS,MAAM;AAAA,MAE3C,KAAK;AACJ,eAAO,mBAAmBA,UAAS,MAAM;AAAA,MAE1C,KAAK;AACJ,eAAO,qBAAqBA,QAAe;AAAA,MAE5C,KAAK;AACJ,eAAO,oBAAoBA,UAAS,MAAM;AAAA,MAE3C,KAAK;AACJ,eAAO,sBAAsBA,UAAS,MAAM;AAAA,MAE7C,KAAK;AACJ,eAAO,sBAAsBA,UAAS,MAAM;AAAA,MAE7C,KAAK;AACJ,eAAO,wBAAwBA,QAAe;AAAA,MAE/C,KAAK;AACJ,eAAO,qBAAqBA,UAAS,MAAM;AAAA,MAE5C,KAAK;AACJ,eAAO,uBAAuBA,QAAe;AAAA,MAE9C,KAAK;AACJ,eAAO,mBAAmBA,UAAS,MAAM;AAAA,MAE1C,KAAK;AACJ,eAAO,qBAAqBA,QAAe;AAAA,MAE5C,KAAK;AACJ,eAAO,qBAAqBA,UAAS,MAAM;AAAA,MAE5C,KAAK;AACJ,eAAO,uBAAuBA,QAAe;AAAA,MAE9C,KAAK;AACJ,eAAO,mBAAmBA,UAAS,MAAM;AAAA,MAE1C,KAAK;AACJ,eAAO,qBAAqBA,UAAS,MAAM;AAAA,MAE5C,KAAK;AACJ,eAAO,gBAAgBA,UAAS,MAAM;AAAA,MAEvC,KAAK;AACJ,eAAO,kBAAkBA,QAAe;AAAA,MAEzC,KAAK;AACJ,eAAO,iBAAiBA,UAAS,MAAM;AAAA,MAExC,KAAK;AACJ,eAAO,mBAAmBA,UAAS,MAAM;AAAA,MAE1C,KAAK;AACJ,eAAO,kBAAkBA,UAAS,MAAM;AAAA,MAEzC,KAAK;AACJ,eAAO,oBAAoBA,UAAS,MAAM;AAAA,MAE3C,KAAK;AACJ,eAAO,kBAAkBA,UAAS,MAAM;AAAA,MAEzC,KAAK;AACJ,eAAO,oBAAoBA,QAAe;AAAA,MAE3C,KAAK;AACJ,eAAO,eAAeA,QAAe;AAAA,MAEtC,KAAK;AACJ,eAAO,iBAAiBA,QAAe;AAAA,MAExC,KAAK;AACJ,eAAO,eAAeA,QAAe;AAAA,MAEtC,KAAK;AACJ,eAAO,iBAAiBA,QAAe;AAAA,MAExC,KAAK;AACJ,eAAO,iBAAiBA,QAAe;AAAA,MAExC,KAAK;AACJ,eAAO,mBAAmBA,QAAe;AAAA,MAE1C,KAAK;AACJ,eAAO,oBAAoBA,UAAS,MAAM;AAAA,MAE3C,KAAK;AACJ,eAAO,sBAAsBA,QAAe;AAAA,MAE7C;AACC,cAAM,IAAI,MAAM,yBAAyB;AAAA,IAC7C;AAAA,EACC;AAE8B,OAAA,mBAAG,SAAS,IAAIA,UAAS,MAAM,QAAQ;AACpE,YAAQ,IAAE;AAAA,MACT,KAAK;AACJ,eAAO,sBAAsBA,UAAS,MAAM,MAAM;AAAA,MAEnD;AACC,cAAM,IAAI,MAAM,6BAA6B;AAAA,IACjD;AAAA,EACC;AAEkB,OAAA,OAAG,SAAS,IAAI;AACjC,YAAQ,IAAE;AAAA,MACT,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AACJ,eAAO;AAAA,MAER;AACC,cAAM,IAAI,MAAM,yBAAyB;AAAA,IAC7C;AAAA,EACC;AAED,OAAA,WAA0B;AAE1B,MAAI,qBAAqB,KAAA,qBAAoC;AAAA,IAC3D,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,aAA4B;AAE5B,MAAI,uBAAuB,KAAA,uBAAsC;AAAA,IAC/D,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,eAA8B;AAE9B,MAAI,yBAAyB,KAAA,yBAAwC;AAAA,IACnE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAA;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,iBAAgC;AAEhC,MAAI,2BAA2B,KAAA,2BAA0C;AAAA,IACvE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,cAA6B;AAE7B,MAAI,wBAAwB,KAAA,wBAAuC;AAAA,IACjE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,gBAA+B;AAE/B,MAAI,0BAA0B,KAAA,0BAAyC;AAAA,IACrE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,eAA8B;AAE9B,MAAI,yBAAyB,KAAA,yBAAwC;AAAA,IACnE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,cAA6B;AAE7B,MAAI,wBAAwB,KAAA,wBAAuC;AAAA,IACjE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,eAA8B;AAE9B,MAAI,yBAAyB,KAAA,yBAAwC;AAAA,IACnE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,WAA0B;AAE1B,MAAI,qBAAqB,KAAA,qBAAoC;AAAA,IAC3D,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,aAA4B;AAE5B,MAAI,uBAAuB,KAAA,uBAAsC;AAAA,IAC/D,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,gBAA+B;AAE/B,MAAI,0BAA0B,KAAA,0BAAyC;AAAA,IACrE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,WAA0B;AAE1B,MAAI,qBAAqB,KAAA,qBAAoC;AAAA,IAC3D,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,cAA6B;AAE7B,MAAI,wBAAwB,KAAA,wBAAuC;AAAA,IACjE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,oBAAmC;AAEnC,MAAI,8BAA8B,KAAA,8BAA6C;AAAA,IAC7E,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,eAA8B;AAE9B,MAAI,yBAAyB,KAAA,yBAAwC;AAAA,IACnE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,iBAAgC;AAEhC,MAAI,2BAA2B,KAAA,2BAA0C;AAAA,IACvE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,YAA2B;AAE3B,MAAI,sBAAsB,KAAA,sBAAqC;AAAA,IAC7D,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,kBAAiC;AAEjC,MAAI,4BAA4B,KAAA,4BAA2C;AAAA,IACzE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,oBAAmC;AAEnC,MAAI,8BAA8B,KAAA,8BAA6C;AAAA,IAC7E,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,mBAAkC;AAElC,MAAI,6BAA6B,KAAA,6BAA4C;AAAA,IAC3E,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,qBAAoC;AAEpC,MAAI,+BAA+B,KAAA,+BAA8C;AAAA,IAC/E,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,iBAAgC;AAEhC,MAAI,2BAA2B,KAAA,2BAA0C;AAAA,IACvE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,mBAAkC;AAElC,MAAI,6BAA6B,KAAA,6BAA4C;AAAA,IAC3E,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,iBAAgC;AAEhC,MAAI,2BAA2B,KAAA,2BAA0C;AAAA,IACvE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,mBAAkC;AAElC,MAAI,6BAA6B,KAAA,6BAA4C;AAAA,IAC3E,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,kBAAiC;AAEjC,MAAI,4BAA4B,KAAA,4BAA2C;AAAA,IACzE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,oBAAmC;AAEnC,MAAI,8BAA8B,KAAA,8BAA6C;AAAA,IAC7E,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,oBAAmC;AAEnC,MAAI,8BAA8B,KAAA,8BAA6C;AAAA,IAC7E,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,sBAAqC;AAErC,MAAI,gCAAgC,KAAA,gCAA+C;AAAA,IACjF,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,yBAAwC;AAExC,MAAI,mCAAmC,KAAA,mCAAkD;AAAA,IACvF,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,2BAA0C;AAE1C,MAAI,qCAAqC,KAAA,qCAAoD;AAAA,IAC3F,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,cAA6B;AAE7B,MAAI,wBAAwB,KAAA,wBAAuC;AAAA,IACjE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,gBAA+B;AAE/B,MAAI,0BAA0B,KAAA,0BAAyC;AAAA,IACrE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,cAA6B;AAE7B,MAAI,wBAAwB,KAAA,wBAAuC;AAAA,IACjE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,gBAA+B;AAE/B,MAAI,0BAA0B,KAAA,0BAAyC;AAAA,IACrE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,eAA8B;AAE9B,MAAI,yBAAyB,KAAA,yBAAwC;AAAA,IACnE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,iBAAgC;AAEhC,MAAI,2BAA2B,KAAA,2BAA0C;AAAA,IACvE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,gBAA+B;AAE/B,MAAI,0BAA0B,KAAA,0BAAyC;AAAA,IACrE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,kBAAiC;AAEjC,MAAI,4BAA4B,KAAA,4BAA2C;AAAA,IACzE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,kBAAiC;AAEjC,MAAI,4BAA4B,KAAA,4BAA2C;AAAA,IACzE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAA;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,oBAAmC;AAEnC,MAAI,8BAA8B,KAAA,8BAA6C;AAAA,IAC7E,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,iBAAgC;AAEhC,MAAI,2BAA2B,KAAA,2BAA0C;AAAA,IACvE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,mBAAkC;AAElC,MAAI,6BAA6B,KAAA,6BAA4C;AAAA,IAC3E,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,eAA8B;AAE9B,MAAI,yBAAyB,KAAA,yBAAwC;AAAA,IACnE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAA;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,iBAAgC;AAEhC,MAAI,2BAA2B,KAAA,2BAA0C;AAAA,IACvE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,iBAAgC;AAEhC,MAAI,2BAA2B,KAAA,2BAA0C;AAAA,IACvE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAA;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,mBAAkC;AAElC,MAAI,6BAA6B,KAAA,6BAA4C;AAAA,IAC3E,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,eAA8B;AAE9B,MAAI,yBAAyB,KAAA,yBAAwC;AAAA,IACnE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAA;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,iBAAgC;AAEhC,MAAI,2BAA2B,KAAA,2BAA0C;AAAA,IACvE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,YAA2B;AAE3B,MAAI,sBAAsB,KAAA,sBAAqC;AAAA,IAC7D,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAA;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,cAA6B;AAE7B,MAAI,wBAAwB,KAAA,wBAAuC;AAAA,IACjE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,aAA4B;AAE5B,MAAI,uBAAuB,KAAA,uBAAsC;AAAA,IAC/D,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,eAA8B;AAE9B,MAAI,yBAAyB,KAAA,yBAAwC;AAAA,IACnE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,cAA6B;AAE7B,MAAI,wBAAwB,KAAA,wBAAuC;AAAA,IACjE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,gBAA+B;AAE/B,MAAI,0BAA0B,KAAA,0BAAyC;AAAA,IACrE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAED,OAAA,cAA6B;AAE7B,MAAI,wBAAwB,KAAA,wBAAuC;AAAA,IACjE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAA;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,gBAA+B;AAE/B,MAAI,0BAA0B,KAAA,0BAAyC;AAAA,IACrE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,WAA0B;AAE1B,MAAI,qBAAqB,KAAA,qBAAoC;AAAA,IAC3D,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,aAA4B;AAE5B,MAAI,uBAAuB,KAAA,uBAAsC;AAAA,IAC/D,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,WAA0B;AAE1B,MAAI,qBAAqB,KAAA,qBAAoC;AAAA,IAC3D,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,aAA4B;AAE5B,MAAI,uBAAuB,KAAA,uBAAsC;AAAA,IAC/D,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,aAA4B;AAE5B,MAAI,uBAAuB,KAAA,uBAAsC;AAAA,IAC/D,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,eAA8B;AAE9B,MAAI,yBAAyB,KAAA,yBAAwC;AAAA,IACnE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,gBAA+B;AAE/B,MAAI,0BAA0B,KAAA,0BAAyC;AAAA,IACrE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV,CAAA;AAAA,EACF;AAED,OAAA,kBAAiC;AAEjC,MAAI,4BAA4B,KAAA,4BAA2C;AAAA,IACzE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM,CAAA;AAAA,EACP;AAED,OAAA,kBAAiC;AAEjC,MAAI,gCAAgC,KAAA,gCAA+C;AAAA,IACjF,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM,CAAE;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACV,GAAK;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAAA;AAAA,EACF;;;;;;;;AC98JD,QAAM,OAAON,sBAAA;AACb,MAAIQ,QAAOP,YAAiB;AAC5B,MAAI,YAAYO,MAAK;AACrB,MAAI,SAASA,MAAK;AAElB,QAAA,kBAAiC,SAAS,OAAO,aAAa,GAAG,GAAG,GAAG,CAAC;AAiBxE,MAAI,eAAe,UAAU,cAC7B,kBAAkB,UAAU,iBAC5B,eAAe,UAAU,cACzB,aAAa,UAAU,YACvB,YAAY,UAAU;AAGtB,QAAM,aAAa;AACnB,QAAM,gBAAgB;AACtB,QAAM,aAAa;AACnB,QAAM,qBAAqB,aAAa,gBAAgB;AACxD,QAAM,kBAAkB;AAkBxB,WAAS,YAAY,QAAQ,QAAQ;AAKnC,QAAI,OAAO,OAAO,UAAU,mBAAmB,YAAY;AACzD,aAAO,OAAO,OAAO,eAAe,MAAM,CAAC;AAAA,IAC/C;AAEE,WAAO,KAAK,YAAY,QAAQ,MAAM;AAAA,EACxC;AASA,QAAA,gBAA+B,SAAUF,UAAS,SAAS;AACzD,UAAM,YAAY,qBAAqB,QAAQ,SAAS;AAExD,UAAMG,SAAQ,OAAO,MAAM,SAAS;AAEpC,QAAI,SAAS;AAEb,aAASA,OAAM,WAAW,YAAY,MAAM;AAC5C,aAASA,OAAM,cAAcH,UAAS,MAAM;AAC5C,aAASG,OAAM,aAAa,QAAQ,QAAQ,MAAM;AAElD,YAAQ,KAAKA,QAAO,MAAM;AAC1B,cAAU,QAAQ;AAElB,IAAAA,OAAM,WAAW,WAAW,MAAM;AAElC,WAAOA;AAAA,EACR;AAQD,WAAS,WAAW,KAAK;AACvB,QAAI,IAAI,SAAS,oBAAoB;AACnC,aAAO;AAAA,IACX;AAEE,UAAM,OAAO,IAAI,UAAU,CAAC;AAC5B,UAAMH,WAAU,IAAI,aAAa,CAAC;AAClC,UAAM,OAAO,IAAI,aAAa,CAAC;AAE/B,UAAM,YAAY,qBAAqB,OAAO;AAE9C,QAAI,IAAI,SAAS,WAAW;AAC1B,aAAO;AAAA,IACX;AAEE,UAAM,WAAW,IAAI,UAAU,qBAAqB,IAAI;AAExD,QAAI,aAAa,WAAW;AAC1B,YAAM,IAAI,MAAM,eAAe;AAAA,IACnC;AAEE,WAAO;AAAA,MACL;AAAA,MACA,SAAAA;AAAA,MACA;AAAA,MACA,SAAS,IAAI,SAAS,oBAAoB,qBAAqB,IAAI;AAAA,MACnE,MAAM,IAAI,SAAS,SAAS;AAAA,IAChC;AAAA,EACA;AAEA,QAAA,aAA4B;AAE5B,MAAI,YAAY,EAAC,SAAS,EAAC;AAOD,QAAA,cAAG,CAACG,WAAU;AACtC,UAAM,UAAUA,OAAM;AACtB,UAAMH,WAAUG,OAAM;AAEtB,YAAQA,OAAM,MAAI;AAAA,MAChB,KAAK,cAAc;AACjB,cAAM,KAAK,QAAQ,aAAa,CAAC;AACjC,cAAM,OAAO,QAAQ,SAAS,CAAC;AAC/B,cAAM,SAAS,OAAO,IAAI,IAAI;AAC9B,eAAO,EAAE,IAAI,SAAAH,UAAS,OAAM;AAAA,MAClC;AAAA,MACI,KAAK,cAAc;AACjB,cAAM,KAAK,QAAQ,aAAa,CAAC;AAEjC,cAAM,OAAO,YAAY,SAAS,CAAC;AACnC,cAAM,iBAAiB,QAAQ,SAAS,EAAE;AAC1C,cAAM,SAAS,OAAO,IAAI,cAAc;AACxC,eAAO,EAAE,IAAI,SAAAA,UAAS,MAAM,OAAM;AAAA,MACxC;AAAA,MACI,KAAK;AACH,eAAO,EAAE,SAAAA,UAAS,SAAS,QAAO;AAAA,MACpC,KAAK;AACH,eAAO;AAAA,MACT;AACE,cAAM,IAAI,MAAM,wBAAwBG,OAAM,IAAI;AAAA,IACxD;AAAA,EACA;AAG4B,QAAA,gBAAG,OAAO,KAAK;AAAA,IAAC,UAAU;AAAA,IACX;AAAA,IAAG;AAAA,IAAG;AAAA,IAAG;AAAA;AAAA,IACT;AAAA,IAAG;AAAA;AAAA,IACH,UAAU;AAAA,EAAS,CAAC;AAE/D,QAAA,YAA2B;;;;;;;;ACpK3B,MAAI,SAAS;AAEb,MAAI,WAAY,OAAO,iBAAiB,aACtC,eAAe,QAAQ;AAAA,EAEzB,MAAM,IAAI;AAAA,IACR,YAAa,YAAY;AACvB,WAAK,aAAa,CAAE;AACpB,WAAK,aAAa,CAAE;AACpB,WAAK,UAAU;AACf,WAAK,gBAAgB;AAErB,WAAK,MAAM;AACX,UAAIC,QAAO;AACX,iBAAW,GAAG,SAAS,WAAY;AACjC,QAAAA,MAAK,UAAU;AACf,QAAAA,MAAK,cAAe;AAAA,MAC1B,CAAK;AAAA,IACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWE,gBAAiB;AAIf,UAAI,KAAK,QAAS;AAElB,UAAI,YAAY;AAChB,UAAI,MAAM,KAAK;AAIf,eAAS,WAAY,SAAS;AAC5B,YAAI;AACJ,eAAO,cAAc,IAAI,QAAQ,MAAO,IAAG;AACzC,cAAI,QAAQ,EAAE,KAAM;AACpB,cAAI,UAAU,MAAM;AAClB,wBAAY,IAAI,MAAM,KAAK;AAC3B,oBAAQ,KAAK,CAAC;AAAA,UACxB;AAAA,QACA;AAAA,MACA;AAEI,iBAAW,KAAK,UAAU;AAO1B,UAAI,WAAW;AAEb,eAAO,MAAM,GAAG,KAAK,WAAW,MAAM;AACtC,mBAAW,KAAK,UAAU;AAAA,MAChC,OACS;AACH,eAAO,KAAK,WAAW,SAAS,GAAG,mCAAmC;AACtE,cAAM,UAAU,KAAK,MAAM,KAAK,YAAY,KAAK,UAAU;AAC3D,aAAK,aAAa,CAAE;AAAA,MAC1B;AAII,WAAK,UAAU,CAAC;AAAA,IACpB;AAAA,IAEE,gBAAiB;AACf,UAAIA,QAAO;AAEX,UAAI,CAACA,MAAK,eAAe;AACvB,iBAAS,WAAY;AACnB,UAAAA,MAAK,gBAAgB;AACrB,UAAAA,MAAK,cAAe;AAAA,QAC5B,CAAO;AACD,QAAAA,MAAK,gBAAgB;AAAA,MAC3B;AAAA,IACA;AAAA,IAEE,SAAU,UAAU;AAClB,UAAIA,QAAO;AAEX,eAAS,UAAW;AAClB,QAAAA,MAAK,WAAW,KAAK,QAAQ;AAC7B,QAAAA,MAAK,cAAe;AAAA,MAC1B;AAEI,eAAS,UAAW;AAClB,iBAAS,eAAe,YAAY,OAAO;AAC3C,iBAAS,eAAe,SAAS,OAAO;AACxC,iBAAS,eAAe,OAAO,OAAO;AACtC,iBAAS,eAAe,cAAc,WAAW;AAAA,MACvD;AACI,eAAS,YAAa,MAAM;AAC1B,YAAI,SAASA,MAAM,SAAS;AAAA,MAClC;AAEI,eAAS,GAAG,cAAc,WAAW;AACrC,eAAS,GAAG,OAAO,OAAO;AAC1B,eAAS,GAAG,SAAS,OAAO;AAC5B,eAAS,GAAG,YAAY,OAAO;AAAA,IACnC;AAAA,IAEE,WAAY,UAAU;AACpB,eAAS,KAAK,cAAc,IAAI;AAAA,IACpC;AAAA,EACA;AAEA,MAAA,MAAqB;;;;;;;;;AC5ErB,QAAI,eAAeV;AAGnB,WAAA,QAAA,cAA6B;AAAA,IAE7B,MAAM,cAAc,aAAa;AAAA,MAC/B,YAAa,UAAU,WAAW,WAAW;AAC3C,cAAO;AAEP,aAAK,WAAW;AAEhB,YAAI,aAAa,WAAW,OAAO,QAAQ;AAE3C,YAAI,OAAO,KAAK,KAAK,KAAK,MAAM,MAAM;AACtC,YAAI,UAAU,KAAK,KAAK,KAAK,MAAM,SAAS;AAE5C,aAAK,YAAY;AAAA,UACf,KAAK,aAAa,KAAK,MAAM,WAAW,IAAI;AAAA,UAAG,aAAa;AAAA,QAAC;AAG/D,YAAI,aAAa;AACjB,iBAAS,YAAa;AACpB,cAAI,CAAC,UAAW;AACd,mBAAQ,EAAE,aAAa;AAAA,eACpB;AAAE,yBAAa;AAAG,mBAAO;AAAA,UAAK;AAAA,QACzC;AACI,aAAK,YAAY;AAAA,UACf,KAAK,aAAa,KAAK,MAAM,WAAW,OAAO;AAAA,UAAG;AAAA,QAAU;AAAA,MAClE;AAAA,MAEE,QAAS;AACP,sBAAc,KAAK,SAAS;AAC5B,sBAAc,KAAK,SAAS;AAAA,MAChC;AAAA,MAEE,aAAc,OAAO,MAAM;AAEzB,YAAI,CAAC,MAAO;AACV,eAAM;AAAA,MACZ;AAAA,IACA;AAEA,WAAA,QAAA,QAAuB;AAAA;;;;;;;;ACnFvB,MAAIQ,QAAOR,YAAiB;AAC5B,MAAIW,WAAS,WAAgB;AAC7B,MAAI,YAAYC,aAAkB,EAAC;AAERC,SAAA,eAAG,SAAS,OAAO;AAC5C,QAAI,OAAO,MAAM,OAAO;AACxB,WAAOF;AAAAA,MAAO;AAAA,MACA;AAAA,MAAMH,MAAK,cAAc,IAAI;AAAA,MAC7B,MAAM,OAAO;AAAA,IAAS;AAAA,EACtC;AAEyBK,SAAA,aAAG,SAAS,IAAI;AACvC,WAAOL,MAAK,KAAK,EAAE,EAAE;AAAA,EACtB;AAEDK,SAAA,UAAyB,SAASJ,QAAO,YAAY;AACnD,QAAIA,WAAU,WAAW;AACvB,aAAO;AAAA,IACX,WACW,CAACA,OAAM,IAAI;AAClB,aAAOE;AAAAA,QAAO;AAAA,QACAF,OAAM;AAAA,QAASA,OAAM;AAAA,MAAI;AAAA,IAC3C,OACO;AACH,UAAI,OAAOD,MAAK,KAAKC,OAAM,EAAE;AAC7B,aAAOE;AAAAA,QAAO;AAAA,QAAqB,KAAK;AAAA,QAAMF,OAAM;AAAA,QACrC,aACC,MAAM,KAAK,UAAUA,OAAM,QAAQ,QAAW,CAAC,IAC/C;AAAA,MAAE;AAAA,IACtB;AAAA,EACA;;;;;;;;EC3BA,MAAM,OAAO;AAAA;AAAA;AAAA;AAAA,IAIX,YAAY,MAAM;AAChB,UAAI,MAAM;AACR,cAAM,WAAW,KAAK,KAAK,OAAO,EAAE;AACpC,aAAK,QAAQ,IAAI,MAAM,QAAQ;AAAA,MACrC,OACS;AACH,aAAK,QAAQ,CAAE;AAAA,MACrB;AACI,WAAK,aAAa;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA,IAKE,WAAW,UAAU;AACnB,YAAM,eAAe,KAAK,MAAM;AAChC,UAAI,eAAe,UAAU;AAC3B,aAAK,QAAQ,KAAK,MAAM,OAAO,IAAI,MAAM,WAAW,YAAY,CAAC;AAAA,MACvE;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKE,IAAI,UAAU;AACZ,YAAM,IAAI,UAAU,QAAQ;AAC5B,UAAI,KAAK,KAAK,YAAY;AACxB,aAAK,WAAW,IAAI,CAAC;AACrB,aAAK,aAAa,IAAI;AAAA,MAC5B;AACI,YAAM,MAAM,KAAK;AACjB,WAAK,MAAM,CAAC,KAAK;AAAA,IACrB;AAAA;AAAA;AAAA;AAAA,IAKE,MAAM,UAAU;AACd,YAAM,IAAI,UAAU,QAAQ;AAC5B,UAAI,KAAK,KAAK,WAAY;AAC1B,YAAM,OAAO,EAAE,KAAK;AACpB,WAAK,MAAM,CAAC,KAAK;AAAA,IACrB;AAAA;AAAA;AAAA;AAAA,IAKE,IAAI,UAAU;AACZ,YAAM,IAAI,UAAU,QAAQ;AAC5B,UAAI,KAAK,KAAK,WAAY,QAAO;AACjC,YAAM,MAAM,KAAK;AACjB,aAAO,CAAC,EAAE,KAAK,MAAM,CAAC,IAAI;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOE,WAAW,WAAW;AACpB,UAAI,IAAI,UAAU,SAAS;AAC3B,UAAI,KAAK,KAAK,WAAY,QAAO;AAIjC,UAAI,OAAO,KAAK,MAAM,CAAC,IAAK,cAAc;AAC1C,aAAO,MAAM;AACX,YAAI,KAAM,QAAQ,IAAI,KAAM,cAAc,IAAI;AAC9C;AACA,YAAI,MAAM,KAAK,WAAY,QAAO;AAClC,eAAO,KAAK,MAAM,CAAC;AAAA,MACzB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKE,aAAa,WAAW;AACtB,UAAI,IAAI,UAAU,SAAS;AAC3B,UAAI,KAAK,KAAK,WAAY,QAAO;AAEjC,UAAI,OAAO,CAAE,KAAK,MAAM,CAAC,IAAM,cAAc;AAC7C,aAAO,MAAM;AACX,YAAI,KAAM,QAAQ,IAAI,KAAM,cAAc,IAAI;AAC9C;AACA,YAAI,KAAK,KAAK,WAAY,QAAO,IAAI;AACrC,eAAO,CAAE,KAAK,MAAM,CAAC;AAAA,MAC3B;AAAA,IACA;AAAA,EACA;AAKA,WAAS,UAAU,UAAU;AAC3B,WAAO,KAAK,MAAM,WAAW,EAAE;AAAA,EACjC;AAKA,WAAS,cAAc,GAAG;AAIxB,QAAI,MAAM,EAAG,QAAO;AACpB,QAAI,GAAG,IAAI;AACX,QAAI,KAAK;AAAI,QAAI,KAAK,GAAG;AAAE,UAAI,IAAG;AAAI,UAAI;AAAA,IAAE;AAC5C,QAAI,KAAK;AAAI,QAAI,KAAK,GAAG;AAAE,UAAI,IAAI;AAAG,UAAI;AAAA,IAAE;AAC5C,QAAI,KAAK;AAAI,QAAI,KAAK,GAAG;AAAE,UAAI,IAAI;AAAG,UAAI;AAAA,IAAE;AAC5C,QAAI,KAAK;AAAI,QAAI,KAAK,GAAG;AAAE,UAAI,IAAI;AAAG,UAAI;AAAA,IAAE;AAC5C,WAAO,KAAM,KAAK,MAAO;AAAA,EAC3B;AAEA,SAAA,SAAwB;;;;;;;;ACjIxB,MAAI,WAAWT,WAAgB;AAE/B,WAAS,UAAU,OAAO,KAAK;AAC7B,WAAO,SAAS,MAAM,MAAM,IAAI,EAAE,MAAM,GAAG,EAAE,KAAK,IAAI;AAAA,EACxD;AAEA,WAAS,sBAAsB,KAAK,OAAO;AACzC,QAAI,MAAM,IAAI,MAAO;AACrB,SAAK,UAAU;AACf,SAAK,QAAQ,KAAK,SAAU,IAAG,OAAO,UAAU,IAAI,OAAO,CAAC;AAC5D,SAAK,qBAAqB;AAAA,EAC5B;AACA,WAAS,uBAAuB,KAAK;AAErC,wBAAsB,UAAU,OAAO;AAEvC,WAAS,aAAa,QAAQ;AAC5B,QAAI,IAAI,IAAI,MAAO;AACnB,WAAO,oBAAoB,SAAS,OAClC,UAAU,EAAE,OAAO,CAAC;AAAA,EACxB;AAEA,QAAA,wBAAuC;AACvC,QAAA,eAA8B;;;;;;;ACjB9B,MAAIQ,QAAOR,YAAiB;AAC5B,MAAI,YAAYQ,MAAK;AACrB,MAAIC,SAAQR,aAAkB;AAC9B,MAAI,YAAYQ,OAAM;AACtB,MAAI,MAAMG,WAAgB,EAAC;AAE3B,MAAI,SAAS,WAAkB;AAC/B,MAAI,eAAeE;AACnB,MAAI,QAAQC,iBAAsB,EAAC;AAEnC,MAAI,aAAaC,cAAmB,EAAC;AACrC,MAAI,WAAWA,cAAmB,EAAC;AACnC,MAAI,UAAUA,cAAmB,EAAC;AAElC,MAAI,SAASC,cAAmB,EAAC;AACjC,MAAI,MAAMC,WAAgB;AAC1B,MAAI,cAAc,WAAkB;AACpC,MAAI,wBAAwBC,aAAkB,EAAC;AAC/C,MAAI,eAAeA,aAAkB,EAAC;AAItC,MAAI,oBAAoB;AAKxB,MAAI,yBAAyB;AAAA,EAE7B,MAAM,mBAAmB,aAAa;AAAA,IACpC,YAAa,YAAY;AACvB,YAAO;AAEP,UAAI,SAAS,KAAK,SAAS,WAAW,UAAU;AAChD,WAAK,QAAQ,IAAI,IAAI,MAAM;AAG3B,WAAK,OAAO,OAAO,MAAM,CAAC;AAC1B,WAAK,WAAW,UAAU;AAC1B,WAAK,qBAAqB;AAC1B,WAAK,qBAAqB;AAE1B,WAAK,oBAAoB;AACzB,WAAK,eAAe,IAAI,OAAQ;AAChC,WAAK,WAAW,CAAC;AAAA,QACf,SAAS,EAAE,QAAQ,SAAS,IAAI,EAAG;AAAA,QACnC,QAAQ;AAAA,MACd,CAAK;AAAA,IACL;AAAA;AAAA;AAAA;AAAA;AAAA,IAME,qBAAsB;AACpB,WAAK,UAAUV,OAAM,eAAe;AAAA,IACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAwBE,KAAM,WAAW,eAAe;AAC9B,UAAIC,QAAO;AACX,UAAI,eAAe,iBAAiB,WAAY;AAAA,MAAG;AAGnD,UAAI,eAAe,OAAO,OAAO,SAAS;AAE1C,eAAS,KAAM,GAAG;AAChB,QAAAA,MAAK,KAAK,SAAU,KAAKD,QAAO;AAC9B,cAAI,QAAQ;AACV,iBAAK,GAAG;AAAA,mBACDA,OAAM,YAAY,GAAG;AAC5B,iBAAK,IAAI;AAAA,cACP;AAAA,gBAAI;AAAA,gBACF,QAAQA,QAAO,KAAK;AAAA,cAAC;AAAA,YAAC,CAAC;AAAA,UACrC;AAEU,cAAEA,MAAK;AAAA,QACjB,CAAO;AAAA,MACP;AAEI,eAAS,OAAQ,QAAQ,GAAG;AAC1B,aAAK,SAAUA,QAAO;AACpB,cAAIA,OAAM,OAAO;AACf,cAAEA,MAAK;AAAA,eACJ;AACH,iBAAK,IAAI;AAAA,cACP;AAAA,gBAAI;AAAA,gBACF,WAAW,MAAM;AAAA,gBAAG,QAAQA,QAAO,KAAK;AAAA,cAAC;AAAA,YAAC,CAAC;AAAA,UACzD;AAAA,QACA,CAAO;AAAA,MACP;AAEI,eAAS,KAAM,KAAK;AAClB,qBAAa,GAAG;AAAA,MACtB;AAEI,eAAS,KAAM,QAAQ;AAGrB,QAAAC,MAAK,WAAW,GAAG,QAAQ,YAAY;AAAA,MAC7C;AAEI,eAAS,UAAW,QAAQ,SAAS;AAMnC,YAAI,WAAW,KAAK,YAAY,GAAG;AAEjC,iBAAO,KAAK,IAAI,QAAQ,OAAO;AAAA,QACvC,OACW;AACH,iBAAO,KAAK,IAAI,QAAQ,OAAO;AAAA,QACvC;AAAA,MACA;AAEI,eAAS,QAAS,OAAO;AACvB,YAAI,aAAa,MAAM,OAAO,WAAW,SAAU,EAAC,MAAM,GAAG;AAC7D,YAAI,WAAW,QAAQ,UAAU,SAAS,IAAI,GAAG;AAC/C,eAAK,IAAI,MAAM;AAAA,YAAI;AAAA,YACjB,UAAU;AAAA,UAAS,CAAC,CAAC;AACvB;AAAA,QACR;AACM,QAAAA,MAAK,mBAAmB,MAAM,OAAO;AACrC,YAAI;AACF,eAAKF,MAAK,iBAAiB;AAAA,QAC5B,SAAQ,KAAK;AACZ,eAAK,GAAG;AACR;AAAA,QACR;AACM,aAAK,YAAY;AAAA,MACvB;AAEI,eAAS,aAAc,OAAO;AAC5B,gBAAQ,MAAM,IAAE;AAAA,UACd,KAAKA,MAAK;AACR,iBAAK,IAAI;AAAA,cACP;AAAA,YAA+C,CAAC;AAClD;AAAA,UACF,KAAKA,MAAK;AACR,iBAAK,IAAI,MAAM;AAAA,cAAI;AAAA,cACjB,SAAS,KAAK;AAAA,YAAC,CAAC,CAAC;AACnB;AAAA,UACF,KAAKA,MAAK;AACR,gBAAI,SAAS,MAAM;AACnB,yBAAa,WACX,UAAU,OAAO,UAAU,UAAU,QAAQ;AAC/C,yBAAa,aACX,UAAU,OAAO,YAAY,UAAU,UAAU;AACnD,yBAAa,YACX,UAAU,OAAO,WAAW,UAAU,SAAS;AACjD,gBAAI;AACF,mBAAKA,MAAK,gBAAgB;AAC1B,mBAAKA,MAAK,cAAc;AAAA,YACzB,SAAQ,KAAK;AACZ,mBAAK,GAAG;AACR;AAAA,YACZ;AACU,mBAAOA,MAAK,kBAAkB,QAAQ;AACtC;AAAA,UACF;AACE,iBAAK,IAAI;AAAA,cACP;AAAA,gBAAI;AAAA,gBAEF,QAAQ,OAAO,KAAK;AAAA,cAAC;AAAA,YAAC,CAAC;AAC3B;AAAA,QACV;AAAA,MACA;AAEI,eAAS,SAAU,QAAQ;AAGzB,QAAAE,MAAK,aAAa,aAAa,cAAc;AAC7C,QAAAA,MAAK,WAAW,aAAa,YAAY;AAGzC,QAAAA,MAAK,YAAY,aAAa;AAC9B,QAAAA,MAAK,cAAcA,MAAK,iBAAkB;AAC1C,QAAAA,MAAK,SAAS;AACd,gBAAQ,MAAM;AAAA,MACpB;AAII,eAAS,gBAAiB,KAAK;AAC7B,aAAK,OAAO,IAAI,MAAM,iDACM,CAAC;AAAA,MACnC;AAEI,WAAK,OAAO,GAAG,OAAO,eAAe;AACrC,WAAK,OAAO,GAAG,SAAS,eAAe;AAEvC,eAAS,QAAS,IAAI;AACpB,QAAAA,MAAK,OAAO,eAAe,OAAO,eAAe;AACjD,QAAAA,MAAK,OAAO,eAAe,SAAS,eAAe;AACnD,QAAAA,MAAK,OAAO,GAAG,SAASA,MAAK,cAAc,KAAKA,KAAI,CAAC;AACrD,QAAAA,MAAK,OAAO,GAAG,OAAOA,MAAK,cAAc;AAAA,UACvCA;AAAA,UAAM,IAAI,MAAM,kBAAkB;AAAA,QAAC,CAAC;AACtC,QAAAA,MAAK,GAAG,cAAcA,MAAK,cAAc,KAAKA,KAAI,CAAC;AACnD,QAAAA,MAAK,WAAY;AACjB,qBAAa,MAAM,EAAE;AAAA,MAC3B;AAGI,WAAK,mBAAoB;AACzB,aAAOF,MAAK,iBAAiB,OAAO;AAAA,IACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA2CE,MAAO,eAAe;AACpB,UAAI,IAAI,iBAAiB,WAAY;AAAE,sBAAc,IAAI;AAAA,MAAI;AAC7D,WAAK,aAAa,kBAAkB,UAAU,eAAe,CAAC;AAAA,IAClE;AAAA;AAAA;AAAA;AAAA,IAKE,aAAc,QAAQ,MAAM,GAAG;AAC7B,WAAK,WAAW,GAAGA,MAAK,iBAAiB;AAAA,QACvC,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,QAAG,SAAS;AAAA,MAC5B,CAAK;AACD,UAAI,IAAI,aAAa,0BAA0B,MAAM;AACrD,WAAK,UAAU,GAAG,CAAC;AAAA,IACvB;AAAA,IAEE,eAAgB,QAAQ,MAAMY,QAAO;AACnC,WAAK,KAAK,SAASA,MAAK;AACxB,WAAK,aAAa,QAAQ,IAAI;AAAA,IAClC;AAAA,IAEE,cAAe,KAAK;AAClB,UAAI,CAAC,KAAK,mBAAmB;AAG3B,aAAK,oBAAoB;AACzB,aAAK,KAAK,SAAS,GAAG;AACtB,YAAI,IAAI,aAAa,cAAc;AACnC,aAAK,SAAS,GAAG,GAAG;AAAA,MAC1B;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKE,UAAW,eAAe,GAAG;AAC3B,UAAI,OAAO,KAAK,WAAW,KAAK,IAAI;AAEpC,WAAK,SAAS,SAAU,GAAG;AACzB,YAAI,EAAE,OAAOZ,MAAK,mBAAmB;AACnC,cAAI;AACF,cAAG;AACL,cAAI,IAAI,aAAa,4BAA4B;AACjD,eAAK,SAAS,GAAG,MAAS;AAAA,QAClC,WACe,EAAE,OAAOA,MAAK,iBAAiB;AACtC,eAAK,GAAGA,MAAK,mBAAmB,CAAA,CAAE;AAAA,QAC1C;AAAA,MAEK;AACD,qBAAe,MAAM,sBAAsB,aAAa;AAAA,IAC5D;AAAA,IAEE,eAAgB,eAAe;AAC7B,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,YAAI,KAAK,KAAK,SAAS,CAAC;AACxB,YAAI,OAAO,MAAM;AACf,aAAG,QAAQ,SAAS,aAAa;AAAA,QACzC;AAAA,MACA;AAAA,IACA;AAAA;AAAA,IAGE,SAAU,eAAe,UAAU;AACjC,WAAK,eAAe,aAAa;AACjC,UAAI,OAAO;AAAA,QAAI;AAAA,QACZ,WAAY,SAAS,SAAQ,IAAK;AAAA,MAAW;AAEhD,qBAAe,MAAM,MAAM,aAAa;AACxC,WAAK,SAAS,UAAU,MAAM,aAAa;AAC3C,WAAK,QAAQ,SAAU,IAAI;AACzB,cAAM,GAAG,IAAI,sBAAsB,MAAM,aAAa,CAAC;AAAA,MACxD;AACD,UAAI,KAAK;AACP,aAAK,YAAY,MAAO;AAE1B,WAAK,oBAAoB;AACzB,WAAK,OAAO,IAAK;AACjB,WAAK,KAAK,SAAS,QAAQ;AAAA,IAC/B;AAAA,IAEE,cAAc,WAAW,QAAQ,IAAI;AACnC,WAAK,WAAW,GAAGA,MAAK,wBAAwB;AAAA,QAC9C;AAAA,QACA;AAAA,MACN,CAAK;AACD,WAAK,KAAK,oBAAoB,EAAE;AAAA,IACpC;AAAA;AAAA,IAGE,mBAAoB;AAClB,UAAI,KAAK,cAAc;AACrB,eAAO;AAAA,WACJ;AACH,YAAIE,QAAO;AACX,YAAI,KAAK,IAAI;AAAA,UAAM,KAAK;AAAA,UACtB,KAAK,UAAU,KAAK,IAAI;AAAA,UACxB,KAAK,UAAU,KAAK,IAAI;AAAA,QAAC;AAC3B,WAAG,GAAG,WAAW,WAAY;AAC3B,cAAI,QAAQ,IAAI,MAAM,mBAAmB;AACzC,UAAAA,MAAK,KAAK,SAAS,KAAK;AACxB,cAAI,IAAI,aAAa,mBAAmB;AACxC,UAAAA,MAAK,SAAS,GAAG,KAAK;AAAA,QAC9B,CAAO;AACD,WAAG,GAAG,QAAQ,WAAY;AACxB,UAAAA,MAAK,cAAe;AAAA,QAC5B,CAAO;AACD,eAAO;AAAA,MACb;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWE,aAAcJ,UAAS,SAAS;AAC9B,UAAI,OAAO,KAAK,aAAa,aAAa,CAAC;AAC3C,UAAI,OAAO,KAAK,OAAO,KAAK;AAC1B,cAAM,IAAI,MAAM,8BAA8B;AAChD,WAAK,aAAa,IAAI,IAAI;AAE1B,UAAI,MAAO,WAAW,QAAQ,iBAAkB;AAChD,UAAI,cAAc,IAAI,YAAY;AAAA,QAChC,YAAY;AAAA,QAAM,eAAe;AAAA,MACvC,CAAK;AACD,WAAK,SAAS,IAAI,IAAI,EAAE,SAASA,UAAS,QAAQ,YAAa;AAC/D,kBAAY,GAAG,SAAS,WAAY;AAClC,QAAAA,SAAQ,cAAe;AAAA,MAC7B,CAAK;AACD,WAAK,MAAM,SAAS,WAAW;AAC/B,aAAO;AAAA,IACX;AAAA,IAEE,eAAgBA,UAAS;AACvB,WAAK,aAAa,MAAMA,QAAO;AAC/B,UAAI,SAAS,KAAK,SAASA,QAAO,EAAE;AACpC,aAAO,IAAG;AACV,WAAK,SAASA,QAAO,IAAI;AAAA,IAC7B;AAAA,IAEE,aAAc;AACZ,UAAII,QAAO;AAEX,eAAS,KAAM;AACb,YAAI;AACF,cAAI;AAAG,iBAAO,IAAIA,MAAK,UAAW;AAChC,YAAAA,MAAK,OAAO,CAAC;AAAA,QACvB,SACa,GAAG;AACR,UAAAA,MAAK,KAAK,cAAc,CAAC;AAAA,QACjC;AAAA,MACA;AACI,MAAAA,MAAK,OAAO,GAAG,YAAY,EAAE;AAC7B,SAAI;AAAA,IACR;AAAA,IAEE,KAAM,IAAI;AACR,UAAIA,QAAO;AACX,eAAS,OAAQ;AACf,YAAI;AACJ,YAAI;AACF,cAAIA,MAAK,UAAW;AAAA,QAC5B,SACa,GAAG;AACR,aAAG,GAAG,IAAI;AACV;AAAA,QACR;AACM,YAAI;AACF,aAAG,MAAM,CAAC;AAAA;AAEV,UAAAA,MAAK,OAAO,KAAK,YAAY,IAAI;AAAA,MACzC;AACI,WAAM;AAAA,IACV;AAAA,IAEE,YAAa;AACX,UAAI,QAAQ,KAAK;AACjB,WAAK,qBAAqB;AAC1B,aAAO;AAAA,IACX;AAAA,IAEE,YAAa;AACX,UAAI,QAAQ,KAAK;AACjB,WAAK,qBAAqB;AAC1B,aAAO;AAAA,IACX;AAAA,IAEE,UAAW,OAAO;AAChB,WAAK,qBAAqB;AAC1B,WAAK,OAAO,MAAM,KAAK;AAAA,IAC3B;AAAA,IAEE,gBAAiB;AACf,aAAO,KAAK,UAAUD,OAAM,aAAa;AAAA,IAC7C;AAAA,IAEE,WAAYH,UAAS,QAAQ,QAAQ;AACnC,UAAIG,SAAQ,aAAa,QAAQH,UAAS,MAAM;AAChD,WAAK,qBAAqB;AAC1B,UAAI,SAAS,KAAK,SAASA,QAAO,EAAE;AACpC,aAAO,OAAO,MAAMG,MAAK;AAAA,IAC7B;AAAA,IAEE,YAAaH,UAAS,QAAQ,QAAQ,YAAY,OAAO,SAAS;AAChE,UAAI,CAAC,OAAO,SAAS,OAAO;AAC1B,cAAM,IAAI,UAAU,yBAAyB;AAE/C,UAAI,SAAS,aAAa,QAAQA,UAAS,MAAM;AACjD,UAAI,SAAS;AAAA,QAAiB;AAAA,QAAYA;AAAA,QACxC,QAAQ;AAAA,QAAQ;AAAA,MAAK;AACvB,UAAI,SAAS,KAAK,SAASA,QAAO,EAAE;AACpC,WAAK,qBAAqB;AAE1B,UAAI,kBAAkB,OAAO,SAAS,OAAO;AAC7C,UAAI,UAAW,QAAQ,SAAS,IAC9B,QAAQ,SAAS,iBAAiB;AACpC,UAAI,SAAS,kBAAkB;AAE/B,UAAI,SAAS,wBAAwB;AAKnC,YAAI,MAAM,OAAO,YAAY,MAAM;AACnC,YAAI,SAAS,OAAO,KAAK,KAAK,CAAC;AAC/B,kBAAU,OAAO,KAAK,KAAK,MAAM;AAEjC,YAAI,UAAU;AACZ,wBAAcA,UAAS,OAAO,EAAE,KAAK,KAAK,MAAM;AAClD,eAAO,OAAO,MAAM,GAAG;AAAA,MAC7B,OACS;AACH,YAAI,kBAAkB,wBAAwB;AAK5C,cAAI,OAAO,OAAO,YAAY,eAAe;AAC7C,cAAI,SAAS,OAAO,KAAK,MAAM,CAAC;AAChC,iBAAO,KAAK,MAAM,MAAM;AACxB,iBAAO,MAAM,IAAI;AAAA,QACzB,OACW;AACH,iBAAO,MAAM,MAAM;AACnB,iBAAO,MAAM,MAAM;AAAA,QAC3B;AACM,eAAO,KAAK,YAAYA,UAAS,OAAO;AAAA,MAC9C;AAAA,IACA;AAAA,IAEE,YAAaA,UAAS,MAAM;AAC1B,UAAI,CAAC,OAAO,SAAS,IAAI,GAAG;AAC1B,cAAM,IAAI,UAAU,IAAI,2BAA2B,IAAI,CAAC;AAAA,MAC9D;AACI,UAAI,cAAc;AAClB,UAAI,SAAS,KAAK,SAASA,QAAO,EAAE;AAEpC,UAAI,UAAU,KAAK,WAAW;AAE9B,eAAS,SAAS,GAAG,SAAS,KAAK,QAAQ,UAAU,SAAS;AAC5D,YAAI,MAAM,SAAS;AACnB,YAAI,QAAS,MAAM,KAAK,SAAU,KAAK,SAAS,MAAM,IAAI,KAAK,SAAS,QAAQ,GAAG;AACnF,YAAI,YAAY,cAAcA,UAAS,KAAK;AAC5C,sBAAc,OAAO,MAAM,SAAS;AAAA,MAC1C;AACI,WAAK,qBAAqB;AAC1B,aAAO;AAAA,IACX;AAAA,IAEE,YAAa;AAEX,UAAIG,SAAQ,WAAW,KAAK,IAAI;AAEhC,UAAI,CAACA,QAAO;AACV,YAAI,WAAW,KAAK,OAAO,KAAM;AACjC,YAAI,aAAa,MAAM;AACrB,iBAAO;AAAA,QACf,OACW;AACH,eAAK,qBAAqB;AAC1B,eAAK,OAAO,OAAO,OAAO,CAAC,KAAK,MAAM,QAAQ,CAAC;AAC/C,iBAAO,KAAK,UAAW;AAAA,QAC/B;AAAA,MACA,OACS;AACH,aAAK,OAAOA,OAAM;AAClB,eAAO,YAAYA,MAAK;AAAA,MAC9B;AAAA,IACA;AAAA,EACA;AAGA,WAAS,WAAWA,QAAO;AACzB,QAAI,MAAM,KAAK,SAASA,OAAM,OAAO;AACrC,QAAI,KAAK;AAAE,aAAO,IAAI,QAAQ,OAAOA,MAAK;AAAA,IAAE;AAG1C,WAAK;AAAA,QACH,IAAI,+BAA+BA,OAAM,OAAO;AAAA,QAChD,UAAU;AAAA,QACV,IAAI,MAAM;AAAA,UAAI;AAAA,UACA,QAAQA,QAAO,KAAK;AAAA,QAAC,CAAC;AAAA,MAAC;AAAA,EAC3C;AAKA,WAAS,SAASY,aAAY;AAC5B,WAAO,SAAS,GAAG;AAMjB,UAAI,MAAM,UAAU;AAAA,eAEX,EAAE,OAAOb,MAAK,iBAAiB;AAEtC,QAAAa,YAAW,WAAW,GAAGb,MAAK,mBAAmB,CAAA,CAAE;AACnD,YAAI,OAAO,IAAI,yBAAyB,SAAS,CAAC,CAAC;AACnD,YAAI,IAAI,aAAa,IAAI;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI;AACtB,UAAE,OAAO,EAAE,OAAO;AAClB,YAAI,aAAa,CAAC,GAAG;AACnB,UAAAa,YAAW,KAAK,SAAS,CAAC;AAAA,QAClC;AACM,QAAAA,YAAW,SAAS,GAAG,CAAC;AAAA,MAC9B,WACa,EAAE,OAAOb,MAAK,mBAAmB;AACxC,QAAAa,YAAW,KAAK,WAAW,EAAE,OAAO,MAAM;AAAA,MAChD,WACa,EAAE,OAAOb,MAAK,qBAAqB;AAC1C,QAAAa,YAAW,KAAK,WAAW;AAAA,MACjC,WACa,EAAE,OAAOb,MAAK,0BAA0B;AAC/C,QAAAa,YAAW,KAAK,kBAAkB;AAAA,MACxC,OACS;AACH,QAAAA,YAAW;AAAA,UACT,IAAI,+BAA+B;AAAA,UACnC,UAAU;AAAA,UACV,IAAI,MAAM;AAAA,YAAI;AAAA,YACA,QAAQ,GAAG,KAAK;AAAA,UAAC,CAAC;AAAA,QAAC;AAAA,MACzC;AAAA,IACG;AAAA,EACH;AAEA,WAAS,UAAU,KAAK,OAAO;AAC7B,WAAO,WAAW;AAChB,YAAM,IAAI,sBAAsB,KAAK,KAAK;AAAA,IAC3C;AAAA,EACH;AAEA,WAAS,eAAe,MAAM,KAAK,OAAO;AACxC,SAAK,aAAa,KAAK,cAAc,KAAK,cACxC,UAAU,KAAK,KAAK;AAAA,EACxB;AAEA,MAAI,eAAeb,MAAK;AACxB,MAAI,mBAAmBA,MAAK;AAE5B,MAAI,iBAAiBA,MAAK;AAC1B,MAAI,gBAAgBC,OAAM;AAE1B,MAAI,aAAaA,OAAM;AACvB,MAAI,cAAcA,OAAM;AAExB,WAAS,WAAW,GAAG;AACrB,QAAI,aAAa,OAAQ,QAAO;AAAA,SAC3B;AACH,UAAI,KAAK,IAAI,OAAQ;AACrB,SAAG,KAAK,CAAC;AACT,SAAG,SAAS,SAAS,OAAO,UAAU,UAAU;AAC9C,eAAO,EAAE,MAAM,OAAO,UAAU,QAAQ;AAAA,MACzC;AACD,aAAO;AAAA,IACX;AAAA,EACA;AAEA,WAAS,aAAaW,QAAO;AAC3B,YAAQA,UAASA,OAAM,MAAI;AAAA,MAC3B,KAAKZ,MAAK,UAAU;AAAA,MACpB,KAAKA,MAAK,UAAU;AAClB,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACA;AAEA,aAAA,aAA4B;AAC5B,aAAA,eAA8B;;;;;;;;ACxpB9B,MAAID,SAAQP,aAAA;AAEZ,cAAA,QAAuB,SAAS,MAAM,QAAQ;AAC5C,WAAO;AAAA,MACL,WAAW;AAAA,MACX,UAAU,WAAW;AACnB,eAAO,OAAO,KAAK,CAAC,IAAI,MAAM,MAAM,EAAE,KAAK,OAAO,aAAa,CAAC,CAAC,CAAC;AAAA,MACnE;AAAA,MACD,UAAU;AAAA,MACV,UAAU;AAAA,IACd;AAAA,EACA;AAEA,cAAA,WAA0B,SAAS,MAAM,QAAQ;AAC/C,WAAO;AAAA,MACL,WAAW;AAAA,MACX,UAAU,WAAW;AACnB,cAAM,SAAS,OAAO,MAAM,KAAK;AACjC,cAAM,OAAOO,OAAM,YAAY,QAAQ,EAAE,OAAO,MAAM,UAAU,OAAM,GAAG,CAAC;AAC1E,eAAO,OAAO,SAAS,GAAG,IAAI;AAAA,MAC/B;AAAA,MACD,UAAU;AAAA,MACV,UAAU;AAAA,IACd;AAAA,EACA;AAEA,cAAA,WAA0B,WAAW;AACnC,WAAO;AAAA,MACL,WAAW;AAAA,MACX,UAAU,WAAW;AAAE,eAAO,OAAO,KAAK,EAAE;AAAA,MAAE;AAAA,IAClD;AAAA,EACA;;;;;;;;;;;ACjCA,MAAI,MAAMP,gBAAoB;AAC9B,MAAI,KAAKC;AACT,MAAI,aAAaW,kBAAuB,EAAC;AACzC,MAAI,MAAMU,WAAgB;AAC1B,MAAIC,eAAcT,mBAAwB;AAE1C,WAAS,SAAS,KAAK,QAAQ;AAC7B,QAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,QAAI,IAAI,KAAK;AACb,WAAO,KAAK;AACV,UAAI,IAAI,KAAK,CAAC;AACd,aAAO,CAAC,IAAI,IAAI,CAAC;AAAA,IACrB;AACE,WAAO;AAAA,EACT;AAGA,WAAS,MAAM,KAAK;AAClB,WAAO,SAAS,KAAK,EAAE;AAAA,EACzB;AAEA,MAAI,oBAAoB;AAAA,IACtB,WAAW;AAAA,IACX,WAAW,WAA2B;AAAA,IACtC,YAAY,IAAI,cAAc,QAAQ,OAAO;AAAA,IAC7C,eAAe;AAAA,IACf,gBAAgB;AAAA,MACd,sBAAsB;AAAA,MACtB,8BAA8B;AAAA,MAC9B,cAAc;AAAA,MACd,0BAA0B;AAAA,MAC1B,sBAAsB;AAAA,MACtB,gCAAgC;AAAA,IACpC;AAAA,EACC;AAGD,WAAS,WAAW,OAAO,OAAOS,cAAa,uBAAuB;AACpE,QAAI,CAAC;AACH,cAAQ;AAAA;AAER,cAAQ,GAAG,SAAS,KAAK;AAE3B,QAAI,QAAQ,SAAS,CAAE;AAEvB,aAAS,aAAa,KAAK,KAAK;AAC9B,aAAQ,QAAQ,SAAa,MAAM,SAAS,GAAG;AAAA,IACnD;AAEE,QAAI,mBAAmB,OAAO,OAAO,iBAAiB;AAEtD,WAAO;AAAA;AAAA,MAEL,oBAAoB,SAAS,uBAAuB,gBAAgB;AAAA,MACpE,aAAaA,aAAY;AAAA,MACzB,YAAYA,aAAY,SAAU;AAAA,MAClC,UAAU,MAAM,UAAU;AAAA;AAAA,MAG1B,cAAc,aAAa,MAAM,YAAY,CAAC;AAAA,MAC9C,YAAY,aAAa,MAAM,UAAU,MAAM;AAAA,MAC/C,aAAa,aAAa,MAAM,WAAW,CAAC;AAAA;AAAA,MAG5C,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,UAAU;AAAA,IACX;AAAA,EACH;AAGA,WAAS,mBAAmB,OAAO;AACjC,QAAI,OAAO,SAAS,SAAS;AAC7B,QAAI,MAAM,YAAY,MAAM,MAAM,YAAY,IAAI;AAChD,aAAQ,MAAM,WAAY,SAAS,MAAM,QAAQ,IAAI;AACrD,eAAU,MAAM,WAAY,SAAS,MAAM,QAAQ,IAAI;AAAA,IAC3D;AACE,WAAOA,aAAY,MAAM,MAAM,MAAM;AAAA,EACvC;AAEA,WAASC,UAAQtB,MAAK,eAAe,cAAc;AAKjD,QAAI,WAAW,MAAM,iBAAiB,EAAE;AACxC,IAAAA,OAAMA,QAAO;AAEb,QAAI,UAAU,CAAC,CAAC,SAAS;AACzB,QAAI,UAAU,SAAS;AACvB,QAAI,YAAY,CAAC,CAAC,SAAS;AAE3B,QAAI,iBAAiB,SAAS,kBAAkB;AAEhD,QAAI,wBAAwB,SAAS,oBAAoB,CAAE;AAE3D,QAAI,UAAU;AACd,QAAI,OAAOA,SAAQ,UAAU;AAC3B,kBAAYA,KAAI,YAAY,UAAU;AACtC,eAAS,OAAOA,KAAI;AACpB,eAAS,aAAa,SAAS,cAAcA,KAAI;AACjD,eAAS,OAAOA,KAAI,SAAU,aAAa,UAAW,OAAO;AAE7D,UAAI,MAAM;AAGV,UAAIA,KAAI,YAAY,UAAaA,KAAI,YAAY,QAAW;AAC1D,eAAO;AAAS,eAAO;AAAA,MAC7B,OAAW;AACL,eAAOA,KAAI,YAAY;AACvB,eAAOA,KAAI,YAAY;AAAA,MAC7B;AAEI,UAAI,SAAS;AAAA,QACX,QAAQA,KAAI;AAAA,QACZ,YAAYA,KAAI;AAAA,QAChB,UAAUA,KAAI;AAAA,QACd,WAAWA,KAAI;AAAA,MAChB;AAED,eAAS,WAAWA,KAAI,OAAO,QAAQ,SAAS,eAAeqB,aAAY,MAAM,MAAM,IAAI,GAAG,qBAAqB;AAAA,IACvH,OAAS;AACL,UAAI,QAAQ,IAAIrB,MAAK,IAAI;AACzB,iBAAW,MAAM;AACjB,eAAS,OAAO,MAAM;AACtB,eAAS,aAAa,SAAS,cAAc,MAAM;AACnD,eAAS,OAAO,SAAS,MAAM,IAAI,MAAO,aAAa,UAAW,OAAO;AACzE,UAAI,QAAQ,MAAM,WAAW,MAAM,SAAS,OAAO,CAAC,IAAI;AACxD,eAAS,WAAW,OAAO,MAAM,OAAO,SAAS,eAAe,mBAAmB,KAAK,GAAG,qBAAqB;AAAA,IACpH;AAEE,QAAI,SAAS;AACb,QAAI;AAEJ,aAAS,YAAY;AACnB,eAAS;AACT,WAAK,WAAW,OAAO;AACvB,UAAI,UAAW,MAAK,aAAa,WAAW,cAAc;AAE1D,UAAI,IAAI,IAAI,WAAW,IAAI;AAC3B,QAAE,KAAK,QAAQ,SAAS,KAAK,IAAI;AAG/B,YAAI,QAAS,MAAK,WAAW,CAAC;AAC9B,YAAI,QAAQ,MAAM;AAChB,uBAAa,MAAM,CAAC;AAAA,QAC5B,OAAa;AAEL,eAAK,IAAK;AACV,eAAK,QAAS;AACd,uBAAa,GAAG;AAAA,QACxB;AAAA,MACA,CAAK;AAAA,IACL;AAEE,QAAI,aAAa,SAAS;AACxB,aAAO,WAAe,QAAQ,UAAU,SAAS;AAAA,IACrD,WACW,aAAa,UAAU;AAC9B,aAAO,WAAe,QAAQ,UAAU,SAAS;AAAA,IACrD,OACO;AACH,YAAM,IAAI,MAAM,mDAAmD,QAAQ;AAAA,IAC/E;AAEE,QAAI,SAAS;AACX,WAAK,WAAW,SAAS,WAAW;AAClC,aAAK,IAAK;AACV,aAAK,QAAS;AACd,qBAAa,IAAI,MAAM,mBAAmB,CAAC;AAAA,MACjD,CAAK;AAAA,IACL;AAEE,SAAK,KAAK,SAAS,SAAS,KAAK;AAC/B,UAAI,CAAC,OAAQ,cAAa,GAAG;AAAA,IACjC,CAAG;AAAA,EAEH;AAEAuB,UAAA,UAAyBD;AACzBC,UAAA,qBAAoC;;;;;;;;;ACpLpC,MAAIjB,QAAOR,YAAiB;AAC5B,MAAI,WAAWC,cAAmB,EAAC;AACnC,MAAI,UAAUA,cAAmB,EAAC;AAClC,MAAI,aAAaA,cAAmB,EAAC;AACrC,MAAI,SAASW;AACb,MAAI,eAAeU;AACnB,MAAI,MAAMR,WAAgB;AAC1B,MAAI,wBAAwBC,aAAkB,EAAC;AAC/C,MAAI,eAAeA,aAAkB,EAAC;AAAA,EAEtC,MAAM,gBAAgB,aAAa;AAAA,IACjC,YAAaM,aAAY;AACvB,YAAO;AAEP,WAAK,aAAaA;AAElB,WAAK,QAAQ;AAEb,WAAK,UAAU,CAAE;AAEjB,WAAK,MAAM;AACX,WAAK,cAAc;AACnB,WAAK,GAAG,OAAO,KAAK,cAAc,KAAK,MAAM,SAAU,IAAI;AACzD,YAAI;AACF,aAAG,IAAI;AAAA,MACf,CAAK,CAAC;AACF,WAAK,GAAG,QAAQ,KAAK,cAAc,KAAK,MAAM,SAAU,IAAI;AAC1D,YAAI;AACF,aAAG,IAAI,MAAM,gBAAgB,CAAC;AAAA,MACtC,CAAK,CAAC;AACF,WAAK,GAAG,SAAS,WAAY;AAC3B,YAAI;AACJ,eAAO,KAAK,KAAK,YAAY,MAAK,GAAI;AACpC,cAAI;AACF,eAAG,IAAI,MAAM,gBAAgB,CAAC;AAAA,QACxC;AAAA,MACA,CAAK;AAED,WAAK,gBAAgB;AAAA,IACzB;AAAA,IAEE,WAAW,SAAS;AAClB,WAAK,UAAU;AAAA,IACnB;AAAA,IAEE,WAAY;AACV,WAAK,KAAK,KAAK,WAAW,aAAa,MAAM,KAAK,OAAO;AACzD,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaE,gBAAiB,QAAQ,QAAQ;AAC/B,aAAO,KAAK,WAAW,WAAW,KAAK,IAAI,QAAQ,MAAM;AAAA,IAC7D;AAAA;AAAA;AAAA;AAAA;AAAA,IAME,cAAe,QAAQ,QAAQ,OAAO;AACpC,UAAI,CAAC,KAAK,OAAO;AACf,eAAO,KAAK,QAAQ,WAAW,CAAC;AAChC,aAAK,QAAQ;AACb,aAAK,gBAAgB,QAAQ,MAAM;AAAA,MACzC,OACS;AACH,aAAK,QAAQ,KAAK;AAAA,UAChB;AAAA,UACA;AAAA,UACA;AAAA,QACR,CAAO;AAAA,MACP;AAAA,IACA;AAAA,IAEE,YAAa,QAAQ,YAAY,SAAS;AACxC,aAAO,KAAK,WAAW;AAAA,QACrB,KAAK;AAAA,QACLb,MAAK;AAAA,QAAc;AAAA,QACnBA,MAAK;AAAA,QAAiB;AAAA,QACtB;AAAA,MAAO;AAAA,IACb;AAAA;AAAA;AAAA,IAIE,KAAM,QAAQ,QAAQ,QAAQ,IAAI;AAChC,UAAIE,QAAO;AAEX,eAAS,MAAO,KAAK,GAAG;AACtB,YAAI,QAAQ,MAAM;AAChB,cAAI,EAAE,OAAO,QAAQ;AACnB,mBAAO,GAAG,MAAM,CAAC;AAAA,UAC3B,OACa;AAGH,gBAAI,eAAe,WAAW,MAAM;AAEpC,gBAAI,IAAI,IAAI,MAAM;AAAA,cAAI;AAAA,cACpB;AAAA,cAAc,QAAQ,GAAG,KAAK;AAAA,YAAC,CAAC;AAClC,YAAAA,MAAK;AAAA,cAAe,EAAE;AAAA,cAAI;AAAA,gBAAI;AAAA,gBAC5B;AAAA,gBAAc,WAAW,EAAE,EAAE;AAAA,cAAC;AAAA,cAC9BF,MAAK,UAAU;AAAA,cAAkB;AAAA,YAAC;AACpC,mBAAO,GAAG,CAAC;AAAA,UACrB;AAAA,QACA,WAKe,eAAe;AACtB,iBAAO,GAAG,GAAG;AAAA,aAKV;AAEH,cAAI,eAAe,IAAI,OAAO,WAAW,MAAM,IAAI,OAAO;AAC1D,cAAI,IAAK,WAAW,cAChB;AAAA,YAAI;AAAA,YACJ,WAAW,MAAM;AAAA,YAAG,SAAS,GAAG;AAAA,UAAC,IACjC,IAAI,gCAAgC,SAAS,GAAG,CAAC;AACrD,cAAI,kBAAkB,IAAI,MAAM,CAAC;AACjC,0BAAgB,OAAO,IAAI,OAAO;AAClC,0BAAgB,UAAU,IAAI,OAAO;AACrC,0BAAgB,WAAW,IAAI,OAAO;AACtC,iBAAO,GAAG,eAAe;AAAA,QACjC;AAAA,MACA;AAEI,WAAK,cAAc,QAAQ,QAAQ,KAAK;AAAA,IAC5C;AAAA;AAAA,IAGE,SAAU,eAAe;AACvB,WAAK,eAAgB;AACrB,qBAAe,MAAM,kBAAkB,aAAa;AACpD,WAAK,SAAS,UAAU,kBAAkB,aAAa;AACvD,WAAK,WAAW,eAAe,KAAK,EAAE;AACtC,WAAK,KAAK,OAAO;AAAA,IACrB;AAAA;AAAA;AAAA;AAAA;AAAA,IAME,UAAW,eAAe,GAAG;AAC3B,UAAI,OAAO,KAAK,gBAAgB,KAAK,IAAI;AACzC,qBAAe,MAAM,mBAAmB,aAAa;AAErD,WAAK,SAAS,SAAU,GAAG;AACzB,YAAI,EAAE,OAAOA,MAAK,gBAAgB;AAChC,cAAI;AACF,cAAG;AACL,cAAI,IAAI,aAAa,+BAA+B;AACpD,eAAK,SAAS,CAAC;AAAA,QACvB,WACe,EAAE,OAAOA,MAAK,cAAc;AACnC,eAAKA,MAAK,gBAAgB,EAAE;AAAA,QACpC;AAAA,MAEK;AAAA,IACL;AAAA,IAEE,iBAAkB;AAChB,eAAS,IAAK,GAAG;AACf,UAAE,IAAI,MAAM,6CAA6C,CAAC;AAAA,MAChE;AACI,UAAI,KAAK,UAAU;AACjB,YAAI,KAAK,KAAK;AAChB,WAAK,QAAQ;AAEb,UAAI;AACJ,aAAO,UAAU,KAAK,QAAQ,MAAO;AACnC,YAAI,QAAQ,KAAK;AACnB,WAAK,UAAU;AAAA,IACnB;AAAA,IAEE,aAAc,QAAQ,MAAM,GAAG;AAC7B,WAAK,gBAAgBA,MAAK,cAAc;AAAA,QACtC,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,QAAG,SAAS;AAAA,MAC5B,CAAK;AACD,UAAI,IAAI,aAAa,0BAA0B,MAAM;AACrD,WAAK,UAAU,GAAG,CAAC;AAAA,IACvB;AAAA;AAAA;AAAA;AAAA,IAKE,eAAgB,IAAI,QAAQ,MAAMY,QAAO;AACvC,UAAIV,QAAO;AACX,WAAK,aAAa,QAAQ,MAAM,WAAY;AAC1C,QAAAU,OAAM,OAAO;AAEb,YAAI,IAAI;AACN,UAAAA,OAAM,UAAUZ,MAAK,KAAK,EAAE,EAAE;AAC9B,UAAAY,OAAM,WAAWZ,MAAK,KAAK,EAAE,EAAE;AAAA,QACvC;AACM,QAAAE,MAAK,KAAK,SAASU,MAAK;AAAA,MAC9B,CAAK;AAAA,IACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASE,mBAAoB,GAAG;AACrB,UAAI;AACF,aAAK,gBAAgB,KAAK,cAAc,CAAC;AAAA,MAC/C,SACW,KAAK;AACV,YAAI,OAAO,QAAQ,UAAU;AAC3B,eAAK;AAAA,YAAe,EAAE;AAAA,YAAI;AAAA,YAAKZ,MAAK,UAAU;AAAA,YAC5C,IAAI,MAAM,GAAG;AAAA,UAAC;AAAA,QACxB,WACe,eAAe,OAAO;AAC7B,eAAK;AAAA,YAAe,EAAE;AAAA,YAAI;AAAA,YACxBA,MAAK,UAAU;AAAA,YAAgB;AAAA,UAAG;AAAA,QAC5C,OACW;AACH,eAAK;AAAA,YAAe,EAAE;AAAA,YAAI;AAAA,YACxBA,MAAK,UAAU;AAAA,YACf,IAAI,MAAM,IAAI,SAAQ,CAAE;AAAA,UAAC;AAAA,QACnC;AAAA,MACA;AAAA,IACA;AAAA,IAEE,cAAe,QAAQ,GAAG;AACxB,UAAI,MAAM,EAAE;AACZ,UAAI,QAAQ,EAAE;AAEd,UAAI,OAAO;AACT,YAAI,YAAY,KAAK,YAAY,OAAO,GAAG,MAAM,KAAK,MAAM,CAAC;AAC7D,aAAK,MAAM,MAAM;AACjB,kBAAU,QAAQ,MAAM;AAAA,MAC9B,OACS;AACH,YAAI;AACJ,YAAI,QAAQ,KAAK,KAAK;AACpB,cAAI,KAAK,YAAY,MAAO;AAC5B,eAAK;AAGL,iBAAO,KAAK,YAAY,CAAC,MAAM,MAAM;AACnC,iBAAK,YAAY,MAAO;AACxB,iBAAK;AAAA,UACf;AAAA,QACA,OACW;AACH,cAAI,KAAK,YAAY,MAAM,KAAK,GAAG;AACnC,eAAK,YAAY,MAAM,KAAK,GAAG,IAAI;AAAA,QAC3C;AAGM,eAAO,CAAC;AAAA,MACd;AAAA,IACA;AAAA,IAEE,oBAAqB,IAAI;AAIvB,WAAK,YAAY,KAAK,MAAM,KAAK;AAAA,IACrC;AAAA,IAEE,gBAAiB;AACf,WAAK,KAAK,OAAO;AAAA,IACrB;AAAA,IAEE,OAAO,GAAG;AAER,cAAQ,EAAE,IAAE;AAAA;AAAA,QAGZ,KAAK;AAAA;AAAA,QACL,KAAKA,MAAK;AAAA,QACV,KAAKA,MAAK;AAAA,QACV,KAAKA,MAAK;AACR,iBAAO,KAAK,mBAAmB,CAAC;AAAA;AAAA,QAGlC,KAAKA,MAAK;AACR,iBAAO,KAAK,KAAK,OAAO,EAAE,MAAM;AAAA,QAClC,KAAKA,MAAK;AACR,iBAAO,KAAK,KAAK,QAAQ,EAAE,MAAM;AAAA,QACnC,KAAKA,MAAK;AAER,iBAAO,KAAK,KAAK,UAAU,EAAE,MAAM;AAAA,QAErC,KAAKA,MAAK;AAIR,cAAI,KAAK,OAAO;AACd,gBAAI,QAAQ,KAAK;AAAO,iBAAK,QAAQ;AACrC,kBAAM,CAAC;AAAA,UACf;AACM,cAAI,OAAO,+BAA+B,SAAS,CAAC;AACpD,eAAK,gBAAgBA,MAAK,gBAAgB,CAAA,CAAE;AAE5C,cAAIY,SAAQ,IAAI,MAAM,IAAI;AAC1B,UAAAA,OAAM,OAAO,EAAE,OAAO;AACtB,UAAAA,OAAM,UAAU,EAAE,OAAO;AACzB,UAAAA,OAAM,WAAW,EAAE,OAAO;AAC1B,eAAK,KAAK,SAASA,MAAK;AAExB,cAAI,IAAI,aAAa,IAAI;AACzB,eAAK,SAAS,CAAC;AACf;AAAA,QAEF,KAAKZ,MAAK;AAER,iBAAO,KAAK;AAAA,YAAe,EAAE;AAAA,YAAI;AAAA,YACNA,MAAK,UAAU;AAAA,YACf,IAAI,MAAM,sBAAsB;AAAA,UAAC;AAAA,QAE9D;AAGE,cAAI,QAAQ,KAAK;AAAO,eAAK,QAAQ;AAKrC,cAAI,KAAK,QAAQ,SAAS,GAAG;AAC3B,gBAAI,OAAO,KAAK,QAAQ,MAAO;AAC/B,iBAAK,QAAQ,KAAK;AAClB,iBAAK,gBAAgB,KAAK,QAAQ,KAAK,MAAM;AAAA,UACrD;AACM,iBAAO,MAAM,MAAM,CAAC;AAAA,MAC1B;AAAA,IACA;AAAA,EACA;AAeA,WAAS,UAAU,KAAK,OAAO;AAC7B,WAAO,WAAW;AAChB,YAAM,IAAI,sBAAsB,KAAK,KAAK;AAAA,IAC3C;AAAA,EACH;AAEA,WAAS,eAAe,IAAI,KAAK,OAAO;AACtC,OAAG,kBAAkB,GAAG,gBAAgB,GAAG,cACzC,UAAU,KAAK,KAAK;AAAA,EACxB;AAIA,WAAS,uBAAuB,GAAG;AACjC,QAAI;AACJ,QAAI,EAAE,OAAOA,MAAK,aAAc,SAAQ;AAAA,aAC/B,EAAE,OAAOA,MAAK,YAAa,SAAQ;AAAA,QACvC,OAAM;AAAA,MAAI;AAAA,MACA,QAAQ,CAAC;AAAA,IAAC;AAEzB,QAAIE,QAAO;AACX,QAAI,SAAS,EAAE;AACf,WAAO,cAAc,SAAS,SAAS;AACrC,cAAQ,SAAS;AACjB,MAAAA,MAAK,KAAK,OAAO,OAAO;AAAA,IAC5B,CAAG;AAAA,EACH;AAIA,WAAS,cAAc,cAAc;AACnC,QAAI,YAAY,GAAG,YAAY;AAC/B,QAAI,UAAU;AAEd,QAAI,UAAU;AAAA,MACZ,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,SAAS;AAAA,IACV;AAED,WAAO;AAGP,aAAS,QAAQ,GAAG;AAClB,UAAI,EAAE,OAAOF,MAAK,iBAAiB;AACjC,gBAAQ,aAAa,EAAE;AACvB,oBAAY,YAAY,EAAE;AAG1B,YAAI,cAAc,GAAG;AACnB,kBAAQ,UAAU,OAAO,MAAM,CAAC;AAChC,uBAAa,OAAO;AACpB,iBAAO;AAAA,QACf,OACW;AACH,iBAAO;AAAA,QACf;AAAA,MACA,OACS;AACH,cAAM;AAAA,MACZ;AAAA,IACA;AAIE,aAAS,QAAQ,GAAG;AAClB,UAAI,EAAE,SAAS;AACb,YAAI,OAAO,EAAE,QAAQ;AACrB,qBAAa;AACb,YAAI,cAAc,GAAG;AACnB,cAAI,YAAY,MAAM;AACpB,oBAAQ,KAAK,EAAE,OAAO;AACtB,oBAAQ,UAAU,OAAO,OAAO,OAAO;AAAA,UACjD,OACa;AACH,oBAAQ,UAAU,EAAE;AAAA,UAC9B;AACQ,uBAAa,OAAO;AACpB,iBAAO;AAAA,QACf,WACe,YAAY,GAAG;AACtB,gBAAM;AAAA,YAAI;AAAA,YACA;AAAA,UAAS;AAAA,QAC3B,OACW;AACH,cAAI,YAAY;AACd,oBAAQ,KAAK,EAAE,OAAO;AAAA;AAEtB,sBAAU,CAAC,EAAE,OAAO;AACtB,iBAAO;AAAA,QACf;AAAA,MACA,MACS,OAAM;AAAA,IACf;AAAA,EACA;AAAA,EAIA,MAAM,oBAAoB,QAAQ;AAAA,IAChC,YAAaa,aAAY;AACvB,YAAMA,WAAU;AAChB,WAAK,YAAY,oBAAI,IAAK;AAAA,IAC9B;AAAA;AAAA;AAAA,IAIE,iBAAkB,KAAK,UAAU;AAC/B,WAAK,UAAU,IAAI,KAAK,QAAQ;AAAA,IACpC;AAAA,IAEE,mBAAoB,KAAK;AACvB,WAAK,UAAU,OAAO,GAAG;AAAA,IAC7B;AAAA,IAEE,gBAAiB,QAAQ,SAAS;AAChC,UAAI,cAAc,OAAO;AACzB,UAAI,WAAW,KAAK,UAAU,IAAI,WAAW;AAC7C,UAAI,UAAU;AACZ,eAAO,SAAS,OAAO;AAAA,MAC7B,OACS;AAEH,cAAM,IAAI,MAAM,uBAAuB,WAAW;AAAA,MACxD;AAAA,IACA;AAAA,IAEE,eAAgB,SAAS;AACvB,aAAO,KAAK,gBAAgB,QAAQ,QAAQ,OAAO;AAAA,IACvD;AAAA,IAEE,aAAc,QAAQ;AACpB,UAAI,SAAS,KAAK,gBAAgB,QAAQ,IAAI;AAC9C,WAAK,mBAAmB,OAAO,WAAW;AAC1C,aAAO;AAAA,IACX;AAAA,EACA;AAEA,UAAA,gBAA+B;AAC/B,UAAA,cAA6B;AAC7B,UAAA,UAAyB;;;;;;;;ACtdzB,WAAS,aAAa,KAAK,MAAM,OAAO;AACtC,QAAI,SAAS,OAAW,KAAI,IAAI,IAAI;AAAA,EACtC;AAEA,MAAI,gBAAgB,OAAO,OAAO,EAAE;AAEpC,MAAI,OAAO,CAAE;AAEb,OAAK,cAAc,SAAS,OAAO,SAAS;AAC1C,YAAQ,SAAS;AACjB,cAAU,WAAW;AAErB,QAAI,OAAO,OAAO,OAAO,QAAQ,aAAa,IAAI;AAClD,iBAAa,MAAM,aAAa,QAAQ,OAAO;AAC/C,iBAAa,MAAM,iBAAiB,QAAQ,UAAU;AACtD;AAAA,MAAa;AAAA,MAAM;AAAA,MACN,QAAQ;AAAA,IAAkB;AACvC;AAAA,MAAa;AAAA,MAAM;AAAA,MACN,QAAQ;AAAA,IAAoB;AACzC,iBAAa,MAAM,gBAAgB,QAAQ,SAAS;AACpD,iBAAa,MAAM,kBAAkB,QAAQ,WAAW;AACxD,iBAAa,MAAM,cAAc,QAAQ,QAAQ;AACjD,iBAAa,MAAM,gBAAgB,QAAQ,SAAS;AAEpD,WAAO;AAAA,MACL;AAAA,MACA,WAAW,CAAC,CAAC,QAAQ;AAAA,MACrB,SAAU,QAAQ,YAAY,SAAa,OAAO,QAAQ;AAAA,MAC1D,YAAY,CAAC,CAAC,QAAQ;AAAA,MACtB,WAAW;AAAA,MACX,SAAS;AAAA;AAAA,MAET,QAAQ;AAAA,MACR,QAAQ;AAAA,IACT;AAAA,EACF;AAED,OAAK,aAAa,SAAS,OAAO;AAChC,WAAO;AAAA,MACL;AAAA,MACA,SAAS;AAAA;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MAAM,YAAY;AAAA,MAAO,WAAW;AAAA;AAAA,MAC7C,QAAQ;AAAA,IACT;AAAA,EACF;AAED,OAAK,cAAc,SAAS,OAAO,SAAS;AAC1C,cAAU,WAAW;AACrB,WAAO;AAAA,MACL;AAAA,MACA,UAAU,CAAC,CAAC,QAAQ;AAAA,MACpB,SAAS,CAAC,CAAC,QAAQ;AAAA,MACnB,QAAQ;AAAA,MAAG,QAAQ;AAAA,IACpB;AAAA,EACF;AAED,OAAK,aAAa,SAAS,OAAO;AAChC,WAAO;AAAA,MACL;AAAA,MACA,QAAQ;AAAA,MAAG,QAAQ;AAAA,IACpB;AAAA,EACF;AAED,OAAK,YAAY,SAAS,OAAO,QAAQ,SAAS,MAAM;AACtD,WAAO;AAAA,MACL;AAAA,MACA,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MAAG,QAAQ;AAAA,IACpB;AAAA,EACF;AAED,OAAK,cAAc,SAAS,OAAO,QAAQ,SAAS,MAAM;AACxD,WAAO;AAAA,MACL;AAAA,MACA,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MAAG,QAAQ;AAAA,IACpB;AAAA,EACF;AAED,OAAK,iBAAiB,SAAS,UAAU,MAAM,SAAS;AACtD,cAAU,WAAW;AACrB,QAAI,OAAO,OAAO,OAAO,QAAQ,aAAa,IAAI;AAClD,iBAAa,MAAM,sBAAsB,QAAQ,iBAAiB;AAClE,WAAO;AAAA,MACL;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA,SAAS;AAAA,MACT,SAAU,QAAQ,YAAY,SAAa,OAAO,QAAQ;AAAA,MAC1D,YAAY,CAAC,CAAC,QAAQ;AAAA,MACtB,UAAU,CAAC,CAAC,QAAQ;AAAA,MACpB,QAAQ;AAAA,MACR,WAAW;AAAA,IACZ;AAAA,EACF;AAED,OAAK,gBAAgB,SAAS,UAAU;AACtC,WAAO;AAAA,MACL;AAAA,MACA,SAAS;AAAA;AAAA,MACT,QAAQ;AAAA;AAAA,MAER,SAAS;AAAA,MAAM,UAAU;AAAA,MAAQ,MAAM;AAAA,MAAK,YAAY;AAAA,MACxD,QAAQ;AAAA,IACT;AAAA,EACF;AAED,OAAK,iBAAiB,SAAS,UAAU,SAAS;AAChD,cAAU,WAAW;AACrB,WAAO;AAAA,MACL;AAAA,MACA,UAAU,CAAC,CAAC,QAAQ;AAAA,MACpB,QAAQ;AAAA,MAAG,QAAQ;AAAA,IACpB;AAAA,EACF;AAED,OAAK,eAAe,SAAS,MAAM,QAAQ,SAAS,MAAM;AACxD,WAAO;AAAA,MACL;AAAA,MACA,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MAAG,QAAQ;AAAA,IACpB;AAAA,EACF;AAED,OAAK,iBAAiB,SAAS,MAAM,QAAQ,SAAS,MAAM;AAC1D,WAAO;AAAA,MACL;AAAA,MACA,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MAAG,QAAQ;AAAA,IACpB;AAAA,EACF;AAOD,OAAK,UAAU,SAAS,UAAU,YAAY,SAAS;AACrD,cAAU,WAAW;AAMrB,aAAS,UAAU,IAAI;AACrB,UAAI,OAAO,QAAW;AACpB,eAAO;AAAA,MACb,WACa,MAAM,QAAQ,EAAE,GAAG;AAC1B,eAAO,GAAG,IAAI,MAAM;AAAA,MAC1B,MACS,QAAO,CAAC,OAAO,EAAE,CAAC;AAAA,IAC3B;AAEE,QAAI,UAAU,OAAO,OAAO,QAAQ,WAAW,IAAI;AACnD,iBAAa,SAAS,MAAM,UAAU,QAAQ,EAAE,CAAC;AACjD,iBAAa,SAAS,OAAO,UAAU,QAAQ,GAAG,CAAC;AAEnD,QAAI;AAMJ,QAAI,QAAQ,eAAe;AACzB,qBAAgB,QAAQ,aAAc,IAAI;AAAA,aACnC,OAAO,QAAQ,iBAAiB;AACvC,qBAAe,QAAQ;AAAA,aAChB,QAAQ;AACf,qBAAe;AAEjB,QAAI,aAAa,QAAQ;AACzB,QAAI,eAAe,OAAW,cAAa,WAAW,SAAU;AAEhE,WAAO;AAAA;AAAA,MAEL;AAAA,MACA;AAAA,MACA,WAAW,CAAC,CAAC,QAAQ;AAAA,MACrB,WAAW;AAAA;AAAA,MACX,QAAQ;AAAA;AAAA,MAER,aAAa,QAAQ;AAAA,MACrB,iBAAiB,QAAQ;AAAA,MACzB;AAAA,MACA;AAAA,MACA,UAAU,QAAQ;AAAA,MAClB,eAAe,QAAQ;AAAA,MACvB,SAAS,QAAQ;AAAA,MACjB;AAAA,MACA,WAAW,QAAQ;AAAA,MACnB,WAAW,QAAQ;AAAA,MACnB,MAAM,QAAQ;AAAA,MACd,QAAQ,QAAQ;AAAA,MAChB,OAAO,QAAQ;AAAA,MACf,WAAW;AAAA,IACZ;AAAA,EACF;AAED,OAAK,UAAU,SAAS,OAAO,SAAS;AACtC,cAAU,WAAW;AACrB,QAAI,OAAO,OAAO,OAAO,QAAQ,aAAa,IAAI;AAClD,iBAAa,MAAM,cAAc,QAAQ,QAAQ;AACjD,WAAO;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,MACA,aAAa,QAAQ,eAAe;AAAA,MACpC,SAAS,CAAC,CAAC,QAAQ;AAAA,MACnB,OAAO,CAAC,CAAC,QAAQ;AAAA,MACjB,WAAW,CAAC,CAAC,QAAQ;AAAA,MACrB,QAAQ;AAAA,MACR,WAAW;AAAA,IACZ;AAAA,EACF;AAED,OAAK,SAAS,SAAS,aAAa;AAClC,WAAO;AAAA,MACL;AAAA,MACA,QAAQ;AAAA,IACT;AAAA,EACF;AAED,OAAK,MAAM,SAAS,OAAO,SAAS;AAClC,cAAU,WAAW;AACrB,WAAO;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,MACA,OAAO,CAAC,CAAC,QAAQ;AAAA,IAClB;AAAA,EACF;AAED,OAAK,MAAM,SAAS,KAAK,SAAS;AAChC,WAAO;AAAA,MACL,aAAa;AAAA,MACb,UAAU,CAAC,CAAC;AAAA,IACb;AAAA,EACF;AAED,OAAK,OAAO,SAAS,KAAK,SAAS,SAAS;AAC1C,WAAO;AAAA,MACL,aAAa;AAAA,MACb,UAAU,CAAC,CAAC;AAAA,MACZ,SAAU,YAAY,SAAa,OAAO;AAAA,IAC3C;AAAA,EACF;AAED,OAAK,SAAS,SAAS,KAAK,SAAS;AACnC,WAAO;AAAA,MACL,aAAa;AAAA,MACb,SAAU,YAAY,SAAa,OAAO;AAAA,IAC3C;AAAA,EACF;AAED,OAAK,WAAW,SAAS,OAAOlB,SAAQ;AACtC,WAAO;AAAA,MACL,eAAe,SAAS;AAAA,MACxB,cAAc;AAAA,MACd,QAAQ,CAAC,CAACA;AAAA,IACX;AAAA,EACF;AAED,OAAK,UAAU,WAAW;AACxB,WAAO,EAAC,SAAS,KAAI;AAAA,EACtB;AAED,aAAiB,OAAO,OAAO,IAAI;;;;;;;ACnTnC,QAAM,eAAeH;AACrB,QAAM,YAAY,WAAgB;AAClC,QAAMQ,QAAOI,YAAiB;AAC9B,QAAM,EAAC,YAAW,IAAIU,eAAoB;AAC1C,QAAM,EAAC,cAAa,IAAIA,eAAoB;AAC5C,QAAM,OAAOR,gBAAqB;AAClC,QAAM,EAAC,QAAO,IAAIC,cAAmB;AAAA,EAErC,MAAM,qBAAqB,aAAa;AAAA,IACtC,YAAYM,aAAY;AACtB,YAAO;AACP,WAAK,aAAaA;AAElB,OAAC,SAAS,SAAS,WAAW,WAAW,EAAE,QAAQ,QAAM;AACvD,QAAAA,YAAW,GAAG,IAAI,KAAK,KAAK,KAAK,MAAM,EAAE,CAAC;AAAA,MAChD,CAAK;AAAA,IACL;AAAA,IAEE,QAAQ;AACN,aAAO,UAAU,KAAK,WAAW,MAAM,KAAK,KAAK,UAAU,CAAC,EAAG;AAAA,IACnE;AAAA,IAEE,aAAa,WAAW,QAAQ;AAC9B,aAAO,UAAU,KAAK,WAAW,cAAc,KAAK,KAAK,UAAU,CAAC,EAAE,WAAW,MAAM;AAAA,IAC3F;AAAA,IAEE,MAAM,cAAc,SAAS;AAC3B,YAAMf,WAAU,IAAI,QAAQ,KAAK,UAAU;AAC3C,MAAAA,SAAQ,WAAW,OAAO;AAC1B,YAAMA,SAAQ,KAAM;AACpB,aAAOA;AAAA,IACX;AAAA,IAEE,MAAM,qBAAqB,SAAS;AAClC,YAAMA,WAAU,IAAI,eAAe,KAAK,UAAU;AAClD,MAAAA,SAAQ,WAAW,OAAO;AAC1B,YAAMA,SAAQ,KAAM;AACpB,YAAMA,SAAQ,IAAIE,MAAK,eAAe,EAAC,QAAQ,MAAK,GAAGA,MAAK,eAAe;AAC3E,aAAOF;AAAA,IACX;AAAA,EACA;AAAA,EAIA,MAAM,gBAAgB,YAAY;AAAA,IAChC,YAAYe,aAAY;AACtB,YAAMA,WAAU;AAChB,WAAK,GAAG,YAAY,KAAK,eAAe,KAAK,IAAI,CAAC;AAClD,WAAK,GAAG,UAAU,KAAK,aAAa,KAAK,IAAI,CAAC;AAAA,IAClD;AAAA;AAAA;AAAA;AAAA,IAKE,MAAM,IAAI,QAAQ,QAAQ,QAAQ;AAChC,YAAM,IAAI,MAAM,UAAU,QAAM;AAC9B,eAAO,KAAK,KAAK,QAAQ,QAAQ,QAAQ,EAAE;AAAA,MACjD,CAAK,EAAG;AAEJ,aAAO,EAAE;AAAA,IACb;AAAA;AAAA,IAGE,MAAM,OAAO;AACX,YAAM,KAAK,MAAM,KAAK,SAAS,KAAK,IAAI,EAAG;AAC3C,aAAO,GAAG;AAAA,QAAIb,MAAK;AAAA,QAAa,EAAC,WAAW,GAAE;AAAA,QAChCA,MAAK;AAAA,MAAa;AAAA,IACpC;AAAA,IAEE,QAAQ;AACN,aAAO,UAAU,QAAM;AACrB,eAAO,KAAK;AAAA,UAAa;AAAA,UAAWA,MAAK,UAAU;AAAA,UACnC;AAAA,QAAE;AAAA,MACxB,CAAK,EAAG;AAAA,IACR;AAAA;AAAA,IAIE,YAAY,OAAO,SAAS;AAC1B,aAAO,KAAK;AAAA,QAAIA,MAAK;AAAA,QACL,KAAK,YAAY,OAAO,OAAO;AAAA,QAC/BA,MAAK;AAAA,MAAc;AAAA,IACvC;AAAA,IAEE,WAAW,OAAO;AAChB,aAAO,KAAK;AAAA,QAAIA,MAAK;AAAA,QACL,KAAK,WAAW,KAAK;AAAA,QACrBA,MAAK;AAAA,MAAc;AAAA,IACvC;AAAA,IAEE,YAAY,OAAO,SAAS;AAC1B,aAAO,KAAK;AAAA,QAAIA,MAAK;AAAA,QACL,KAAK,YAAY,OAAO,OAAO;AAAA,QAC/BA,MAAK;AAAA,MAAa;AAAA,IACtC;AAAA,IAEE,WAAW,OAAO;AAChB,aAAO,KAAK;AAAA,QAAIA,MAAK;AAAA,QACL,KAAK,WAAW,KAAK;AAAA,QACrBA,MAAK;AAAA,MAAY;AAAA,IACrC;AAAA,IAEE,UAAU,OAAO,QAAQ,SAAS,MAAM;AACtC,aAAO,KAAK;AAAA,QAAIA,MAAK;AAAA,QACL,KAAK,UAAU,OAAO,QAAQ,SAAS,IAAI;AAAA,QAC3CA,MAAK;AAAA,MAAW;AAAA,IACpC;AAAA,IAEE,YAAY,OAAO,QAAQ,SAAS,MAAM;AACxC,aAAO,KAAK;AAAA,QAAIA,MAAK;AAAA,QACL,KAAK,YAAY,OAAO,QAAQ,SAAS,IAAI;AAAA,QAC7CA,MAAK;AAAA,MAAa;AAAA,IACtC;AAAA,IAEE,eAAe,UAAU,MAAM,SAAS;AAGtC,aAAO,KAAK;AAAA,QAAIA,MAAK;AAAA,QACL,KAAK,eAAe,UAAU,MAAM,OAAO;AAAA,QAC3CA,MAAK;AAAA,MAAiB,EACnC,KAAK,SAAO;AAAE,eAAO,EAAE,SAAQ;AAAA,MAAG,CAAE;AAAA,IAC3C;AAAA,IAEE,cAAc,UAAU;AACtB,aAAO,KAAK;AAAA,QAAIA,MAAK;AAAA,QACL,KAAK,cAAc,QAAQ;AAAA,QAC3BA,MAAK;AAAA,MAAiB;AAAA,IAC1C;AAAA,IAEE,eAAe,MAAM,SAAS;AAC5B,aAAO,KAAK;AAAA,QAAIA,MAAK;AAAA,QACL,KAAK,eAAe,MAAM,OAAO;AAAA,QACjCA,MAAK;AAAA,MAAgB;AAAA,IACzC;AAAA,IAEE,aAAa,MAAM,QAAQ,SAAS,MAAM;AACxC,aAAO,KAAK;AAAA,QAAIA,MAAK;AAAA,QACL,KAAK,aAAa,MAAM,QAAQ,SAAS,IAAI;AAAA,QAC7CA,MAAK;AAAA,MAAc;AAAA,IACvC;AAAA,IAEE,eAAe,MAAM,QAAQ,SAAS,MAAM;AAC1C,aAAO,KAAK;AAAA,QAAIA,MAAK;AAAA,QACL,KAAK,eAAe,MAAM,QAAQ,SAAS,IAAI;AAAA,QAC/CA,MAAK;AAAA,MAAgB;AAAA,IACzC;AAAA;AAAA,IAIE,QAAQ,UAAU,YAAY,SAAS,SAAS;AAC9C,YAAM,iBAAiB,KAAK,QAAQ,UAAU,YAAY,OAAO;AACjE,aAAO,KAAK,YAAY,gBAAgB,gBAAgB,OAAO;AAAA,IACnE;AAAA,IAEE,YAAY,OAAO,SAAS,SAAS;AACnC,aAAO,KAAK,QAAQ,IAAI,OAAO,SAAS,OAAO;AAAA,IACnD;AAAA,IAEE,QAAQ,OAAO,UAAU,SAAS;AAGhC,YAAM,SAAS,KAAK,QAAQ,OAAO,OAAO;AAC1C,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,aAAK,KAAKA,MAAK,cAAc,QAAQA,MAAK,gBAAgB,CAAC,KAAK,OAAO;AACrE,cAAI,IAAK,QAAO,OAAO,GAAG;AAC1B,eAAK,iBAAiB,GAAG,OAAO,aAAa,QAAQ;AACrD,kBAAQ,GAAG,MAAM;AAAA,QACzB,CAAO;AAAA,MACP,CAAK;AAAA,IACL;AAAA,IAEE,MAAM,OAAO,aAAa;AACb,YAAM,UAAU,QAAM;AAC/B,aAAK;AAAA,UAAKA,MAAK;AAAA,UAAa,KAAK,OAAO,WAAW;AAAA,UAC7CA,MAAK;AAAA,UACL;AAAA,QAAE;AAAA,MACd,CAAK,EAAC,EACD,KAAK,QAAM;AACV,aAAK,mBAAmB,WAAW;AACnC,eAAO,GAAG;AAAA,MACX,CAAA;AAAA,IACL;AAAA,IAEE,IAAI,OAAO,SAAS;AAClB,YAAM,SAAS,KAAK,IAAI,OAAO,OAAO;AACtC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,aAAK,cAAcA,MAAK,UAAU,QAAQ,CAAC,KAAK,MAAM;AACpD,cAAI,IAAK,QAAO,OAAO,GAAG;AAC1B,cAAI,EAAE,OAAOA,MAAK,eAAe;AAC/B,mBAAO,QAAQ,KAAK;AAAA,UAC9B,WACiB,EAAE,OAAOA,MAAK,YAAY;AACjC,kBAAMkB,UAAS,EAAE;AACjB,iBAAK,gBAAgB,cAAc,OAAK;AACtC,gBAAE,SAASA;AACX,sBAAQ,CAAC;AAAA,YACrB,CAAW;AAAA,UACX,OACa;AACH,mBAAO,IAAI,MAAM,oCAAoC,QAAQ,CAAC,CAAC,EAAE,CAAC;AAAA,UAC5E;AAAA,QACA,CAAO;AAAA,MACP,CAAK;AAAA,IACL;AAAA,IAEE,IAAI,SAAS,SAAS;AACpB,WAAK;AAAA,QACHlB,MAAK;AAAA,QACL,KAAK,IAAI,QAAQ,OAAO,aAAa,OAAO;AAAA,MAAC;AAAA,IACnD;AAAA,IAEE,SAAS;AACP,WAAK,gBAAgBA,MAAK,UAAU,KAAK,IAAI,GAAG,IAAI,CAAC;AAAA,IACzD;AAAA,IAEE,KAAK,SAAS,SAAS,SAAS;AAC9B,WAAK;AAAA,QACHA,MAAK;AAAA,QACL,KAAK,KAAK,QAAQ,OAAO,aAAa,SAAS,OAAO;AAAA,MAAC;AAAA,IAC7D;AAAA,IAEE,QAAQ,SAAS;AACf,WAAK;AAAA,QAAgBA,MAAK;AAAA,QACL,KAAK,KAAK,GAAG,MAAM,OAAO;AAAA,MAAC;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA,IAME,OAAO,SAAS,SAAS;AACvB,WAAK;AAAA,QACHA,MAAK;AAAA,QACL,KAAK,OAAO,QAAQ,OAAO,aAAa,OAAO;AAAA,MAAC;AAAA,IACtD;AAAA,IAEE,UAAU;AACR,aAAO,KAAK;AAAA,QAAIA,MAAK;AAAA,QACL,KAAK,QAAS;AAAA,QACdA,MAAK;AAAA,MAAc;AAAA,IACvC;AAAA,IAEE,IAAI,OAAOL,SAAQ;AACjB,aAAO,KAAK;AAAA,QAAIK,MAAK;AAAA,QACL,KAAK,SAAS,OAAOL,OAAM;AAAA,QAC3BK,MAAK;AAAA,MAAU;AAAA,IACnC;AAAA,EACA;AAOA,UAAQ,UAAU,WAAW,QAAQ,UAAU;AAAA,EAS/C,MAAM,uBAAuB,QAAQ;AAAA,IACnC,QAAQ,UAAU,YAAY,SAAS,SAAS,IAAI;AAClD,WAAK,oBAAoB,EAAE;AAC3B,aAAO,MAAM,QAAQ,UAAU,YAAY,SAAS,OAAO;AAAA,IAC/D;AAAA,IAEE,YAAY,OAAO,SAAS,SAAS,IAAI;AACvC,aAAO,KAAK,QAAQ,IAAI,OAAO,SAAS,SAAS,EAAE;AAAA,IACvD;AAAA,IAEE,kBAAkB;AAChB,YAAM,WAAW,CAAE;AACnB,YAAM,cAAc,KAAK;AACzB,kBAAY,QAAQ,CAAC,KAAK,UAAU;AAClC,YAAI,QAAQ,MAAM;AAChB,gBAAM,YAAY,IAAI,QAAQ,CAAC,SAAS,WAAW;AACjD,wBAAY,KAAK,IAAI,SAAO;AAC1B,kBAAI,IAAK,KAAI,GAAG;AAChB,kBAAI,QAAQ,KAAM,SAAS;AAAA,kBACtB,QAAO,GAAG;AAAA,YAChB;AAAA,UACX,CAAS;AACD,mBAAS,KAAK,SAAS;AAAA,QAC/B;AAAA,MACA,CAAK;AAED,UAAI,CAAC,KAAK,SAAS;AACjB,YAAI;AACJ,eAAO,KAAK,KAAK,YAAY,MAAK,GAAI;AACpC,cAAI,GAAI,IAAG,IAAI,MAAM,gBAAgB,CAAC;AAAA,QAC9C;AAAA,MACA;AACI,aAAO,QAAQ,IAAI,QAAQ;AAAA,IAC/B;AAAA,EACA;AAEA,gBAAA,iBAAgC;AAChC,gBAAA,UAAyB;AACzB,gBAAA,eAA8B;;;;;;;ACnT9B,MAAI,cAAcR,eAAwB,EAAC;AAC3C,MAAI,eAAeC,qBAA8B,EAAC;AAClD,MAAI,YAAYW,WAAgB;AAEhC,WAASY,SAAQtB,MAAK,aAAa;AACjC,WAAO,UAAU,SAAS,IAAI;AAC5B,aAAO,YAAYA,MAAK,aAAa,EAAE;AAAA,IAC3C,CAAG,EAAC,EACD,KAAK,SAAS,MAAM;AACnB,aAAO,IAAI,aAAa,IAAI;AAAA,IAChC,CAAG;AAAA;AAGH,cAAA,UAAyBsB;AACzB,cAAA,cAA6BF,mBAA4B;AACrB,cAAA,wBAAGR,aAAsB,EAAC;;;;;ACL9D,MAAMa,eAAa,cAAc,YAAY,GAAG;AAChD,MAAMC,cAAY,KAAK,QAAQD,YAAU;AAoBlC,MAAM,uBAAuB;AAAA,EAO1B,cAAc;AALd,SAAA,mCAA+C,IAAI;AACnD,SAAA,0CAA+C,IAAI;AAC3D,SAAQ,cAAc;AAOf,SAAA,YAAY,KAAK,mBAAmB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMnC,qBAA6B;AAEnC,UAAM,gBAAgB;AAAA;AAAA,MAEpB,KAAK,KAAKC,aAAW,8BAA8B;AAAA;AAAA,MAEnD,KAAK,KAAKA,aAAW,iCAAiC;AAAA,MACtD,KAAK,KAAKA,aAAW,0BAA0B;AAAA;AAAA,MAE/C,KAAK,KAAKA,aAAW,uBAAuB;AAAA,IAC9C;AAEA,eAAW,cAAc,eAAe;AAClC,UAAA,GAAG,WAAW,UAAU,GAAG;AACrB,gBAAA,IAAI,oCAAoC,UAAU,EAAE;AACrD,eAAA;AAAA,MAAA;AAAA,IACT;AAII,UAAA,cAAc,cAAc,CAAC;AAC3B,YAAA,KAAK,oDAAoD,WAAW,EAAE;AACvE,WAAA;AAAA,EAAA;AAAA,EAGT,OAAc,cAAsC;AAC9C,QAAA,CAAC,uBAAuB,UAAU;AACb,6BAAA,WAAW,IAAI,uBAAuB;AAAA,IAAA;AAE/D,WAAO,uBAAuB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMhC,MAAa,aAA4B;AACvC,QAAI,KAAK,YAAa;AAElB,QAAA;AACF,YAAM,KAAK,oBAAoB;AAC/B,WAAK,oBAAoB;AACzB,WAAK,cAAc;AACnB,cAAQ,IAAI,oDAAoD;AAAA,aACzDR,QAAO;AACN,cAAA,MAAM,mDAAmDA,MAAK;AAChE,YAAAA;AAAA,IAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAMF,MAAc,sBAAqC;AAC7C,QAAA;AAEF,UAAI,CAAC,GAAG,WAAW,KAAK,SAAS,GAAG;AAClC,gBAAQ,KAAK,qCAAqC,KAAK,SAAS,EAAE;AAClE;AAAA,MAAA;AAIF,YAAM,QAAQ,GAAG,YAAY,KAAK,SAAS;AAC3C,YAAM,YAAY,MAAM,OAAO,UAAQ,KAAK,SAAS,OAAO,CAAC;AAE7D,iBAAW,YAAY,WAAW;AAC5B,YAAA;AACF,gBAAM,WAAW,KAAK,KAAK,KAAK,WAAW,QAAQ;AACnD,gBAAM,cAAc,GAAG,aAAa,UAAU,OAAO;AAC/C,gBAAA,SAAsB,KAAK,MAAM,WAAW;AAElD,gBAAM,WAAW,SAAS,QAAQ,SAAS,EAAE;AAGzC,cAAA,CAAC,OAAO,UAAU;AACb,mBAAA,WAAW,KAAK,cAAc,QAAQ;AAAA,UAAA;AAG/C,gBAAM,WAA0B;AAAA,YAC9B,IAAI;AAAA,YACJ;AAAA,YACA;AAAA,UACF;AAEK,eAAA,aAAa,IAAI,UAAU,QAAQ;AAChC,kBAAA,IAAI,wBAAwB,QAAQ,EAAE;AAAA,iBACvCA,QAAO;AACd,kBAAQ,KAAK,gCAAgC,QAAQ,IAAIA,MAAK;AAAA,QAAA;AAAA,MAChE;AAAA,aAEKA,QAAO;AACN,cAAA,MAAM,gCAAgCA,MAAK;AAC7C,YAAAA;AAAA,IAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAMM,cAAc,SAA0C;AAC9D,UAAM,aAAa,CAAC,yBAAyB,yBAAyB,6BAA6B,SAAS;AAC5G,UAAM,eAAe,CAAC,oBAAoB,SAAS,SAAS,YAAY;AAExE,QAAI,WAAW,SAAS,OAAO,EAAU,QAAA;AACzC,QAAI,aAAa,SAAS,OAAO,EAAU,QAAA;AACpC,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMD,sBAA4B;AAClC,SAAK,oBAAoB,MAAM;AAE/B,SAAK,aAAa,QAAQ,CAAC,UAAU,OAAO;AAC1C,WAAK,oBAAoB,IAAI,IAAI,SAAS,OAAO,WAAW;AAAA,IAAA,CAC7D;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMI,eAAgC;AACrC,WAAO,MAAM,KAAK,KAAK,aAAa,QAAQ;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMvC,aAAa,IAAuC;AAClD,WAAA,KAAK,aAAa,IAAI,EAAE;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,oBAAoB,SAAyB;AAElD,QAAI,CAAC,KAAK,eAAe,KAAK,oBAAoB,SAAS,GAAG;AACrD,aAAA,KAAK,sBAAsB,OAAO;AAAA,IAAA;AAE3C,WAAO,KAAK,oBAAoB,IAAI,OAAO,KAAK,KAAK,sBAAsB,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM5E,sBAAsB,SAAyB;AACrD,UAAM,aAAqC;AAAA,MACzC,yBAAyB;AAAA,MACzB,yBAAyB;AAAA,MACzB,WAAW;AAAA,MACX,6BAA6B;AAAA,MAC7B,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,cAAc;AAAA,IAChB;AACO,WAAA,WAAW,OAAO,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMzB,yBAAiD;AACtD,UAAM,MAA8B,CAAC;AACrC,SAAK,oBAAoB,QAAQ,CAAC,aAAa,OAAO;AACpD,UAAI,EAAE,IAAI;AAAA,IAAA,CACX;AACM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,UAAU,SAA0B;AACnC,UAAA,WAAW,KAAK,aAAa,OAAO;AAC1C,WAAO,WAAW,SAAS,OAAO,aAAa,WAAW;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMrD,oBAAoB,UAAmC;AAC5D,WAAO,MAAM,KAAK,KAAK,aAAa,OAAQ,CAAA,EAAE;AAAA,MAC5C,CAAA,aAAY,SAAS,OAAO,aAAa;AAAA,IAC3C;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,MAAa,SAAwB;AACnC,SAAK,aAAa,MAAM;AACxB,SAAK,oBAAoB,MAAM;AAC/B,SAAK,cAAc;AACnB,UAAM,KAAK,WAAW;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,gBAAyB;AAC9B,WAAO,KAAK;AAAA,EAAA;AAEhB;AAGa,MAAA,yBAAyB,uBAAuB,YAAY;AAIlE,MAAM,0BAA0B,CAAC,YAAoB,uBAAuB,oBAAoB,OAAO;AClPvG,MAAM,4BAA4B;AAAA,EAYvC,YAAY,YAA4B;AAXxC,SAAQ,aAAgC;AACxC,SAAQ,UAA0B;AAClC,SAAQ,cAAc;AACtB,SAAQ,oBAAoB;AAC5B,SAAQ,uBAAuB;AAC/B,SAAQ,iBAAiB;AACzB,SAAQ,aAAmC;AAG3C,SAAiB,8BAA8B;AAG7C,SAAK,aAAa,cAAc;AAEhC,SAAK,sBAAsB;AAAA,EAAA;AAAA;AAAA,EAI7B,MAAc,wBAAwB;AAChC,QAAA;AACF,YAAM,uBAAuB,WAAW;AACxC,cAAQ,IAAI,4CAA4C;AAAA,aACjDA,QAAO;AACN,cAAA,MAAM,wDAAwDA,MAAK;AAAA,IAAA;AAAA,EAC7E;AAAA;AAAA,EAIM,oBAAoB,WAA2B;AACjD,QAAA;AACF,aAAO,wBAAwB,SAAS;AAAA,aACjCA,QAAO;AACN,cAAA,KAAK,qDAAqDA,MAAK;AAEvE,YAAM,aAAqC;AAAA,QACzC,yBAAyB;AAAA,QACzB,yBAAyB;AAAA,QACzB,WAAW;AAAA,QACX,6BAA6B;AAAA,QAC7B,OAAO;AAAA,QACP,gBAAgB;AAAA,MAClB;AACO,aAAA,WAAW,SAAS,KAAK;AAAA,IAAA;AAAA,EAClC;AAAA;AAAA,EAIF,cAAcS,SAAuB;AACnC,SAAK,aAAaA;AAAA,EAAA;AAAA;AAAA,EAIpB,MAAM,QAAQ3B,OAAc,oBAAsC;AAC5D,QAAA;AACF,cAAQ,IAAI,2BAA2B;AACvC,WAAK,aAAa,MAAM,KAAK,QAAQA,IAAG;AACxC,WAAK,UAAU,MAAM,KAAK,WAAW,cAAc;AAGnD,WAAK,WAAW,GAAG,SAAS,KAAK,sBAAsB,KAAK,IAAI,CAAC;AACjE,WAAK,WAAW,GAAG,SAAS,KAAK,sBAAsB,KAAK,IAAI,CAAC;AAGjE,YAAM,KAAK,YAAY;AAGvB,YAAM,KAAK,0BAA0B;AAErC,WAAK,cAAc;AACnB,WAAK,oBAAoB;AAEzB,cAAQ,IAAI,iCAAiC;AACtC,aAAA;AAAA,aACAkB,QAAO;AACN,cAAA,MAAM,kCAAkCA,MAAK;AACrD,WAAK,cAAc;AAGf,UAAA,KAAK,oBAAoB,KAAK,sBAAsB;AACjD,aAAA;AACL,gBAAQ,IAAI,4BAA4B,KAAK,iBAAiB,IAAI,KAAK,oBAAoB,MAAM;AACjG,mBAAW,MAAM,KAAK,QAAQlB,IAAG,GAAG,KAAK,cAAc;AAAA,MAAA;AAGlD,aAAA;AAAA,IAAA;AAAA,EACT;AAAA;AAAA,EAIF,MAAc,cAAc;AAC1B,QAAI,CAAC,KAAK,QAAe,OAAA,IAAI,MAAM,uBAAuB;AAG1D,UAAM,KAAK,QAAQ,YAAY,KAAK,6BAA6B;AAAA,MAC/D,SAAS;AAAA,MACT,WAAW;AAAA,QACT,iBAAiB;AAAA;AAAA,MAAA;AAAA,IACnB,CACD;AAED,YAAQ,IAAI,6CAA6C;AAAA,EAAA;AAAA;AAAA,EAI3D,MAAc,4BAA4B;AACpC,QAAA,CAAC,KAAK,QAAS;AAGnB,UAAM,KAAK,QAAQ,QAAQ,KAAK,6BAA6B,CAAC,QAA+B;;AAC3F,UAAI,KAAK;AACH,YAAA;AACF,gBAAM,eAAoC,KAAK,MAAM,IAAI,QAAQ,UAAU;AAC3E,eAAK,wBAAwB,YAAY;AACpC,qBAAA,YAAA,mBAAS,IAAI;AAAA,iBACXkB,QAAO;AACN,kBAAA,MAAM,2CAA2CA,MAAK;AAC9D,qBAAK,YAAL,mBAAc,KAAK,KAAK,OAAO;AAAA,QAAK;AAAA,MACtC;AAAA,IACF,CACD;AAED,YAAQ,IAAI,wCAAwC;AAAA,EAAA;AAAA;AAAA,EAI9C,wBAAwB,cAAmC;AACzD,YAAA,IAAI,mCAAmC,YAAY;AAG3D,SAAK,uBAAuB,YAAY;AAGnC,SAAA,eAAe,yBAAyB,YAAY;AAAA,EAAA;AAAA;AAAA,EAInD,uBAAuB,cAAmC;;AAC5D,QAAA;AAEF,YAAM,YAAY,KAAK,oBAAoB,aAAa,SAAS;AAE7D,UAAA;AACA,UAAA;AAEA,UAAA,aAAa,WAAW,aAAa;AACvC,gBAAQ,GAAG,SAAS;AACpB,eAAO,aAAa,WAAW;AAE3B,aAAA,kBAAa,WAAb,mBAAqB,UAAU;AACzB,kBAAA;AAAA,QAAW,aAAa,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC;AAAA,QAAA;AAAA,MACnE,OACK;AACL,gBAAQ,GAAG,SAAS;AACb,eAAA,aAAa,SAAS,aAAa,WAAW;AAAA,MAAA;AAGjD,YAAA,qBAAqB,IAAI,aAAa;AAAA,QAC1C;AAAA,QACA;AAAA,QACA,MAAM,KAAK,oBAAoB,aAAa,MAAM;AAAA,QAClD,SAAS,aAAa,WAAW,WAAW,aAAa;AAAA,QACzD,aAAa;AAAA;AAAA,QACb,SAAS,aAAa,WAAW,cAAc;AAAA,UAC7C;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,UAAA;AAAA,QACR,IACE,CAAA;AAAA,MAAC,CACN;AAGD,UAAI,qBAAqB;AAGN,yBAAA,GAAG,SAAS,MAAM;AACnC,gBAAQ,IAAI,6BAA6B;AACpB,6BAAA;AAAA,MAAA,CACtB;AAGkB,yBAAA,GAAG,SAAS,MAAM;AAC3B,gBAAA,IAAI,wCAAwC,kBAAkB;AAGtE,mBAAW,MAAM;AACf,cAAI,CAAC,oBAAoB;AACvB,oBAAQ,IAAI,+BAA+B;AAC3C,iBAAK,wBAAwB,YAAY;AAAA,UAAA,OACpC;AACL,oBAAQ,IAAI,0CAA0C;AAAA,UAAA;AAAA,WAEvD,EAAE;AAAA,MAAA,CACN;AAGD,yBAAmB,GAAG,UAAU,CAAC,QAAQ,UAAU;AACjD,YAAI,UAAU,GAAG;AACf,kBAAQ,IAAI,uBAAuB;AACnC,eAAK,wBAAwB,YAAY;AAAA,QAAA;AAAA,MAC3C,CACD;AAGkB,yBAAA,GAAG,UAAU,CAACA,WAAU;AACjC,gBAAA,MAAM,wBAAwBA,MAAK;AAAA,MAAA,CAC5C;AAED,yBAAmB,KAAK;AAAA,aAEjBA,QAAO;AACN,cAAA,MAAM,sCAAsCA,MAAK;AAAA,IAAA;AAAA,EAC3D;AAAA;AAAA,EAIM,oBAAoB,SAAqC;AAGxD,WAAA;AAAA,EAAA;AAAA;AAAA,EAID,wBAAwB,cAAmC;;AACjE,QAAI,KAAK,cAAc,CAAC,KAAK,WAAW,eAAe;AAEjD,UAAA,KAAK,WAAW,eAAe;AACjC,aAAK,WAAW,QAAQ;AAAA,MAAA;AAE1B,WAAK,WAAW,MAAM;AAGjB,WAAA,WAAW,YAAY,KAAK,qBAAqB;AAAA,QACpD,QAAQ,aAAa;AAAA,QACrB,WAAW,aAAa;AAAA,QACxB,QAAQ,aAAa;AAAA,QACrB,YAAW,kBAAa,WAAb,mBAAqB;AAAA,MAAA,CACjC;AAAA,IAAA;AAAA,EACH;AAAA;AAAA,EAIM,eAAe,OAAe,MAAW;AAC/C,QAAI,KAAK,cAAc,CAAC,KAAK,WAAW,eAAe;AACrD,WAAK,WAAW,YAAY,KAAK,OAAO,IAAI;AAAA,IAAA;AAAA,EAC9C;AAAA;AAAA,EAIF,MAAM,mBAAmB;AACvB,UAAM,mBAAwC;AAAA,MAC5C,QAAQ,UAAU,KAAK,IAAI;AAAA,MAC3B,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,SAAS,EAAE,WAAW,MAAM,QAAQ,KAAK;AAAA,MAC3C;AAAA,MACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IACpC;AAEA,SAAK,wBAAwB,gBAAgB;AAAA,EAAA;AAAA;AAAA,EAIvC,sBAAsBA,QAAc;AAClC,YAAA,MAAM,8BAA8BA,MAAK;AACjD,SAAK,cAAc;AAAA,EAAA;AAAA;AAAA,EAIb,wBAAwB;AAC9B,YAAQ,IAAI,4BAA4B;AACxC,SAAK,cAAc;AAGf,QAAA,KAAK,oBAAoB,KAAK,sBAAsB;AACjD,WAAA;AACL,cAAQ,IAAI,4BAA4B,KAAK,iBAAiB,IAAI,KAAK,oBAAoB,MAAM;AACjG,iBAAW,MAAM,KAAK,QAAQ,GAAG,KAAK,cAAc;AAAA,IAAA;AAAA,EACtD;AAAA;AAAA,EAIF,MAAM,aAAa;AACb,QAAA;AACF,UAAI,KAAK,SAAS;AACV,cAAA,KAAK,QAAQ,MAAM;AACzB,aAAK,UAAU;AAAA,MAAA;AAGjB,UAAI,KAAK,YAAY;AACb,cAAA,KAAK,WAAW,MAAM;AAC5B,aAAK,aAAa;AAAA,MAAA;AAGpB,WAAK,cAAc;AACnB,cAAQ,IAAI,uBAAuB;AAAA,aAC5BA,QAAO;AACN,cAAA,MAAM,sCAAsCA,MAAK;AAAA,IAAA;AAAA,EAC3D;AAAA;AAAA,EAIF,qBAA8B;AAC5B,WAAO,KAAK,eAAe,KAAK,eAAe,QAAQ,KAAK,YAAY;AAAA,EAAA;AAAA;AAAA,EAI1E,oBAAoB;AACX,WAAA;AAAA,MACL,aAAa,KAAK;AAAA,MAClB,mBAAmB,KAAK;AAAA,MACxB,sBAAsB,KAAK;AAAA,IAC7B;AAAA,EAAA;AAEJ;ACpTA,MAAM,aAAaU,gBAAc,YAAY,GAAG;AAChD,MAAM,YAAY,QAAQ,UAAU;AACpC,QAAY,IAAA,gBAAgB,KAAK,WAAW,IAAI;AAChD,QAAA,IAAY,OAAO,KAAK,QAAY,IAAA,eAAe,SAAS;AAC5D,QAAY,IAAA,SAAS,YAAY,sBAC7B,KAAK,YAAY,eAAe,WAAW,IAC3C,QAAY,IAAA;AAEhB,MAAM,QAAQ,QAAY,IAAA,UAAU,MAAM;AAG1C,IAAI,QAAU,EAAA,WAAW,KAAK,OAAO,4BAA4B;AAGjE,IAAI,QAAQ,aAAa,aAAa,kBAAkB,IAAI,SAAS;AAErE,IAAI,CAAC,IAAI,6BAA6B;AACpC,MAAI,KAAK;AACT,UAAQ,KAAK,CAAC;AAChB;AAOA,IAAI,MAA4B;AAChC,IAAI,YAAkC;AAGtC,IAAI,kBAAsD;AAG1D,MAAM,UAAU,KAAK,WAAW,sBAAsB;AACtD,MAAM,MAAM,QAAY,IAAA;AACxB,MAAM,YAAY,KAAK,QAAY,IAAA,MAAM,YAAY;AAGrD,SAAS,WAAW,QAAQ,SAAS;AACnC,QAAM,OAAO,KAAK;AAAA,IAChB,QAAQ,KAAK;AAAA,EACf;AACA,OAAK,mBAAmB,IAAI;AAC9B;AAEA,eAAe,iBAAiB,cAAuB;AACrD,QAAM,IAAI,cAAc;AAAA,IACtB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,MAAM,KAAK,QAAY,IAAA,QAAQ,aAAa;AAAA,IAC5C,gBAAgB;AAAA,MACd;AAAA,MACA,iBAAiB;AAAA;AAAA,MACjB,kBAAkB;AAAA;AAAA,IAAA;AAAA,EACpB,CACD;AAED,QAAM,YAAY,eAAe,GAAG,GAAG,IAAI,YAAY,KAAK;AAC5D,QAAM,kBAAkB,eAAe,EAAE,UAAU,WAAW,MAAM,iBAAiB;AAEjF,MAAA,YAAY,qBAAqB;AAEnC,QAAI,QAAQ,SAAS;AAErB,QAAI,YAAY,aAAa,EAAE,MAAM,UAAU;AAAA,EAAA,OAC1C;AACL,QAAI,SAAS,OAAO,oBAAoB,WAAW,kBAAkB,gBAAgB,UAAU,OAAO,oBAAoB,WAAW,CAAK,IAAA,EAAE,MAAM,gBAAgB,MAAM;AAAA,EAAA;AAG/J,aAAA;AAGP,MAAA,YAAY,GAAG,mBAAmB,MAAM;AAC1C,+BAAK,YAAY,KAAK,6CAA4B,KAAK,GAAE;EAAgB,CAC1E;AAGD,MAAI,YAAY,qBAAqB,CAAC,EAAE,KAAA5B,WAAU;AAM1C,UAAA,cAAc,IAAI,cAAc;AAAA,MACpC,iBAAiB;AAAA;AAAA,MACjB,gBAAgB;AAAA,QACd;AAAA,QACA,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MAAA;AAAA,IACpB,CACD;AAED,gBAAY,QAAQA,IAAG;AAEhB,WAAA,EAAE,QAAQ,OAAO;AAAA,EAAA,CACzB;AAIG,MAAA,GAAG,qBAAqB,MAAM;AAChC,eAAW,OAAO;AAAA,EAAA,CACnB;AAGG,MAAA,GAAG,qBAAqB,MAAM;AACrB,eAAA;AAAA,EAAA,CACZ;AAID,QAAM,kBAAkB,QAAA,IAAY,0BAA0B,UACtC,YAAY,aAAa;AAEjD,UAAQ,IAAI,mBAAmB;AAAA,IAC7B,uBAAuB,QAAY,IAAA;AAAA,IACnC,UAAU,QAAY,IAAA;AAAA,IACtB;AAAA,EAAA,CACD;AAED,MAAI,iBAAiB;AACnB,YAAQ,IAAI,wBAAwB;AACjB,uBAAA;AAAA,EAAA,OACd;AACL,YAAQ,IAAI,eAAe;AAAA,EAAA;AAE/B;AAEA,eAAe,qBAAqB;AAClC,cAAY,IAAI,cAAc;AAAA,IAC5B,OAAO;AAAA;AAAA,IACP,QAAQ;AAAA;AAAA,IACR,OAAO;AAAA;AAAA,IACP,WAAW;AAAA,IACX,SAAS;AAAA;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA;AAAA,IACP,MAAM,KAAK,QAAY,IAAA,QAAQ,aAAa;AAAA;AAAA,IAC5C,gBAAgB;AAAA,MACd;AAAA,MACA,iBAAiB;AAAA;AAAA,MACjB,kBAAkB;AAAA;AAAA,IAAA;AAAA,EACpB,CACD;AAEK,QAAA,kBAAkB,GAAG,GAAG;AAC9B,QAAM,kBAAkB,EAAE,UAAU,WAAW,MAAM,SAAS;AAE1D,MAAA,YAAY,qBAAqB;AACnC,cAAU,QAAQ,eAAe;AAAA,EAAA,OAC5B;AACL,cAAU,SAAS,gBAAgB,UAAU,EAAE,MAAM,gBAAgB,MAAM;AAAA,EAAA;AAK/E;AAEA,IAAI,UAAA,EAAY,KAAK,kBAAkB;AAEvC,IAAI,GAAG,qBAAqB,MAAM;AAEhC,MAAI,cAAc,gBAAgB,WAAW,GAAG;AAC9C,QAAI,KAAK;AAAA,EAAA;AAEb,CAAC;AAED,IAAI,GAAG,mBAAmB,MAAM;AAE9B,MAAI,OAAO,CAAC,IAAI,eAAe;AAE7B,QAAI,IAAI,cAAe,KAAI,QAAQ;AACnC,QAAI,MAAM;AAAA,EACD,WAAA,aAAa,CAAC,UAAU,eAAe;AAChD,QAAI,UAAU,cAAe,WAAU,QAAQ;AAC/C,cAAU,MAAM;AAAA,EAAA;AAEpB,CAAC;AAED,IAAI,GAAG,YAAY,MAAM;AAGjB,QAAA,aAAa,cAAc,cAAc;AAC3C,MAAA,WAAW,WAAW,GAAG;AACR,uBAAA;AAAA,EAAA,OACd;AAEL,QAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,UAAI,IAAI,cAAe,KAAI,QAAQ;AACnC,UAAI,MAAM;AAAA,IACD,WAAA,aAAa,CAAC,UAAU,eAAe;AAChD,UAAI,UAAU,cAAe,WAAU,QAAQ;AAC/C,gBAAU,MAAM;AAAA,IAAA,OACX;AAEM,iBAAA,CAAC,EAAE,MAAM;AAAA,IAAA;AAAA,EACtB;AAEJ,CAAC;AAGD,QAAQ,GAAG,iCAAiC,CAAC,OAAO,OAKhD,CAAA,MAAO;AACT,MAAI,WAAW;AACb,cAAU,MAAM;AACJ,gBAAA;AAAA,EAAA;AAEd,mBAAiB,KAAK,WAAW;AAGjC,MAAI,KAAK,gBAAgB;AACvB,YAAQ,IAAI,8CAA8C;AAAA,MACxD,UAAU,KAAK;AAAA,MACf,aAAa,KAAK;AAAA,MAClB,gBAAgB,KAAK;AAAA,IAAA,CACtB;AAED,UAAM,eAAe,MAAM;;AACzB,cAAQ,IAAI,wCAAwC;AAEpD,WAAI,UAAK,gBAAL,mBAAkB,SAAS,4BAA4B;AAEzD,gBAAQ,IAAI,iDAAiD;AAC7D,mCAAK,YAAY,KAAK,uBAAuB,KAAK;AAAA,MAAc,OAC3D;AAEL,gBAAQ,IAAI,qDAAqD;AACjE,mCAAK,YAAY,KAAK,2BAA2B,KAAK;AAAA,MAAc;AAGtE,UAAI,KAAK,gBAAgB;AACvB,gBAAQ,IAAI,kDAAkD;AAC9D,mCAAK,YAAY,KAAK,wBAAwB,KAAK;AAAA,MAAc;AAAA,IAErE;AAEK,+BAAA,YAAY,KAAK,mBAAmB;AACpC,+BAAA,YAAY,KAAK,aAAa;AACnC,eAAW,cAAc,GAAI;AAAA,EAAA;AAEjC,CAAC;AAGD,MAAM,UAAU,CAAC,oBAA4B;AAC3C,QAAM,YAA0C;AAAA,IAC9C,EAAE,OAAO,MAAM,MAAM,QAAQ;AAAA,IAC7B,EAAE,OAAO,SAAS,MAAM,iBAAiB;AAAA,IACzC,EAAE,OAAO,QAAQ,MAAM,cAAc;AAAA;AAAA,EAEvC;AACA,MAAI,CAAC,OAAO;AACV,UAAM,gBAAgB,UAAU,UAAU,CAAQ,SAAA,KAAK,SAAS,gBAAgB;AAChF,QAAI,gBAAgB,GAAc,WAAA,OAAO,eAAe,CAAC;AACzD,UAAM,mBAAmB,UAAU,UAAU,CAAQ,SAAA,KAAK,SAAS,aAAa;AAChF,QAAI,mBAAmB,GAAc,WAAA,OAAO,kBAAkB,CAAC;AAAA,EAAA;AAGjE,QAAM,WAAyD;AAAA,IAC7D;AAAA,MACE,OAAO,IAAI;AAAA,MACX,SAAS;AAAA,IACX;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,SAAS;AAAA,QACP;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO,YAAY;AACjB,gBAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,kBAAI,MAAM;AACN,kBAAA,YAAY,KAAK,+BAA+B;AAAA,YAAA,OAC/C;AAEL,oBAAM,eAAgB,aAAa,CAAC,UAAU,YAAA,IAAiB,YAAY;AAC3E,oBAAM,gBAA4C,EAAE,YAAY,CAAC,eAAe,EAAE;AAC5E,oBAAA,sBAAsB,eACxB,MAAM,OAAO,eAAe,cAAc,aAAa,IACvD,MAAM,OAAO,eAAe,aAAa;AAE7C,kBAAI,CAAC,oBAAoB,YAAY,oBAAoB,UAAU,SAAS,GAAG;AACvE,sBAAA,cAAc,oBAAoB,UAAU,CAAC;AACnD,oBAAI,aAAa,CAAC,UAAU,eAAe;AAAE,4BAAU,MAAM;AAAe,8BAAA;AAAA,gBAAA;AAC5E,iCAAiB,cAAc,mBAAmB,WAAW,CAAC,EAAE;AAAA,cAAA;AAAA,YAClE;AAAA,UACF;AAAA,QAEJ;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO,YAAY;AACjB,gBAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,kBAAI,MAAM;AACN,kBAAA,YAAY,KAAK,0BAA0B;AAAA,YAAA,OAC1C;AACL,oBAAM,eAAgB,aAAa,CAAC,UAAU,YAAA,IAAiB,YAAY;AAC3E,oBAAM,gBAA4C,EAAE,YAAY,CAAC,UAAU,EAAE;AACvE,oBAAA,iBAAiB,eACnB,MAAM,OAAO,eAAe,cAAc,aAAa,IACvD,MAAM,OAAO,eAAe,aAAa;AAE7C,kBAAI,CAAC,eAAe,YAAY,eAAe,UAAU,SAAS,GAAG;AAC7D,sBAAA,WAAW,eAAe,UAAU,CAAC;AAC3C,oBAAI,aAAa,CAAC,UAAU,eAAe;AAAE,4BAAU,MAAM;AAAe,8BAAA;AAAA,gBAAA;AAGtE,sBAAA,cAAc,qBAAqB,KAAK,QAAQ;AAEtD,oBAAI,aAAa;AAEf,mCAAiB,yBAAyB;AAE1C,wBAAM,gBAAgB,MAAM;AACrB,+CAAA,YAAY,KAAK,uBAAuB;AACxC,+CAAA,YAAY,KAAK,wBAAwB;AAAA,kBAChD;AACK,6CAAA,YAAY,KAAK,mBAAmB;AACpC,6CAAA,YAAY,KAAK,aAAa;AACnC,6BAAW,eAAe,GAAI;AAAA,gBAAA,OACzB;AAEC,wBAAA,UAAU,SAAS,UAAU,GAAG,SAAS,YAAY,GAAG,KAAK,SAAS,YAAY,IAAI,CAAC;AAC7F,mCAAiB,cAAc,mBAAmB,OAAO,CAAC,EAAE;AAE5D,wBAAM,oBAAoB,MAAM;AACzB,+CAAA,YAAY,KAAK,2BAA2B;AAC5C,+CAAA,YAAY,KAAK,wBAAwB;AAAA,kBAChD;AACK,6CAAA,YAAY,KAAK,mBAAmB;AACpC,6CAAA,YAAY,KAAK,aAAa;AAEnC,6BAAW,mBAAmB,GAAI;AAAA,gBAAA;AAAA,cACpC;AAAA,YACF;AAAA,UACF;AAAA,QAEJ;AAAA,QACA,EAAE,MAAM,YAAY;AAAA,QACpB,EAAE,OAAO,MAAM,MAAM,OAAO;AAAA,MAAA;AAAA,IAEhC;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,SAAS;AAAA,QACP,EAAE,OAAO,MAAM,MAAM,OAAO;AAAA,QAC5B,EAAE,OAAO,MAAM,MAAM,OAAO;AAAA,QAC5B,EAAE,MAAM,YAAY;AAAA,QACpB,EAAE,OAAO,MAAM,MAAM,MAAM;AAAA,QAC3B,EAAE,OAAO,MAAM,MAAM,OAAO;AAAA,QAC5B,EAAE,OAAO,MAAM,MAAM,QAAQ;AAAA,QAC7B,EAAE,OAAO,MAAM,MAAM,SAAS;AAAA,QAC9B,EAAE,OAAO,MAAM,MAAM,YAAY;AAAA,MAAA;AAAA,IAErC;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,SAAS;AAAA,QACP,EAAE,OAAO,MAAM,MAAM,SAAS;AAAA,QAC9B,EAAE,OAAO,QAAQ,MAAM,YAAY;AAAA,QACnC,EAAE,OAAO,MAAM,MAAM,UAAU;AAAA,QAC/B,EAAE,MAAM,YAAY;AAAA,QACpB;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,QAAA;AAAA,MACR;AAAA,IACF;AAAA,EAEJ;AAGM,QAAA,iBAAiB,SAAS,CAAC,EAAE;AAC/B,MAAA,MAAM,QAAQ,cAAc,GAAG;AACjC,UAAM,gBAAgB,eAAe,UAAU,CAAQ,SAAA,KAAK,SAAS,MAAM;AACrE,UAAA,cAAc,SAAS,CAAC,EAAE;AAChC,QAAI,gBAAgB,MAAM,MAAM,QAAQ,WAAW,KAAK,YAAY,KAAK,CAAQ,SAAA,KAAK,SAAS,MAAM,GAAG;AACvF,qBAAA,OAAO,eAAe,CAAC;AAAA,IAAA;AAAA,EACxC;AAEK,SAAA;AACT;AAGA,QAAQ,OAAO,YAAY,CAAC,GAAG,QAAQ;AAC/B,QAAA,cAAc,IAAI,cAAc;AAAA,IACpC,gBAAgB;AAAA,MACd;AAAA,MACA,iBAAiB;AAAA;AAAA,MACjB,kBAAkB;AAAA,IAAA;AAAA,EACpB,CACD;AAEG,MAAA,YAAY,qBAAqB;AACnC,gBAAY,QAAQ,GAAG,GAAG,IAAI,GAAG,EAAE;AAAA,EAAA,OAC9B;AACL,gBAAY,SAAS,WAAW,EAAE,MAAM,KAAK;AAAA,EAAA;AAEjD,CAAC;AAqDD,MAAM6B,WAAU,cAAc,YAAY,GAAG;AAK7C,QAAQ,OAAO,wBAAwB,YAAY;AAC3C,QAAA,SAAS,MAAM,OAAO,eAAe;AAAA,IACzC,YAAY,CAAC,eAAe;AAAA,EAAA,CAC7B;AACM,SAAA,OAAO,UAAU,CAAC;AAC3B,CAAC;AAGD,QAAQ,OAAO,mBAAmB,YAAY;AACtC,QAAA,SAAS,MAAM,OAAO,eAAe;AAAA,IACzC,YAAY,CAAC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAAA,CAMxB;AACD,MAAI,OAAO,YAAY,OAAO,UAAU,WAAW,GAAG;AAC7C,WAAA;AAAA,EAAA;AAEF,SAAA,OAAO,UAAU,CAAC;AAC3B,CAAC;AAGD,QAAQ,OAAO,oBAAoB,OAAO,GAAG,YAAoB;AACzD,QAAA,QAAQ,MAAM,GAAG,SAAS,QAAQ,SAAS,EAAE,eAAe,MAAM;AACjE,SAAA,MAAM,IAAI,CAAW,YAAA;AAAA,IAC1B,MAAM,OAAO;AAAA,IACb,aAAa,OAAO,YAAY;AAAA,IAChC,MAAM,KAAK,KAAK,SAAS,OAAO,IAAI;AAAA,EAAA,EACpC;AACJ,CAAC;AAGD,QAAQ,OAAO,sBAAsB,OAAO,GAAG,eAAuB;AACpE,QAAM,GAAG,SAAS,MAAM,YAAY,EAAE,WAAW,MAAM;AAChD,SAAA,EAAE,SAAS,KAAK;AACzB,CAAC;AAGD,QAAQ,OAAO,iBAAiB,OAAO,GAAG,aAAqB;AAC7D,QAAM,GAAG,SAAS,UAAU,UAAU,EAAE;AACjC,SAAA,EAAE,SAAS,KAAK;AACzB,CAAC;AAGD,QAAQ,OAAO,iBAAiB,OAAO,GAAG,eAAuB;AAC/D,QAAM,QAAQ,MAAM,GAAG,SAAS,KAAK,UAAU;AAC3C,MAAA,MAAM,eAAe;AACvB,UAAM,GAAG,SAAS,MAAM,YAAY,EAAE,WAAW,MAAM;AAAA,EAAA,OAClD;AACC,UAAA,GAAG,SAAS,OAAO,UAAU;AAAA,EAAA;AAE9B,SAAA,EAAE,SAAS,KAAK;AACzB,CAAC;AAGD,MAAM,cAAc,CAAC,aAA6E;AAChG,QAAM,MAAM,KAAK,QAAQ,QAAQ,EAAE,YAAY;AAGzC,QAAA,iBAAiB,CAAC,QAAQ,OAAO,SAAS,QAAQ,SAAS,QAAQ,OAAO,OAAO,QAAQ,OAAO,SAAS,QAAQ,MAAM,MAAM,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,MAAM;AAG3L,QAAM,mBAAmB,CAAC,QAAQ,SAAS,QAAQ,SAAS,QAAQ,OAAO;AAGrE,QAAA,gBAAgB,CAAC,MAAM;AAGvB,QAAA,kBAAkB,CAAC,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAGhF,QAAM,oBAAoB,CAAC,QAAQ,QAAQ,OAAO,QAAQ,KAAK;AAG/D,QAAM,uBAAuB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAG5E,QAAM,kBAAkB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,OAAO;AAEpE,MAAA,eAAe,SAAS,GAAG,GAAG;AAChC,WAAO,EAAE,MAAM,KAAK,UAAU,QAAQ,WAAW,KAAK;AAAA,EAC7C,WAAA,iBAAiB,SAAS,GAAG,GAAG;AACzC,WAAO,EAAE,MAAM,KAAK,UAAU,UAAU,WAAW,KAAK;AAAA,EAC/C,WAAA,cAAc,SAAS,GAAG,GAAG;AACtC,WAAO,EAAE,MAAM,KAAK,UAAU,OAAO,WAAW,KAAK;AAAA,EAC5C,WAAA,gBAAgB,SAAS,GAAG,GAAG;AACxC,WAAO,EAAE,MAAM,KAAK,UAAU,SAAS,WAAW,KAAK;AAAA,EAC9C,WAAA,kBAAkB,SAAS,GAAG,GAAG;AAC1C,WAAO,EAAE,MAAM,KAAK,UAAU,WAAW,WAAW,MAAM;AAAA,EACjD,WAAA,qBAAqB,SAAS,GAAG,GAAG;AAC7C,WAAO,EAAE,MAAM,KAAK,UAAU,cAAc,WAAW,MAAM;AAAA,EACpD,WAAA,gBAAgB,SAAS,GAAG,GAAG;AACxC,WAAO,EAAE,MAAM,KAAK,UAAU,SAAS,WAAW,MAAM;AAAA,EAAA,OACnD;AACL,WAAO,EAAE,MAAM,KAAK,UAAU,WAAW,WAAW,MAAM;AAAA,EAAA;AAE9D;AAGA,QAAQ,OAAO,eAAe,OAAO,GAAG,aAAqB;AACvD,MAAA;AACF,UAAM,UAAU,MAAM,GAAG,SAAS,SAAS,UAAU,OAAO;AACrD,WAAA;AAAA,WACAX,QAAO;AACN,YAAA,MAAM,uBAAuBA,MAAK;AACpC,UAAAA;AAAA,EAAA;AAEV,CAAC;AAGD,QAAQ,OAAO,uBAAuB,OAAO,GAAG,aAAqB;AAC/D,MAAA;AACI,UAAA,WAAW,YAAY,QAAQ;AAEjC,QAAA,CAAC,SAAS,WAAW;AAChB,aAAA;AAAA,QACL,SAAS;AAAA,QACT;AAAA,QACA,OAAO,aAAa,SAAS,IAAI;AAAA,QACjC,SAAS,sBAAsB,QAAQ;AAAA,MACzC;AAAA,IAAA;AAGF,QAAI,UAAU;AACd,QAAI,YAAY;AAEZ,QAAA,SAAS,aAAa,QAAQ;AAEhC,gBAAU,MAAM,GAAG,SAAS,SAAS,UAAU,OAAO;AAAA,IAAA,WAC7C,SAAS,aAAa,UAAU;AAErC,UAAA,SAAS,SAAS,SAAS;AACnB,kBAAA,MAAM,gBAAgB,QAAQ;AAAA,MAAA,WAC/B,SAAS,SAAS,QAAQ;AACzB,kBAAA;AAAA,MAAA,OACL;AACK,kBAAA,kBAAkB,SAAS,IAAI;AAAA,MAAA;AAAA,IAC3C,WACS,SAAS,aAAa,OAAO;AAC5B,gBAAA;AAAA,IAAA,WACD,SAAS,aAAa,SAAS;AAExC,YAAM,cAAc,MAAM,GAAG,SAAS,SAAS,QAAQ;AACjD,YAAA,aAAa,YAAY,SAAS,QAAQ;AAC1C,YAAA,WAAW,YAAY,SAAS,IAAI;AAC9B,kBAAA,QAAQ,QAAQ,WAAW,UAAU;AACvC,gBAAA;AAAA,IAAA;AAGL,WAAA;AAAA,MACL,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,WACOA,QAAY;AACX,YAAA,MAAM,iCAAiCA,MAAK;AAC7C,WAAA;AAAA,MACL,SAAS;AAAA,MACT,UAAU,YAAY,QAAQ;AAAA,MAC9B,QAAOA,UAAA,gBAAAA,OAAO,YAAW;AAAA,MACzB,SAAS;AAAA,IACX;AAAA,EAAA;AAEJ,CAAC;AAGD,MAAM,cAAc,CAAC,cAA8B;AACjD,QAAM,YAAoC;AAAA,IACxC,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACA,SAAO,UAAU,UAAU,YAAY,CAAC,KAAK;AAC/C;AAGA,MAAM,wBAAwB,CAAC,aAAiD;AAC9E,UAAQ,SAAS,UAAU;AAAA,IACzB,KAAK;AACI,aAAA;AAAA,IACT,KAAK;AACI,aAAA;AAAA,IACT,KAAK;AACI,aAAA;AAAA,IACT,KAAK;AACI,aAAA;AAAA,IACT;AACS,aAAA;AAAA,EAAA;AAEb;AAGA,MAAM,kBAAkB,OAAO,aAAsC;AAC/D,MAAA;AAEE,QAAA;AACI,YAAA,UAAUW,SAAQ,SAAS;AACjC,YAAM,SAAS,MAAM,QAAQ,eAAe,EAAE,MAAM,UAAU;AAC9D,aAAO,OAAO,SAAS;AAAA,aAChB,cAAc;AACrB,cAAQ,IAAI,8CAA8C;AAGtD,UAAA;AACI,cAAA,SAASA,SAAQ,SAAS;AAC1B,cAAA,MAAM,IAAI,OAAO,QAAQ;AACzB,cAAA,cAAc,IAAI,WAAW,mBAAmB;AAEtD,YAAI,aAAa;AAET,gBAAA,cAAc,YACjB,QAAQ,YAAY,GAAG,EACvB,QAAQ,QAAQ,GAAG,EACnB,KAAK;AAER,iBAAO,eAAe;AAAA,QAAA;AAAA,eAEjB,UAAe;AACtB,gBAAQ,IAAI,2BAA0B,qCAAU,YAAW,eAAe;AAAA,MAAA;AAGrE,aAAA;AAAA;AAAA,QAAsB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,IAAA;AAAA,WAEhCX,QAAY;AACX,YAAA,MAAM,+BAA+BA,MAAK;AAC3C,WAAA;AAAA;AAAA,QAAsB,QAAQ;AAAA;AAAA,iBAAqBA,UAAA,gBAAAA,OAAO,YAAW,eAAe;AAAA,EAAA;AAE/F;AAGA,QAAQ,OAAO,qBAAqB,OAAO,GAAG,aAAqB;AAC7D,MAAA;AACF,UAAM,SAAS,MAAM,GAAG,SAAS,SAAS,QAAQ;AAC3C,WAAA;AAAA,WACAA,QAAO;AACN,YAAA,MAAM,8BAA8BA,MAAK;AAC3C,UAAAA;AAAA,EAAA;AAEV,CAAC;AAGD,QAAQ,OAAO,gBAAgB,OAAO,GAAG,UAAkB,YAAoB;AACzE,MAAA;AACF,UAAM,GAAG,SAAS,UAAU,UAAU,SAAS,OAAO;AAC/C,WAAA,EAAE,SAAS,KAAK;AAAA,WAChBA,QAAO;AACN,YAAA,MAAM,uBAAuBA,MAAK;AACpC,UAAAA;AAAA,EAAA;AAEV,CAAC;AAGD,QAAQ,GAAG,2BAA2B,CAAC,OAAO,aAAqB;AAEjE,MAAI,OAAO,CAAC,IAAI,eAAe;AACzB,QAAA,YAAY,KAAK,2BAA2B,QAAQ;AAAA,EAAA;AAE5D,CAAC;AAGD,QAAQ,GAAG,uBAAuB,CAAC,OAAO,aAAqB;AAE7D,MAAI,OAAO,CAAC,IAAI,eAAe;AACzB,QAAA,YAAY,KAAK,uBAAuB,QAAQ;AAAA,EAAA;AAExD,CAAC;AAGD,QAAQ,GAAG,YAAY,MAAM;AAC3B,MAAI,KAAK;AACX,CAAC;AAGD,eAAe,qBAAqB;AAC9B,MAAA;AACF,QAAI,CAAC,KAAK;AACF,YAAA,IAAI,MAAM,gDAAgD;AAAA,IAAA;AAEhD,sBAAA,IAAI,4BAA4B,GAAG;AAG/C,UAAA,OAAO,QAAY,IAAA;AACnB,UAAA,OAAO,QAAY,IAAA;AACnB,UAAA,WAAW,QAAY,IAAA;AACvB,UAAA,WAAW,QAAY,IAAA;AACvB,UAAA,QAAQ,QAAY,IAAA;AAE1B,UAAM,cAAc,UAAU,QAAQ,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,GAAG,UAAU,MAAM,KAAK,MAAM,KAAK;AACrG,YAAQ,IAAI,8BAA8B,UAAU,QAAQ,QAAQ,IAAI,IAAI,IAAI,GAAG,UAAU,MAAM,KAAK,MAAM,KAAK,EAAE;AAGrH,UAAM,YAAY,MAAM,gBAAgB,QAAQ,WAAW;AAE3D,QAAI,WAAW;AACb,cAAQ,IAAI,wDAAwD;AAGpE,UAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,YAAI,YAAY,KAAK,sBAAsB,EAAE,WAAW,MAAM;AAAA,MAAA;AAAA,IAChE,OACK;AACL,cAAQ,KAAK,2DAA2D;AAGxE,UAAI,OAAO,CAAC,IAAI,eAAe;AACzB,YAAA,YAAY,KAAK,sBAAsB;AAAA,UACzC,WAAW;AAAA,UACX,OAAO;AAAA,QAAA,CACR;AAAA,MAAA;AAAA,IACH;AAAA,WAEKA,QAAO;AACN,YAAA,MAAM,qDAAqDA,MAAK;AAGxE,QAAI,OAAO,CAAC,IAAI,eAAe;AACzB,UAAA,YAAY,KAAK,sBAAsB;AAAA,QACzC,WAAW;AAAA,QACX,OAAQA,OAAgB,WAAW;AAAA,MAAA,CACpC;AAAA,IAAA;AAAA,EACH;AAEJ;AAKA,QAAQ,OAAO,kCAAkC,YAAY;AACvD,MAAA;AACF,QAAI,CAAC,iBAAiB;AACb,aAAA;AAAA,QACL,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAAA,IAAA;AAGI,UAAA,iBAAiB,gBAAgB,kBAAkB;AAElD,WAAA;AAAA,MACL,WAAW,gBAAgB,mBAAmB;AAAA,MAC9C,GAAG;AAAA,IACL;AAAA,WACOA,QAAO;AACN,YAAA,MAAM,oCAAoCA,MAAK;AAChD,WAAA;AAAA,MACL,WAAW;AAAA,MACX,OAAQA,OAAgB,WAAW;AAAA,IACrC;AAAA,EAAA;AAEJ,CAAC;AAGD,QAAQ,OAAO,8BAA8B,YAAY;AACnD,MAAA;AACF,QAAI,CAAC,iBAAiB;AACd,YAAA,IAAI,MAAM,6CAA6C;AAAA,IAAA;AAG/D,UAAM,gBAAgB,iBAAiB;AAEhC,WAAA;AAAA,MACL,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,WACOA,QAAO;AACN,YAAA,MAAM,oCAAoCA,MAAK;AAChD,WAAA;AAAA,MACL,SAAS;AAAA,MACT,OAAQA,OAAgB,WAAW;AAAA,IACrC;AAAA,EAAA;AAEJ,CAAC;AAGD,IAAI,GAAG,eAAe,YAAY;AAChC,MAAI,iBAAiB;AACnB,YAAQ,IAAI,gCAAgC;AAC5C,UAAM,gBAAgB,WAAW;AAAA,EAAA;AAErC,CAAC;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}