{"name": "XGBoost", "displayName": "XGBoost回归", "description": "通过Boosting形式组合多个决策树来提高预测性能", "category": "tree", "type": "regression", "defaultParams": {"n_estimators": {"value": 100, "type": "number", "description": "集成中基学习器的数量", "displayName": "估计器数量", "min": 10, "max": 1000, "step": 10}, "max_depth": {"value": null, "type": "number", "description": "每个基学习器的最大深度", "displayName": "最大深度", "min": 1, "max": 50, "step": 1, "nullable": true}, "min_samples_split": {"value": 2, "type": "number", "description": "分割内部节点所需的最小样本数", "displayName": "最小分割样本数", "min": 2, "max": 100, "step": 1}, "min_samples_leaf": {"value": 1, "type": "number", "description": "叶节点所需的最小样本数", "displayName": "叶节点最小样本数", "min": 1, "max": 50, "step": 1}, "learning_rate": {"value": 0.1, "type": "number", "description": "学习率（仅适用于梯度提升）", "displayName": "学习率", "min": 0.01, "max": 1.0, "step": 0.01}, "random_state": {"value": 42, "type": "number", "description": "控制随机性的种子值", "displayName": "随机种子", "min": 0, "max": 1000, "step": 1, "nullable": true}}, "evaluation": {"splitDataset": {"value": false, "description": "是否划分训练/测试集"}, "trainRatio": {"value": 70, "min": 50, "max": 90, "step": 5, "description": "训练集比例(%)"}, "randomState": {"value": 42, "min": 0, "max": 1000, "description": "随机种子"}, "useModelValidation": {"value": false, "description": "是否使用交叉验证"}, "validationType": {"value": "k-fold", "options": ["k-fold", "leave-one-out"], "description": "验证方法"}, "kFolds": {"value": 5, "min": 2, "max": 10, "description": "k折交叉验证的k值"}}, "tips": ["集成学习通常比单一模型具有更好的泛化能力", "增加估计器数量可以提高性能，但会增加计算时间", "随机森林对过拟合有较强的抗性", "梯度提升需要调节学习率以平衡性能和过拟合风险"], "introduction": {"detailedDescription": "集成学习包括Bagging（如随机森林）和Boosting（如梯度提升）等方法。Bagging通过并行训练多个模型并平均预测结果来减少方差；Boosting通过串行训练，每个新模型都试图纠正前面模型的错误，从而减少偏差。", "usageTips": ["通常比单一模型具有更好的泛化能力", "适用于各种类型的数据和问题", "计算成本较高，训练时间较长", "参数调优相对复杂"], "scenarios": "适用于各种类型的数据和问题，通常比单一模型具有更好的泛化能力。", "mainParams": [{"name": "n_estimators", "description": "集成中基学习器的数量"}, {"name": "max_depth", "description": "每个基学习器的最大深度"}, {"name": "learning_rate", "description": "学习率，控制每个基学习器的贡献"}]}}