<template>
  <div class="model-charts">
    <div v-if="modelResult?.eval" class="charts-section">
      <div class="mb-4 text-right">
        <el-button type="primary" @click="saveAllCharts">
          <el-icon><Download /></el-icon>
          保存全部图表
        </el-button>
      </div>

      <div class="charts-grid">
        <ChartCard
          v-for="dataset in availableDatasets"
          :key="dataset"
          :ref="(el) => setChartComponentRef(dataset, el)"
          :dataset="dataset"
          :title="datasetConfig.titles[dataset]"
          :data="modelResult.eval[dataset]"
          :chart-type="chartType"
          @save="saveSingleChart(dataset)"
        />
      </div>
    </div>
    <el-empty v-else description="暂无图表数据" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineComponent, h, onMounted } from "vue";
import { Download } from "@element-plus/icons-vue";
import { ElMessage, ElCard, ElButton, ElIcon } from "element-plus";
import { useDark, useECharts } from "@pureadmin/utils";

interface Props {
  modelResult: any;
  chartType?: 'scatter' | 'line' | 'bar';
  datasetConfig?: {
    order: readonly string[];
    titles: Record<string, string>;
  };
}

const props = withDefaults(defineProps<Props>(), {
  chartType: 'scatter',
  datasetConfig: () => ({
    order: ["train", "test", "cv", "validation"] as const,
    titles: {
      train: "训练集",
      test: "测试集", 
      cv: "交叉验证",
      validation: "验证集"
    }
  })
});

// 可用数据集
const availableDatasets = computed(() =>
  props.modelResult?.eval
    ? props.datasetConfig.order.filter((key) => props.modelResult.eval[key])
    : [],
);

// ChartCard 组件
const ChartCard = defineComponent({
  name: "ChartCard",
  props: {
    dataset: String,
    title: String,
    data: Object,
    chartType: {
      type: String,
      default: 'scatter'
    }
  },
  emits: ["save"],
  setup(props, { emit, expose }) {
    const chartRef = ref<HTMLDivElement>();
    const { isDark } = useDark();
    const theme = computed(() => (isDark.value ? "dark" : "default"));
    let chartInstance: any = null;

    const initChart = () => {
      if (!chartRef.value || !props.data) return;
      
      const { setOptions, getInstance } = useECharts(chartRef, { theme });
      chartInstance = getInstance;
      
      const { y_true, y_predict } = props.data as any;
      
      if (!y_true || !y_predict) return;
      
      if (props.chartType === 'scatter') {
        // 散点图 - 预测值 vs 真实值
        const scatterData = y_true.map((val: number, i: number) => [
          val,
          y_predict[i],
        ]);
        const minVal = Math.min(...y_true, ...y_predict);
        const maxVal = Math.max(...y_true, ...y_predict);
        const range = [minVal - 1, maxVal + 1];
        
        setOptions({
          tooltip: {
            trigger: "item",
            formatter: (params: any) =>
              `真实值: ${params.value[0].toFixed(2)}<br/>预测值: ${params.value[1].toFixed(2)}`
          },
          grid: {
            left: "15%",
            right: "10%",
            bottom: "15%",
            top: "20%",
            containLabel: true,
          },
          xAxis: {
            type: "value",
            name: "真实值",
            min: range[0],
            max: range[1],
            axisLabel: { formatter: (value: number) => value.toFixed(2) },
          },
          yAxis: {
            type: "value",
            name: "预测值",
            min: range[0],
            max: range[1],
            axisLabel: { formatter: (value: number) => value.toFixed(2) },
          },
          series: [
            {
              name: "预测点",
              type: "scatter",
              data: scatterData,
              itemStyle: { color: "#409EFF" },
            },
            {
              name: "理想线",
              type: "line",
              data: [
                [range[0], range[0]],
                [range[1], range[1]],
              ],
              lineStyle: { type: "dashed", color: "#67C23A" },
              symbol: 'none'
            },
          ],
        });
      } else if (props.chartType === 'line') {
        // 折线图 - 真实值和预测值对比
        setOptions({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            }
          },
          legend: {
            data: ['真实值', '预测值']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: y_true.map((_: any, i: number) => i + 1)
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '真实值',
              type: 'line',
              data: y_true,
              itemStyle: { color: '#67C23A' }
            },
            {
              name: '预测值',
              type: 'line',
              data: y_predict,
              itemStyle: { color: '#409EFF' }
            }
          ]
        });
      }
    };

    onMounted(initChart);
    expose({
      getChartInstance: () => (chartInstance ? chartInstance() : null),
    });

    return () =>
      h(
        ElCard,
        { class: "chart-card" },
        {
          header: () =>
            h("div", { class: "flex justify-between items-center" }, [
              h("span", props.title),
              h(
                ElButton,
                { type: "primary", size: "small", onClick: () => emit("save") },
                { default: () => [h(ElIcon, () => h(Download)), " 保存"] },
              ),
            ]),
          default: () => h("div", { ref: chartRef, class: "chart" }),
        },
      );
  },
});

// 图表组件引用集合
const chartComponents = ref<Record<string, any>>({});
const setChartComponentRef = (dataset: string, el: any) => {
  if (el) chartComponents.value[dataset] = el;
};

const saveSingleChart = (dataset: string) => {
  const comp = chartComponents.value[dataset];
  if (!comp) {
    ElMessage.error("图表组件未找到");
    return;
  }
  const inst = comp.getChartInstance();
  if (!inst) {
    ElMessage.error("图表未初始化");
    return;
  }
  const dataURL = inst.getDataURL({
    type: "png",
    pixelRatio: 2,
    backgroundColor: "#fff",
  });
  const link = document.createElement("a");
  link.download = `${props.datasetConfig.titles[dataset]}_预测结果_${Date.now()}.png`;
  link.href = dataURL;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  ElMessage.success(`${props.datasetConfig.titles[dataset]}图表已保存`);
};

const saveAllCharts = async () => {
  for (const ds of availableDatasets.value) {
    saveSingleChart(ds);
    await new Promise((resolve) => setTimeout(resolve, 100));
  }
  ElMessage.success("所有图表已保存");
};
</script>

<style scoped>
.model-charts {
  width: 100%;
}

.charts-section {
  padding: 20px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

:deep(.chart-card) {
  min-height: 450px;

  .chart {
    width: 100%;
    height: 400px;
    min-height: 400px;
  }
}

@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
}
</style>
