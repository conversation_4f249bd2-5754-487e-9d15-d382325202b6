<template>
  <div class="params-setting-container">
    <!-- 评估设置 -->
    <el-card shadow="never" class="param-card">
      <template #header>
        <span class="param-card-title">模型评估设置</span>
      </template>

      <!-- 划分训练测试集部分 -->
      <div class="param-section">
        <el-checkbox v-model="evaluationParams.splitDataset">划分训练/测试集</el-checkbox>
        <div v-if="evaluationParams.splitDataset" class="param-content">
          <div class="param-item">
            <span class="param-label">训练集比例：</span>
            <el-slider
              v-model="evaluationParams.trainRatio"
              :min="evaluationConfig.trainRatio?.min || 50"
              :max="evaluationConfig.trainRatio?.max || 90"
              :step="evaluationConfig.trainRatio?.step || 5"
              show-input
            />
          </div>
          <div class="param-item">
            <span class="param-label">随机种子：</span>
            <el-input-number
              v-model="evaluationParams.randomState"
              :min="evaluationConfig.randomState?.min || 0"
              :max="evaluationConfig.randomState?.max || 1000"
              controls-position="right"
            />
          </div>
        </div>
      </div>

      <!-- 交叉验证部分 -->
      <div class="param-section">
        <el-checkbox v-model="evaluationParams.useModelValidation">检验模型（交叉验证）</el-checkbox>
        <div v-if="evaluationParams.useModelValidation" class="param-content">
          <div class="param-item">
            <el-radio-group v-model="evaluationParams.validationType">
              <el-radio value="k-fold">k折交叉验证</el-radio>
              <el-radio value="leave-one-out">留一法验证</el-radio>
            </el-radio-group>
          </div>
          <div v-if="evaluationParams.validationType === 'k-fold'" class="param-item">
            <span class="param-label">k值：</span>
            <el-input-number
              v-model="evaluationParams.kFolds"
              :min="evaluationConfig.kFolds?.min || 2"
              :max="evaluationConfig.kFolds?.max || 10"
              controls-position="right"
            />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 模型参数设置 -->
    <el-card shadow="never" class="param-card">
      <template #header>
        <div class="param-card-header">
          <span class="param-card-title">模型参数设置</span>
          <el-switch
            v-model="useCustomParams"
            active-text="自定义参数"
            inactive-text="使用预设"
            @change="handleCustomParamsToggle"
          />
        </div>
      </template>

      <!-- 预设参数模式 -->
      <div v-if="!useCustomParams && modelConfig" class="preset-params">
        <div v-for="(paramConfig, key) in modelConfig.defaultParams" :key="key" class="param-item">
          <span class="param-label" :title="paramConfig.description">
            {{ paramConfig.displayName }}：
          </span>
          <div class="param-control">
            <!-- 数字输入 -->
            <el-input-number
              v-if="paramConfig.type === 'number'"
              v-model="algorithmParams[key]"
              :min="paramConfig.min"
              :max="paramConfig.max"
              :step="paramConfig.step || 0.001"
              :precision="getPrecision(paramConfig.step)"
              controls-position="right"
              :placeholder="paramConfig.nullable ? '留空表示自动' : ''"
              clearable
            />
            <!-- 布尔开关 -->
            <el-switch
              v-else-if="paramConfig.type === 'boolean'"
              v-model="algorithmParams[key]"
            />
            <!-- 选择器 -->
            <el-select
              v-else-if="paramConfig.type === 'select'"
              v-model="algorithmParams[key]"
              placeholder="请选择"
            >
              <el-option
                v-for="option in paramConfig.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <!-- 文本输入 -->
            <el-input
              v-else
              v-model="algorithmParams[key]"
              :placeholder="paramConfig.description"
            />
          </div>
        </div>
      </div>

      <!-- 自定义参数模式 -->
      <div v-else class="custom-params">
        <div class="param-section">
          <div class="param-header">
            <span>自定义模型参数</span>
            <el-button
              link
              size="small"
              @click="resetToPreset"
              :disabled="!modelConfig"
            >
              重置为预设
            </el-button>
          </div>
          <div class="param-content-input">
            <el-input
              v-model="customParamsText"
              type="textarea"
              :rows="8"
              :placeholder="customParamsPlaceholder"
              @blur="validateCustomParams"
              @input="clearCustomParamsError"
            />
            <div v-if="customParamsError" class="error-message">
              {{ customParamsError }}
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import { ModelMetaEvaluation, ModelAlgorithm } from "@/types/models";

interface Props {
  algorithmName: string;
  modelConfig?: any;
  evaluationConfig?: Record<string, any>;
  customParamsPlaceholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  evaluationConfig: () => ({}),
  customParamsPlaceholder: '请输入JSON格式的参数'
});

const emit = defineEmits<{
  (e: "update:params", value: {
    algorithm: ModelAlgorithm;
    evaluation: ModelMetaEvaluation;
  }): void;
}>();

// 状态管理
const useCustomParams = ref(false);
const customParamsText = ref("");
const customParamsError = ref("");

// 算法参数
const algorithmParams = reactive<Record<string, any>>({});

// 评估参数
interface EvaluationParams {
  splitDataset: boolean;
  trainRatio: number;
  randomState: number;
  useModelValidation: boolean;
  validationType: "k-fold" | "leave-one-out";
  kFolds: number;
}

const evaluationParams = reactive<EvaluationParams>({
  splitDataset: false,
  trainRatio: 70,
  randomState: 42,
  useModelValidation: false,
  validationType: "k-fold",
  kFolds: 5,
});

// 计算属性
const finalAlgorithmParams = computed(() => {
  if (useCustomParams.value) {
    try {
      const customParams = JSON.parse(customParamsText.value || "{}");
      if (typeof customParams === 'object' && customParams !== null && !Array.isArray(customParams)) {
        return customParams;
      }
      return {};
    } catch {
      return {};
    }
  }
  
  const filteredParams: Record<string, any> = {};
  Object.entries(algorithmParams).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      filteredParams[key] = value;
    }
  });
  return filteredParams;
});

const finalEvaluationParams = computed((): ModelMetaEvaluation => {
  return {
    cv: evaluationParams.useModelValidation && evaluationParams.validationType === "k-fold"
      ? { k: evaluationParams.kFolds, randomState: evaluationParams.randomState }
      : undefined,
    loocv: evaluationParams.useModelValidation && evaluationParams.validationType === "leave-one-out",
    test: evaluationParams.splitDataset
      ? { size: 1 - evaluationParams.trainRatio / 100, randomState: evaluationParams.randomState }
      : undefined,
  };
});

// 方法
const handleCustomParamsToggle = (value: boolean) => {
  if (value) {
    customParamsText.value = JSON.stringify(algorithmParams, null, 2);
    customParamsError.value = "";
  } else {
    validateCustomParams();
  }
};

const validateCustomParams = () => {
  if (!useCustomParams.value || !customParamsText.value.trim()) {
    customParamsError.value = "";
    return;
  }

  try {
    const params = JSON.parse(customParamsText.value);
    if (typeof params !== 'object' || params === null || Array.isArray(params)) {
      customParamsError.value = "参数必须是一个对象";
      return;
    }
    customParamsError.value = "";
  } catch (error) {
    customParamsError.value = "JSON格式错误，请检查语法";
  }
};

const resetToPreset = () => {
  if (props.modelConfig) {
    customParamsText.value = JSON.stringify(algorithmParams, null, 2);
    customParamsError.value = "";
    ElMessage.success("已重置为预设参数");
  }
};

const getPrecision = (step?: number): number => {
  if (!step) return 3;
  const stepStr = step.toString();
  const decimalIndex = stepStr.indexOf('.');
  return decimalIndex === -1 ? 0 : stepStr.length - decimalIndex - 1;
};

const clearCustomParamsError = () => {
  if (customParamsError.value) {
    customParamsError.value = "";
  }
};

// 监听参数变化并emit
watch(
  [finalAlgorithmParams, finalEvaluationParams],
  ([newAlgorithmParams, newEvaluationParams]) => {
    const algorithmResult: ModelAlgorithm = {
      name: props.algorithmName,
      params: newAlgorithmParams,
    };

    const evaluationResult: ModelMetaEvaluation = {
      cv: newEvaluationParams.cv,
      loocv: newEvaluationParams.loocv,
      test: newEvaluationParams.test,
    };

    emit("update:params", {
      algorithm: algorithmResult,
      evaluation: evaluationResult
    });
  },
  { deep: true }
);

// 重置参数的方法
const resetParams = () => {
  console.log("BaseParamsSetting resetParams called");

  // 重置状态
  useCustomParams.value = false;
  customParamsText.value = "";
  customParamsError.value = "";

  // 清空算法参数
  Object.keys(algorithmParams).forEach((key) => {
    delete algorithmParams[key];
  });

  // 重置评估参数为默认值
  Object.assign(evaluationParams, {
    splitDataset: false,
    trainRatio: 70,
    randomState: 42,
    useModelValidation: false,
    validationType: "k-fold",
    kFolds: 5,
  });

  // 如果有模型配置，重新初始化参数
  if (props.modelConfig) {
    // 重新初始化评估参数
    Object.entries(props.evaluationConfig).forEach(([key, configValue]) => {
      if (key in evaluationParams && typeof configValue === 'object' && 'value' in configValue) {
        (evaluationParams as any)[key] = configValue.value;
      }
    });

    // 重新初始化算法参数
    const defaultParams = Object.entries(props.modelConfig.defaultParams).reduce((acc, [key, config]) => {
      acc[key] = (config as any).value;
      return acc;
    }, {} as Record<string, any>);

    Object.assign(algorithmParams, defaultParams);
    // 不要在重置时设置自定义参数文本，保持为空
    customParamsText.value = "";
  }

  console.log("BaseParamsSetting resetParams completed", {
    useCustomParams: useCustomParams.value,
    customParamsText: customParamsText.value,
    algorithmParams: algorithmParams
  });
};

// 强制重新加载配置的方法
const forceReloadConfig = () => {
  console.log("BaseParamsSetting forceReloadConfig called");

  // 完全清空所有状态
  useCustomParams.value = false;
  customParamsText.value = "";
  customParamsError.value = "";

  // 清空所有参数
  Object.keys(algorithmParams).forEach((key) => {
    delete algorithmParams[key];
  });

  // 重置评估参数
  Object.assign(evaluationParams, {
    splitDataset: false,
    trainRatio: 70,
    randomState: 42,
    useModelValidation: false,
    validationType: "k-fold",
    kFolds: 5,
  });

  // 如果有模型配置，重新初始化参数
  if (props.modelConfig) {
    // 重新初始化评估参数
    Object.entries(props.evaluationConfig).forEach(([key, configValue]) => {
      if (key in evaluationParams && typeof configValue === 'object' && 'value' in configValue) {
        (evaluationParams as any)[key] = configValue.value;
      }
    });

    // 重新初始化算法参数
    const defaultParams = Object.entries(props.modelConfig.defaultParams).reduce((acc, [key, config]) => {
      acc[key] = (config as any).value;
      return acc;
    }, {} as Record<string, any>);

    Object.assign(algorithmParams, defaultParams);
  }

  console.log("BaseParamsSetting forceReloadConfig completed");
};

// 暴露方法给父组件
defineExpose({
  algorithmParams,
  evaluationParams,
  customParamsText,
  resetParams,
  forceReloadConfig,
  initializeParams: (params: Record<string, any>) => {
    Object.assign(algorithmParams, params);
    customParamsText.value = JSON.stringify(params, null, 2);
  },
});
</script>

<style scoped>
.params-setting-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 参数卡片 */
.param-card {
  border: 1px solid var(--el-border-color-light);
}

.param-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.param-card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 参数区域 */
.param-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  margin-bottom: 16px;
}

.param-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-left: 32px;
  padding-top: 12px;
}

.param-item {
  display: flex;
  align-items: center;
  gap: 16px;
  min-height: 36px;
  margin-bottom: 16px;
}

.param-label {
  min-width: 140px;
  color: var(--el-text-color-regular);
  font-size: 14px;
  flex-shrink: 0;
  text-align: right;
  padding-right: 8px;
}

.param-control {
  flex: 1;
  max-width: 320px;
  display: flex;
  align-items: center;
}

/* 预设参数区域 */
.preset-params {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 20px;
}

.preset-params .param-item:last-child {
  margin-bottom: 0;
}

/* 自定义参数区域 */
.custom-params {
  padding: 20px;
}

.param-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.param-content-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.error-message {
  color: var(--el-color-danger);
  font-size: 12px;
  line-height: 1.4;
  padding: 4px 8px;
  background: var(--el-color-danger-light-9);
  border-radius: 4px;
  border-left: 3px solid var(--el-color-danger);
}

/* 滑块样式 */
:deep(.el-slider) {
  flex: 1;
  margin-right: 16px;
  min-width: 120px;
}

:deep(.el-slider__input) {
  width: 90px;
}

:deep(.el-slider .el-input-number) {
  width: 90px;
}

/* 输入框样式 */
:deep(.el-input-number) {
  width: 140px;
}

:deep(.el-select) {
  width: 220px;
}

/* 开关样式 */
:deep(.el-switch) {
  margin-left: 0;
  --el-switch-on-color: var(--el-color-primary);
}

/* 深度样式 */
:deep(.el-textarea__inner) {
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 13px;
  line-height: 1.4;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-switch__label) {
  font-size: 13px;
}

:deep(.el-alert) {
  padding: 8px 12px;
}

:deep(.el-alert__title) {
  font-size: 13px;
  line-height: 1.4;
}

/* 单选按钮组样式 */
:deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

:deep(.el-radio) {
  margin-right: 0;
}

/* 复选框样式 */
:deep(.el-checkbox) {
  margin-right: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .param-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 20px;
  }

  .param-label {
    min-width: auto;
    text-align: left;
    padding-right: 0;
    font-weight: 600;
  }

  .param-control {
    width: 100%;
    max-width: none;
  }

  .param-content {
    margin-left: 16px;
  }

  :deep(.el-slider) {
    margin-right: 0;
    margin-bottom: 8px;
  }

  :deep(.el-input-number),
  :deep(.el-select) {
    width: 100%;
    max-width: 280px;
  }
}
</style>
