<template>
  <div class="ml-model-result-page">
    <!-- 模型结果内容 -->
    <div v-if="modelResult" class="result-content">
      <ModelResultContainer
        :model-result="modelResult"
        :info-column-count="2"
        :show-evaluation-method="true"
        :show-metrics-cards="false"
        :show-metrics-table="true"
        :chart-type="'scatter'"
        :show-error-rate="false"
        :dataset-config="datasetConfig"
      />
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-container">
      <el-empty description="未找到模型结果数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import ModelResultContainer from "@/components/modelManagement/src/modelResult/ModelResultContainer.vue";

defineOptions({
  name: "MLModelResult",
});

const route = useRoute();
const modelResult = ref<any>(null);

// 数据集配置
const datasetConfig = {
  order: ["train", "test", "cv"] as const,
  titles: {
    train: "训练集",
    test: "测试集",
    cv: "交叉验证",
  },
};

// 自定义字段配置
const customFields = computed(() => [
  {
    key: "duration",
    label: "训练时长",
    formatter: (value: number) => formatDuration(value),
  },
  {
    key: "datasetSize",
    label: "数据集大小",
    formatter: (value: number | string) => formatDatasetSize(value),
  },
]);

const formatDuration = (duration: number) => {
  if (!duration) return "-";

  if (duration < 60) {
    return `${duration.toFixed(2)}秒`;
  } else if (duration < 3600) {
    return `${(duration / 60).toFixed(2)}分钟`;
  } else {
    return `${(duration / 3600).toFixed(2)}小时`;
  }
};

const formatDatasetSize = (size: number | string) => {
  if (typeof size === "number") {
    return `${size.toLocaleString()} 条记录`;
  }
  return size;
};

// 加载模型结果数据
onMounted(() => {
  try {
    const resultData = route.query.result as string;
    const taskId = route.query.taskId as string;
    const modelType = route.query.modelType as string;

    if (resultData) {
      modelResult.value = JSON.parse(resultData);

      // 补充任务信息
      if (taskId && !modelResult.value.taskId) {
        modelResult.value.taskId = taskId;
      }
      if (modelType && !modelResult.value.modelType) {
        modelResult.value.modelType = modelType;
      }

      // 添加时间戳（如果没有的话）
      if (!modelResult.value.timestamp) {
        modelResult.value.timestamp = Date.now();
      }

      console.log("Model result loaded:", modelResult.value);
    } else {
      ElMessage.warning("未找到模型结果数据");
    }
  } catch (err) {
    ElMessage.error("数据加载失败: " + (err as Error).message);
    console.error("Error loading model result:", err);
  }
});
</script>

<style scoped>
.ml-model-result-page {
  padding: 20px;
  min-height: 100vh;
  background: var(--el-bg-color);
}

.page-header {
  margin-bottom: 20px;
}

.loading-container,
.error-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.result-content {
  max-width: 1200px;
  margin: 0 auto;
}

.mb-4 {
  margin-bottom: 16px;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}
</style>
