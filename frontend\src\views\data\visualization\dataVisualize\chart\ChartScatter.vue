<template>
  <div style="width: 96%; height: 100vh">
    <el-card style="margin-top: 30px">
      <!-- 添加列选择器 -->
      <div style="margin-bottom: 20px; display: flex; gap: 20px">
        X轴：
        <el-select v-model="xAxisField" placeholder="请选择X轴" style="flex: 1">
          <el-option
            v-for="header in header_table"
            :key="header"
            :label="header"
            :value="header"
            :disabled="header === yAxisField"
          />
        </el-select>
        Y轴：
        <el-select v-model="yAxisField" placeholder="请选择Y轴" style="flex: 1">
          <el-option
            v-for="header in header_table"
            :key="header"
            :label="header"
            :value="header"
            :disabled="header === xAxisField"
          />
        </el-select>
      </div>
      <div ref="chartRef" style="width: 100%; height: 60vh" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watchEffect, onMounted } from "vue";
import { useDark, useECharts } from "@pureadmin/utils";

const { data: data_table, header: header_table } = defineProps([
  "data",
  "header"
]);

// 兼容dark主题
const { isDark } = useDark();
let theme = computed(() => {
  return isDark.value ? "dark" : "default";
});

// Echarts配置相关
const chartRef = ref();
const { setOptions, getInstance } = useECharts(chartRef, { theme });

// 用于记录x轴和y轴的取值
const xAxisField = ref("");
const yAxisField = ref("");

// 初始化默认选择前两列
onMounted(() => {
  if (header_table?.length >= 2) {
    xAxisField.value = header_table[0];
    yAxisField.value = header_table[1];
  }
});

// 生成散点图数据
const scatterData = computed(() => {
  if (!xAxisField.value || !yAxisField.value || !data_table) return [];

  const xIndex = header_table.indexOf(xAxisField.value);
  const yIndex = header_table.indexOf(yAxisField.value);

  if (xIndex === -1 || yIndex === -1) return [];

  return data_table.map(row => [row[xIndex], row[yIndex]]);
});

// 监听数据变化更新图表
watchEffect(() => {
  setOptions({
    tooltip: {
      formatter: (params: any) => {
        return `${xAxisField.value}: ${params.value[0]}<br/>
                ${yAxisField.value}: ${params.value[1]}`;
      }
    },
    xAxis: {
      name: xAxisField.value,
      type: "value",
      // nameLocation: "center",
      nameGap: 30
    },

    yAxis: {
      name: yAxisField.value,
      type: "value",
      // nameLocation: "center",
      nameGap: 30
    },

    series: [
      {
        symbolSize: 10,
        data: scatterData.value,
        type: "scatter"
        // itemStyle: {
        //   color: isDark.value ? "#4c98f7" : "#3a8ee6"
        // }
      }
    ]
  });
});

import { exportChartInstance } from "@/utils/chartExport";
// 暴露给父组件的方法-用于按钮点击时的下载
function exportChartImage(type: "png" | "svg" = "png") {
  const chart = getInstance();
  exportChartInstance(chart, type);
}
defineExpose({ exportChartImage });
</script>
