<template>
  <div class="flex flex-col items-center mt-5 w-full h-full">
    <div class="flex justify-between items-center w-full max-w-screen-xl px-4">
      <!--左边放下拉框-->
      <!-- <el-card class="w-full max-w-[300px]" shadow="hover"> -->
      <header class="w-full max-w-[300px]">
        图表类型：
        <el-select
          v-model="selectedChartType"
          placeholder="选择图表类型"
          class="w-full"
        >
          <el-option label="饼图" value="pie" />
          <el-option label="柱状图" value="bar" />
          <el-option label="热力图" value="heatmap" />
          <el-option label="箱线图" value="box" />
          <el-option label="散点图" value="scatter" />
          <el-option label="3D散点图" value="scatter3d" />
        </el-select>
      </header>
      <!-- </el-card> -->

      <!-- 右边放按钮 -->
      <div class="flex space-x-4">
        <el-button type="primary" @click="handleUpdateData">更新数据</el-button>
        <el-button type="success" @click="handleExportImage"
          >导出图片</el-button
        >
      </div>
    </div>

    <!-- 展示图表 -->
    <!-- 感觉这里展示图表应该使用路由的方式，而不是使用v-if的方式？ -->
    <div v-if="selectedChartType === 'pie'" class="chart-container mt-4">
      <ChartPie ref="currentChartRef" :data="tableData" :header="tableHeader" />
    </div>
    <div v-if="selectedChartType === 'bar'" class="chart-container mt-4">
      <ChartBar ref="currentChartRef" :data="tableData" :header="tableHeader" />
    </div>
    <div
      v-if="selectedChartType === 'heatmap'"
      class="chart-container mt-4 mb-4"
    >
      <ChartHeatMap
        ref="currentChartRef"
        :data="tableData"
        :header="tableHeader"
      />
    </div>
    <div v-if="selectedChartType === 'box'" class="chart-container mt-4">
      <ChartBox ref="currentChartRef" :data="tableData" :header="tableHeader" />
    </div>
    <div v-if="selectedChartType === 'scatter'" class="chart-container mt-4">
      <ChartScatter
        ref="currentChartRef"
        :data="tableData"
        :header="tableHeader"
      />
    </div>
    <div v-if="selectedChartType === 'scatter3d'" class="chart-container mt-4">
      <ChartScatter3D
        ref="currentChartRef"
        :data="tableData"
        :header="tableHeader"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from "vue";
import {
  ChartPie,
  ChartBar,
  ChartHeatMap,
  ChartBox,
  ChartScatter,
  ChartScatter3D
} from "./chart";
import { useTableDataStore } from "@/store/modules/tableData";
import { ElMessage } from "element-plus";

/* 获取表格的数据和表头 */
const tableData = useTableDataStore().currentTableData;
const tableHeader = useTableDataStore().currentTableHeader;

/* 下拉框内选择内容 */
const selectedChartType = ref("pie"); // 默认选择饼图

/* 表格数据更新->测试 */
const handleUpdateData = () => {
  console.log("是否拿到了表格中的数据呢", tableData);
};

const currentChartRef = ref<any>(null); // 当前选中的图表组件实例

/* 导出图片 */
const handleExportImage = () => {
  if (currentChartRef.value?.exportChartImage) {
    currentChartRef.value.exportChartImage("png"); // or "svg"
  } else {
    ElMessage.warning("当前图表不支持导出");
  }
};
</script>

<style scoped>
/* 自定义样式 */
.el-card {
  border-radius: 12px;
  overflow: hidden;
}

.el-select {
  font-size: 14px;
}

.el-button {
  font-size: 16px;
  padding: 10px 20px;
  border-radius: 6px;
}

.chart-container {
  width: 100%;
  /* height: 100vh; */
  height: 100%;
  max-width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-container {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 确保整个页面的高度不被拉伸 */
  min-height: 100vh; /* 设置最小高度为100vh，避免容器过度伸展 */
  padding: 10px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .el-select {
    font-size: 12px;
  }

  .el-button {
    font-size: 14px;
    padding: 8px 16px;
  }

  .chart-container {
    height: 40vh; /* 小屏幕下图表高度适当调整 */
  }
}
</style>
