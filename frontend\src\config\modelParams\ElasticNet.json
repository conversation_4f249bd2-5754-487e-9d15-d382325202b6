{"name": "ElasticNet", "displayName": "弹性网络回归", "description": "结合L1和L2正则化的线性回归，兼具Ridge和Lasso的优点", "category": "linear", "type": "regression", "defaultParams": {"alpha": {"value": 1.0, "type": "number", "description": "正则化强度，值越大正则化越强", "displayName": "正则化参数(α)", "min": 0.001, "max": 100, "step": 0.001}, "l1_ratio": {"value": 0.5, "type": "number", "description": "L1正则化在总正则化中的比例，0表示纯Ridge，1表示纯Lasso", "displayName": "L1比例", "min": 0, "max": 1, "step": 0.01}, "fit_intercept": {"value": true, "type": "boolean", "description": "是否计算截距项", "displayName": "计算截距"}, "positive": {"value": false, "type": "boolean", "description": "是否强制系数为正数", "displayName": "强制正系数"}, "random_state": {"value": null, "type": "number", "description": "随机种子", "displayName": "随机种子", "min": 0, "max": 1000, "nullable": true}, "selection": {"value": "cyclic", "type": "select", "description": "坐标下降算法中特征选择的策略", "displayName": "特征选择策略", "options": [{"value": "cyclic", "label": "循环选择"}, {"value": "random", "label": "随机选择"}]}}, "evaluation": {"splitDataset": {"value": false, "description": "是否划分训练/测试集"}, "trainRatio": {"value": 70, "min": 50, "max": 90, "step": 5, "description": "训练集比例(%)"}, "randomState": {"value": 42, "min": 0, "max": 1000, "description": "随机种子"}, "useModelValidation": {"value": false, "description": "是否使用交叉验证"}, "validationType": {"value": "k-fold", "options": ["k-fold", "leave-one-out"], "description": "验证方法"}, "kFolds": {"value": 5, "min": 2, "max": 10, "description": "k折交叉验证的k值"}}, "tips": ["弹性网络结合了Ridge和Lasso的优点", "l1_ratio=0时等价于Ridge回归", "l1_ratio=1时等价于Lasso回归", "l1_ratio=0.5时平衡L1和L2正则化", "适用于特征间存在相关性且需要特征选择的场景", "当特征数量远大于样本数量时表现优异"], "introduction": {"detailedDescription": "弹性网络回归结合了Ridge回归的L2正则化和Lasso回归的L1正则化。通过l1_ratio参数控制两种正则化的比例，既能进行特征选择，又能处理特征间的相关性，特别适用于高维数据和特征数量远大于样本数量的情况。", "usageTips": ["结合了Ridge和Lasso的优点", "l1_ratio参数控制L1和L2正则化的平衡", "适用于特征间存在相关性且需要特征选择的场景", "当特征数量远大于样本数量时表现优异"], "scenarios": "适用于特征数量远大于样本数量的情况，结合了Ridge和Lasso的优点。", "mainParams": [{"name": "alpha", "description": "正则化强度参数"}, {"name": "l1_ratio", "description": "L1和L2正则化的混合比例"}, {"name": "fit_intercept", "description": "是否计算截距项"}]}}