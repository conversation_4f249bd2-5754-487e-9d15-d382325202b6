<template>
  <div class="start-screen">
    <!-- 背景气泡装饰 -->
    <div class="background-decoration">
      <div class="floating-bubble bubble-1"></div>
      <div class="floating-bubble bubble-2"></div>
      <div class="floating-bubble bubble-3"></div>
      <div class="floating-bubble bubble-4"></div>
      <div class="floating-bubble bubble-5"></div>
      <div class="floating-bubble bubble-6"></div>
    </div>

    <!-- 退出按钮 -->
    <div class="exit-button-container">
      <button @click="handleExit" class="exit-button">退出</button>
    </div>

    <!-- 主要内容 -->
    <div class="main-content" :class="{ 'content-loaded': contentLoaded }">
      <h1 class="main-title">欢迎使用</h1>
      <div class="actions">
        <button @click="importProject" class="action-button primary-button">
          <span class="button-text">导入项目</span>
        </button>
        <button @click="openFile" class="action-button secondary-button">
          <span class="button-text">打开文件</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElLoading } from "element-plus";

// 动画状态
const contentLoaded = ref(false);

// 页面加载后触发动画
onMounted(() => {
  setTimeout(() => {
    contentLoaded.value = true;
  }, 300);
});

// Assuming you have ipcRenderer exposed via preload script
// If not, you'll need to set that up. Example: contextBridge.exposeInMainWorld('ipcRenderer', ipcRenderer)
// For now, let's assume window.ipcRenderer is available.
// declare global {
//   interface Window {
//     ipcRenderer: import('electron').IpcRenderer;
//   }
// }

// Adjust the type definition if you have one, or rely on 'any' if not strictly typed yet.
declare global {
  interface Window {
    ipcRenderer: {
      send: (channel: string, ...args: any[]) => void;
      invoke: (channel: string, ...args: any[]) => Promise<any>;
      on: (channel: string, listener: (event: any, ...args: any[]) => void) => void;
      off: (channel: string, ...args: any[]) => void; // Added off based on preload
      removeAllListeners: (channel: string) => void; // Added based on preload
    };
  }
}

// 退出应用
const handleExit = () => {
  window.ipcRenderer.send('app-quit');
};

const importProject = async () => {
  try {
    // const filePath = await window.ipcRenderer.invoke('dialog:openDirectory');
    // if (filePath) {
    //   console.log('Project directory selected:', filePath);
      // Here you would typically store the filePath in your app's state (e.g., Vuex)
      // and then navigate or trigger the main window.
      // For now, just proceeding to main window with a default dashboard route.
      // window.ipcRenderer.send('APP_READY_TO_SHOW_MAIN_WINDOW', { targetRoute: '/dashboard' });
      
      // Using the existing fs:readDirectory and dialog:openDirectory from main/index.ts
      const directoryPath = await window.ipcRenderer.invoke('dialog:openDirectory');
      if (directoryPath) {
        console.log('Project directory selected:', directoryPath);
        // Store directoryPath or handle it as needed
        // For example, navigate to a workspace view and pass the path
        window.ipcRenderer.send('APP_READY_TO_SHOW_MAIN_WINDOW', { targetRoute: `/workspace/${encodeURIComponent(directoryPath)}` });
      }
    // }
  } catch (error) {
    console.error('Error importing project:', error);
  }
};

const openFile = async () => {
  try {
    const filePath = await window.ipcRenderer.invoke('dialog:openFile');
    if (filePath) {
      console.log('File selected:', filePath);

      // 显示文件打开加载动画
      const loadingInstance = ElLoading.service({
        fullscreen: true,
        text: `正在打开文件: ${filePath.split(/[/\\]/).pop()}`,
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 延迟一下确保加载动画能显示
      setTimeout(() => {
        try {
          // Check if it's an Excel file
          const isExcelFile = /\.(xlsx|xls|csv)$/i.test(filePath);

          if (isExcelFile) {
            // For Excel files, navigate to dataImandEx page and set current file
            window.ipcRenderer.send('APP_READY_TO_SHOW_MAIN_WINDOW', {
              targetRoute: `/dataManagement/imandex`,
              openedFilePath: filePath,
              singleFileMode: true,
              clearCache: true
            });
          } else {
            // For other files, create a single-file workspace
            const fileDir = filePath.substring(0, filePath.lastIndexOf('/') || filePath.lastIndexOf('\\'));
            window.ipcRenderer.send('APP_READY_TO_SHOW_MAIN_WINDOW', {
              targetRoute: `/workspace/${encodeURIComponent(fileDir)}`,
              openedFilePath: filePath,
              singleFileMode: true,
              clearCache: true // Signal to clear cache
            });
          }
        } finally {
          // 延迟关闭加载动画
          setTimeout(() => {
            loadingInstance.close();
          }, 500);
        }
      }, 100);
    } else {
      console.log('Open file dialog canceled or no file selected.');
    }
  } catch (error) {
    console.error('Error opening file:', error);
  }
};

// It's important that your preload script (e.g., frontend/electron/preload/index.ts or similar)
// exposes ipcRenderer safely. For example:
//
// import { contextBridge, ipcRenderer } from 'electron';
//
// contextBridge.exposeInMainWorld('electron', {
//   ipcRenderer: {
//     send(channel: string, ...args: any[]) {
//       ipcRenderer.send(channel, ...args);
//     },
//     invoke(channel: string, ...args: any[]) {
//       return ipcRenderer.invoke(channel, ...args);
//     },
//     on(channel: string, listener: (event: any, ...args: any[]) => void) {
//       ipcRenderer.on(channel, listener);
//     },
//     removeAllListeners(channel: string) {
//       ipcRenderer.removeAllListeners(channel);
//     }
//   }
// });
//
// Make sure your tsconfig.json includes the type for window.electron if you use this.
</script>

<style scoped>
.start-screen {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  background: linear-gradient(135deg, #f5faff 0%, #dff1ff 100%);
  overflow: hidden;
}

/* 背景气泡装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-bubble {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.15), rgba(116, 185, 255, 0.1));
  animation: bubble-rise 12s infinite ease-in-out;
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.2);
}

.bubble-1 {
  width: 60px;
  height: 60px;
  left: 10%;
  animation-delay: 0s;
}

.bubble-2 {
  width: 80px;
  height: 80px;
  left: 20%;
  animation-delay: 2s;
}

.bubble-3 {
  width: 40px;
  height: 40px;
  left: 70%;
  animation-delay: 4s;
}

.bubble-4 {
  width: 100px;
  height: 100px;
  left: 80%;
  animation-delay: 1s;
}

.bubble-5 {
  width: 50px;
  height: 50px;
  left: 50%;
  animation-delay: 3s;
}

.bubble-6 {
  width: 70px;
  height: 70px;
  left: 30%;
  animation-delay: 5s;
}

/* 气泡上浮动画 */
@keyframes bubble-rise {
  0% {
    transform: translateY(100vh) scale(0.5) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  90% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100px) scale(1.5) rotate(360deg);
    opacity: 0;
  }
}

/* 退出按钮 */
.exit-button-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.exit-button {
  padding: 10px 20px;
  font-size: 0.9em;
  cursor: pointer;
  border: 2px solid #dc3545;
  border-radius: 25px;
  background-color: rgba(220, 53, 69, 0.9);
  color: white;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.exit-button:hover {
  background-color: #c82333;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

/* 主要内容 */
.main-content {
  position: relative;
  z-index: 2;
  max-width: 500px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.main-content.content-loaded {
  opacity: 1;
  transform: translateY(0);
}

.main-title {
  margin-bottom: 40px;
  font-size: 3em;
  font-weight: bold;
  background: linear-gradient(45deg, #409eff, #67c23a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: title-glow 2s ease-in-out infinite alternate;
}

@keyframes title-glow {
  from {
    text-shadow: 0 0 20px rgba(64, 158, 255, 0.5);
  }
  to {
    text-shadow: 0 0 30px rgba(103, 194, 58, 0.5);
  }
}

/* 按钮容器 */
.actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-button {
  position: relative;
  padding: 15px 30px;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  border: none;
  border-radius: 50px;
  transition: all 0.3s ease;
  overflow: hidden;
  min-width: 140px;
}

.primary-button {
  background: linear-gradient(45deg, #409eff, #67c23a);
  color: white;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
}

.secondary-button {
  background: linear-gradient(45deg, #909399, #606266);
  color: white;
  box-shadow: 0 4px 15px rgba(144, 147, 153, 0.3);
}

.action-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.primary-button:hover {
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.4);
}

.secondary-button:hover {
  box-shadow: 0 8px 25px rgba(144, 147, 153, 0.4);
}

/* 按钮波纹效果 */
.action-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.6s, height 0.6s, top 0.6s, left 0.6s;
  transform: translate(-50%, -50%);
}

.action-button:active::before {
  width: 300px;
  height: 300px;
}

.button-text {
  position: relative;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    margin: 20px;
    padding: 30px 20px;
  }

  .main-title {
    font-size: 2.5em;
  }

  .actions {
    /* 保持横向排布，即使在小屏幕上 */
    flex-direction: row;
    justify-content: center;
    gap: 15px;
  }

  .action-button {
    flex: 1;
    min-width: 120px;
    max-width: 160px;
    padding: 12px 20px;
    font-size: 1em;
  }
}

@media (max-width: 480px) {
  .actions {
    gap: 10px;
  }

  .action-button {
    min-width: 100px;
    max-width: 140px;
    padding: 10px 15px;
    font-size: 0.9em;
  }
}
</style>