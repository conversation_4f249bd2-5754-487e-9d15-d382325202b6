<template>
  <BaseModelDialog
    :model-value="dialogStore.dialogs.linearModel"
    title="构建线性模型"
    dialog-class="linear-model-dialog"
    :model-type="modelType"
    :model-display-name="getModelTypeLabel(modelType)"
    :is-building="isBuilding"
    @close="handleClose"
    @confirm="handleConfirm"
    @dialog-opened="handleDialogOpened"
  >
    <template #variable-selection="{ headers, modelType: type, onUpdateSelection }">
      <LinearModelSelection
        style="width: 100%"
        :headers="headers"
        :model-type="type"
        @update:selection="onUpdateSelection"
      />
    </template>

    <template #params-setting="{ algorithmName, onUpdateParams }">
      <LinearModelParamsSetting
        ref="paramsSettingRef"
        style="width: 100%"
        :algorithm-name="algorithmName"
        @update:params="onUpdateParams"
      />
    </template>
  </BaseModelDialog>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from "vue";
import { useDialogStore } from "@/store/modules/dialog";
import LinearModelSelection from "./linearModelSelection.vue";
import LinearModelParamsSetting from "./linearModelParamsSetting.vue";
import BaseModelDialog from "../common/BaseModelDialog.vue";
import type { ModelConfig } from "@/types/models";

const emit = defineEmits<{
  (e: "confirm", config: ModelConfig): void;
}>();

const props = defineProps<{
  modelType: string;
}>();

// 获取模型类型显示标签
const getModelTypeLabel = (type: string) => {
  const typeMap = {
    LinearRegression: "多元线性回归",
    Ridge: "岭回归",
    Lasso: "Lasso回归",
    ElasticNet: "弹性网络回归",
  };
  return typeMap[type as keyof typeof typeMap] || type;
};

const dialogStore = useDialogStore();
const isBuilding = ref(false);

// 子组件引用
const paramsSettingRef = ref();

// 监听dialog关闭，清空参数设置
watch(
  () => dialogStore.dialogs.linearModel,
  async (newValue, oldValue) => {
    if (!newValue && oldValue) {
      // dialog从打开状态变为关闭状态，清空参数设置
      await nextTick();
      if (paramsSettingRef.value?.resetParams) {
        paramsSettingRef.value.resetParams();
      }
    }
  }
);

// 处理dialog打开事件，重新初始化参数
const handleDialogOpened = async () => {
  console.log("Linear model dialog opened, force reloading config");
  await nextTick();
  if (paramsSettingRef.value?.forceReloadConfig) {
    paramsSettingRef.value.forceReloadConfig();
  }
};

// 处理关闭
const handleClose = () => {
  dialogStore.hideDialog("linearModel");
};

// 处理确认 - 这个函数会被BaseModelDialog调用
const handleConfirm = (config: ModelConfig) => {
  emit("confirm", config);
  dialogStore.hideDialog("linearModel");
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.model-intro-form-item {
  margin-bottom: 20px;
}

:deep(.el-form-item__content) {
  flex-wrap: nowrap;
}

:deep(.el-tabs__nav-scroll) {
  width: 50%;
  margin: 0 auto;
}

:deep(.model-intro-form-item .el-form-item__content) {
  width: 100%;
}
</style>