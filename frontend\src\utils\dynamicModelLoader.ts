/**
 * 动态模型配置加载器
 * 用于动态扫描和加载所有模型配置文件
 */

// 模型配置接口
export interface ModelConfig {
  name: string;
  displayName: string;
  description: string;
  category: "linear" | "tree" | "ml" | "clustering" | "preprocessing";
  type: "regression" | "classification" | "clustering" | "preprocessing";
  introduction?: {
    detailedDescription?: string;
    usageTips?: string[];
    scenarios?: string;
    mainParams?: Array<{
      name: string;
      displayName: string;
      description: string;
      value: any;
      type: string;
    }>;
  };
  defaultParams?: Record<string, any>;
  evaluation?: Record<string, any>;
}

// 模型元数据接口
export interface ModelMetadata {
  id: string; // 配置文件名（不含扩展名）
  fileName: string; // 完整文件名
  config: ModelConfig;
}

// 模型分类配置
export interface ModelCategory {
  key: string;
  label: string;
  description?: string;
  models: ModelMetadata[];
}

// 动态模型管理器类
export class DynamicModelManager {
  private static instance: DynamicModelManager;
  private modelConfigs: Map<string, ModelMetadata> = new Map();
  private categories: Map<string, ModelCategory> = new Map();
  private initialized = false;

  private constructor() {}

  public static getInstance(): DynamicModelManager {
    if (!DynamicModelManager.instance) {
      DynamicModelManager.instance = new DynamicModelManager();
    }
    return DynamicModelManager.instance;
  }

  /**
   * 初始化模型配置
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await this.loadAllModelConfigs();
      this.categorizeModels();
      this.initialized = true;
      console.log("Dynamic model manager initialized successfully");
    } catch (error) {
      console.error("Failed to initialize dynamic model manager:", error);
      throw error;
    }
  }

  /**
   * 动态加载所有模型配置文件
   */
  private async loadAllModelConfigs(): Promise<void> {
    try {
      // 使用 Vite 的 import.meta.glob 动态导入所有配置文件
      const configModules = import.meta.glob("@/config/modelParams/*.json", {
        eager: false,
        import: "default",
      });

      // 遍历所有找到的配置文件
      for (const [filePath, importFn] of Object.entries(configModules)) {
        try {
          // 从文件路径提取配置ID（文件名不含扩展名）
          const fileName = filePath.split("/").pop() || "";
          const configId = fileName.replace(".json", "");

          if (!configId) {
            console.warn(`Invalid file path: ${filePath}`);
            continue;
          }

          // 动态导入配置文件
          const config: ModelConfig = (await importFn()) as ModelConfig;

          // 自动推断分类（如果配置文件中没有指定）
          if (!config.category) {
            config.category = this.inferCategory(configId);
          }

          const metadata: ModelMetadata = {
            id: configId,
            fileName: fileName,
            config,
          };

          this.modelConfigs.set(configId, metadata);
          console.log(`Loaded model config: ${configId} from ${fileName}`);
        } catch (error) {
          console.warn(`Failed to load model config from ${filePath}:`, error);
        }
      }

      console.log(
        `Successfully loaded ${this.modelConfigs.size} model configurations`,
      );
    } catch (error) {
      console.error("Failed to load model configurations:", error);
    }
  }

  /**
   * 根据模型ID推断分类
   */
  private inferCategory(modelId: string): ModelConfig["category"] {
    const treeModels = [
      "DecisionTreeRegressor",
      "RandomForestRegressor",
      "GradientBoostingRegressor",
      "XGBoost",
    ];
    const linearModels = ["LinearRegression", "Ridge", "Lasso", "ElasticNet"];

    if (treeModels.includes(modelId)) return "tree";
    if (linearModels.includes(modelId)) return "linear";
    return "ml";
  }

  /**
   * 将模型按分类组织
   */
  private categorizeModels(): void {
    // 清空现有分类
    this.categories.clear();

    // 定义分类信息
    const categoryInfo = {
      linear: {
        key: "linear",
        label: "线性模型",
        description: "基于线性关系的回归模型",
      },
      tree: {
        key: "tree",
        label: "树模型",
        description: "基于决策树的集成学习模型",
      },
      ml: { key: "ml", label: "其他模型", description: "其他机器学习算法模型" },
      clustering: {
        key: "clustering",
        label: "聚类模型",
        description: "无监督聚类算法",
      },
      preprocessing: {
        key: "preprocessing",
        label: "预处理模型",
        description: "数据预处理算法",
      },
    };

    // 初始化分类
    Object.values(categoryInfo).forEach((info) => {
      this.categories.set(info.key, { ...info, models: [] });
    });

    // 将模型分配到对应分类
    this.modelConfigs.forEach((metadata) => {
      const category = this.categories.get(metadata.config.category);
      if (category) {
        category.models.push(metadata);
      }
    });

    // 移除空分类
    this.categories.forEach((category, key) => {
      if (category.models.length === 0) {
        this.categories.delete(key);
      }
    });
  }

  /**
   * 获取所有模型配置
   */
  public getAllModels(): ModelMetadata[] {
    return Array.from(this.modelConfigs.values());
  }

  /**
   * 根据ID获取模型配置
   */
  public getModelById(id: string): ModelMetadata | undefined {
    return this.modelConfigs.get(id);
  }

  /**
   * 获取所有分类
   */
  public getCategories(): ModelCategory[] {
    return Array.from(this.categories.values());
  }

  /**
   * 根据分类获取模型
   */
  public getModelsByCategory(category: string): ModelMetadata[] {
    const cat = this.categories.get(category);
    return cat ? cat.models : [];
  }

  /**
   * 检查模型是否为ML模型（非线性模型）
   */
  public isMLModel(modelId: string): boolean {
    const metadata = this.getModelById(modelId);
    return metadata ? metadata.config.category !== "linear" : false;
  }

  /**
   * 获取模型显示名称
   */
  public getModelDisplayName(modelId: string): string {
    const metadata = this.getModelById(modelId);
    return metadata ? metadata.config.displayName : modelId;
  }

  /**
   * 动态添加新模型配置
   * 注意：由于Vite的限制，新模型需要在构建时就存在于配置映射中
   */
  public async addModelConfig(configId: string): Promise<void> {
    console.warn(
      "addModelConfig: Due to Vite limitations, new models must be added to the static import map in loadAllModelConfigs method",
    );

    // 检查是否已经加载
    if (this.modelConfigs.has(configId)) {
      console.log(`Model config ${configId} already loaded`);
      return;
    }

    // 重新初始化以加载可能新增的配置
    await this.reload();
  }

  /**
   * 重新加载所有配置
   */
  public async reload(): Promise<void> {
    this.modelConfigs.clear();
    this.categories.clear();
    this.initialized = false;
    await this.initialize();
  }
}

// 导出单例实例
export const dynamicModelManager = DynamicModelManager.getInstance();

// 便捷函数
export const initializeModelManager = () => dynamicModelManager.initialize();
export const getAllModels = () => dynamicModelManager.getAllModels();
export const getModelById = (id: string) =>
  dynamicModelManager.getModelById(id);
export const getCategories = () => dynamicModelManager.getCategories();
export const getModelsByCategory = (category: string) =>
  dynamicModelManager.getModelsByCategory(category);
export const isMLModel = (modelId: string) =>
  dynamicModelManager.isMLModel(modelId);
export const getModelDisplayName = (modelId: string) =>
  dynamicModelManager.getModelDisplayName(modelId);
