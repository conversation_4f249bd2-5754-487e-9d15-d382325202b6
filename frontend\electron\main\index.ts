import { release } from "node:os";
import { fileURLToPath } from "node:url";
import { join, dirname } from "node:path";
import {
  type MenuItem,
  type MenuItemConstructorOptions,
  app,
  Menu,
  shell,
  ipcMain,
  BrowserWindow,
  dialog
} from "electron";

// 在文件顶部添加 fs 和 path 的引入
// import { existsSync, mkdirSync, readFileSync, writeFileSync, readdirSync, Stats, statSync } from 'node:fs'
// import { dialog } from 'electron'

// The built directory structure
//
// ├─┬ dist-electron
// │ ├─┬ main
// │ │ └── index.js    > Electron-Main
// │ └─┬ preload
// │   └── index.mjs    > Preload-Scripts
// ├─┬ dist
// │ └── index.html    > Electron-Renderer
//
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
process.env.DIST_ELECTRON = join(__dirname, "..");
process.env.DIST = join(process.env.DIST_ELECTRON, "../dist");
process.env.PUBLIC = process.env.VITE_DEV_SERVER_URL
  ? join(process.env.DIST_ELECTRON, "../public")
  : process.env.DIST;
// 是否为开发环境
const isDev = process.env["NODE_ENV"] === "development";

// Disable GPU Acceleration for Windows 7
if (release().startsWith("6.1")) app.disableHardwareAcceleration();

// Set application name for Windows 10+ notifications
if (process.platform === "win32") app.setAppUserModelId(app.getName());

if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}

// Remove electron security warnings
// This warning only shows in development mode
// Read more on https://www.electronjs.org/docs/latest/tutorial/security
// process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'

let win: BrowserWindow | null = null; // Will become mainWindow
let splashWin: BrowserWindow | null = null; // For the splash screen

// RabbitMQ 通知服务实例
let rabbitMQService: RabbitMQNotificationService | null = null;

// Here, you can also use other preload
const preload = join(__dirname, "../preload/index.mjs");
const url = process.env.VITE_DEV_SERVER_URL;
const indexHtml = join(process.env.DIST, "index.html");

// 创建菜单
function createMenu(label = "进入全屏幕") {
  const menu = Menu.buildFromTemplate(
    appMenu(label) as (MenuItemConstructorOptions | MenuItem)[]
  );
  Menu.setApplicationMenu(menu);
}

async function createMainWindow(initialRoute?: string) { // Renamed from createWindow, added initialRoute
  win = new BrowserWindow({
    width: 1024,
    height: 768,
    minWidth: 1024,
    minHeight: 768,
    title: "Main window",
    icon: join(process.env.PUBLIC, "favicon.ico"),
    webPreferences: {
      preload,
      nodeIntegration: false, // Recommended for security
      contextIsolation: true, // Required for contextBridge
    }
  });

  const targetUrl = initialRoute ? `${url}#${initialRoute}` : url;
  const targetIndexHtml = initialRoute ? { pathname: indexHtml, hash: initialRoute } : indexHtml;

  if (process.env.VITE_DEV_SERVER_URL) {
    // electron-vite-vue#298
    win.loadURL(targetUrl); // MainWindow loads the main content
    // Open devTool if the app is not packaged
    win.webContents.openDevTools({ mode: 'bottom' }); // 打开开发者工具
  } else {
    win.loadFile(typeof targetIndexHtml === 'string' ? targetIndexHtml : targetIndexHtml.pathname, typeof targetIndexHtml === 'string' ? {} : { hash: targetIndexHtml.hash }); // MainWindow loads the main content
  }

  createMenu();

  // Test actively push message to the Electron-Renderer
  win.webContents.on("did-finish-load", () => {
    win?.webContents.send("main-process-message", new Date().toLocaleString());
  });

  // Make all links open with the browser, not with the application
  win.webContents.setWindowOpenHandler(({ url }) => {
    // if (url.startsWith("https:")) shell.openExternal(url);
    // return { action: "deny" };
    // 这里用于新开一个页面--------->必须要把这个设置成允许，否则将不能使用路由打开一个子页面

    // 创建自定义窗口，隐藏菜单栏防止出现bug
    const childWindow = new BrowserWindow({
      autoHideMenuBar: true, // 隐藏菜单栏，防止出现bug
      webPreferences: {
        preload,
        nodeIntegration: false,
        contextIsolation: true
      }
    });

    childWindow.loadURL(url);

    return { action: "deny" }; // 阻止默认行为，使用我们自定义的窗口
  });
  // win.webContents.on('will-navigate', (event, url) => { }) #344

  // 窗口进入全屏状态时触发
  win.on("enter-full-screen", () => {
    createMenu("退出全屏幕");
  });

  // 窗口离开全屏状态时触发
  win.on("leave-full-screen", () => {
    createMenu();
  });

  // 检查是否启用RabbitMQ功能
  // 在开发环境中，环境变量可能需要从不同的地方读取
  const rabbitmqEnabled = process.env.VITE_RABBITMQ_ENABLED === 'true' ||
                          process.env.NODE_ENV === 'development'; // 开发环境默认启用

  console.log('RabbitMQ环境变量检查:', {
    VITE_RABBITMQ_ENABLED: process.env.VITE_RABBITMQ_ENABLED,
    NODE_ENV: process.env.NODE_ENV,
    rabbitmqEnabled
  });

  if (rabbitmqEnabled) {
    console.log('RabbitMQ功能已启用，开始初始化...');
    initializeRabbitMQ();
  } else {
    console.log('RabbitMQ功能已禁用');
  }
}

async function createSplashWindow() {
  splashWin = new BrowserWindow({
    width: 600, // Smaller size for splash
    height: 400, // Smaller size for splash
    frame: false, // No window frame
    resizable: false,
    movable: true, // Or false if you want it perfectly centered and static
    center: true,
    title: "Loading...", // Optional, won't be visible
    icon: join(process.env.PUBLIC, "favicon.ico"), // Keep icon consistent
    webPreferences: {
      preload,
      nodeIntegration: false, // Recommended for security
      contextIsolation: true, // Required for contextBridge
    },
  });

  const splashTargetUrl = `${url}#/start`; // Always load /start for splash
  const splashIndexHtml = { pathname: indexHtml, hash: '/start' }; // Always load /start for splash

  if (process.env.VITE_DEV_SERVER_URL) {
    splashWin.loadURL(splashTargetUrl); 
  } else {
    splashWin.loadFile(splashIndexHtml.pathname, { hash: splashIndexHtml.hash });
  }

  // No menu for splash screen
  // No dev tools for splash screen by default, can be enabled for debugging
}

app.whenReady().then(createSplashWindow); // Start with splash screen

app.on("window-all-closed", () => {
  // Splash window might be closed before main window, ensure 'win' refers to mainWindow
  if (BrowserWindow.getAllWindows().length === 0) { // Check if all windows are closed
    app.quit();
  }
});

app.on("second-instance", () => {
  // This logic might need adjustment depending on whether splash or main window should get focus
  if (win && !win.isDestroyed()) { // If main window exists and is not destroyed
    // Focus on the main window if the user tried to open another
    if (win.isMinimized()) win.restore();
    win.focus();
  } else if (splashWin && !splashWin.isDestroyed()) { // If only splash window exists and is not destroyed
    if (splashWin.isMinimized()) splashWin.restore();
    splashWin.focus();
  }
});

app.on("activate", () => {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  const allWindows = BrowserWindow.getAllWindows();
  if (allWindows.length === 0) {
    createSplashWindow(); // If no windows, always start with splash
  } else {
    // If windows exist, try to focus the main one, then splash
    if (win && !win.isDestroyed()) {
      if (win.isMinimized()) win.restore();
      win.focus();
    } else if (splashWin && !splashWin.isDestroyed()) {
      if (splashWin.isMinimized()) splashWin.restore();
      splashWin.focus();
    } else {
      // Fallback if recorded windows are destroyed for some reason
      allWindows[0].focus();
    }
  }
});

// Listener to close splash and open main window
ipcMain.on('APP_READY_TO_SHOW_MAIN_WINDOW', (event, args: {
  targetRoute?: string,
  openedFilePath?: string,
  singleFileMode?: boolean,
  clearCache?: boolean
} = {}) => {
  if (splashWin) {
    splashWin.close();
    splashWin = null;
  }
  createMainWindow(args.targetRoute); // Pass targetRoute to createMainWindow

  // If there's a file to open, send it to the main window
  if (args.openedFilePath) {
    console.log('Main process: Preparing to send file data:', {
      filePath: args.openedFilePath,
      targetRoute: args.targetRoute,
      singleFileMode: args.singleFileMode
    });

    const sendFileData = () => {
      console.log('Main process: Sending file data events');

      if (args.targetRoute?.includes('/dataManagement/imandex')) {
        // For Excel files
        console.log('Main process: Sending excel-file-selected event');
        win?.webContents.send('excel-file-selected', args.openedFilePath);
      } else {
        // For other files
        console.log('Main process: Sending workspace-file-selected event');
        win?.webContents.send('workspace-file-selected', args.openedFilePath);
      }

      if (args.singleFileMode) {
        console.log('Main process: Sending set-single-file-mode event');
        win?.webContents.send('set-single-file-mode', args.openedFilePath);
      }
    };

    win?.webContents.once('did-finish-load', sendFileData);
    win?.webContents.once('dom-ready', sendFileData);
    setTimeout(sendFileData, 1000);
  }
});

// 菜单栏 https://www.electronjs.org/zh/docs/latest/api/menu-item#%E8%8F%9C%E5%8D%95%E9%A1%B9
const appMenu = (fullscreenLabel: string) => {
  const menuItems: MenuItemConstructorOptions[] = [
    { label: "关于", role: "about" },
    { label: "开发者工具", role: "toggleDevTools" },
    { label: "强制刷新", role: "forceReload" },
    // Quit is moved to File menu
  ];
  if (!isDev) {
    const devToolsIndex = menuItems.findIndex(item => item.role === 'toggleDevTools');
    if (devToolsIndex > -1) menuItems.splice(devToolsIndex, 1);
    const forceReloadIndex = menuItems.findIndex(item => item.role === 'forceReload');
    if (forceReloadIndex > -1) menuItems.splice(forceReloadIndex, 1);
  }

  const template: Array<MenuItemConstructorOptions | MenuItem> = [
    {
      label: app.name,
      submenu: menuItems
    },
    {
      label: "文件",
      submenu: [
        {
          label: "导入项目...",
          accelerator: "CmdOrCtrl+Shift+O",
          click: async () => {
            if (win && !win.isDestroyed()) {
              win.focus();
              win.webContents.send('menu-triggered-import-project');
            } else {
              // Determine parent for dialog if only splash is open
              const parentWindow = (splashWin && !splashWin.isDestroyed()) ? splashWin : undefined;
              const dialogOptions: Electron.OpenDialogOptions = { properties: ['openDirectory'] };
              const directoryPathResult = parentWindow 
                ? await dialog.showOpenDialog(parentWindow, dialogOptions)
                : await dialog.showOpenDialog(dialogOptions);

              if (!directoryPathResult.canceled && directoryPathResult.filePaths.length > 0) {
                const projectPath = directoryPathResult.filePaths[0];
                if (splashWin && !splashWin.isDestroyed()) { splashWin.close(); splashWin = null; }
                createMainWindow(`/workspace/${encodeURIComponent(projectPath)}`);
              }
            }
          }
        },
        {
          label: "打开文件...",
          accelerator: "CmdOrCtrl+O",
          click: async () => {
            if (win && !win.isDestroyed()) {
              win.focus();
              win.webContents.send('menu-triggered-open-file');
            } else {
              const parentWindow = (splashWin && !splashWin.isDestroyed()) ? splashWin : undefined;
              const dialogOptions: Electron.OpenDialogOptions = { properties: ['openFile'] };
              const filePathResult = parentWindow
                ? await dialog.showOpenDialog(parentWindow, dialogOptions)
                : await dialog.showOpenDialog(dialogOptions);

              if (!filePathResult.canceled && filePathResult.filePaths.length > 0) {
                const filePath = filePathResult.filePaths[0];
                if (splashWin && !splashWin.isDestroyed()) { splashWin.close(); splashWin = null; }

                // Check if it's an Excel file
                const isExcelFile = /\.(xlsx|xls|csv)$/i.test(filePath);

                if (isExcelFile) {
                  // For Excel files, navigate to dataImandEx page
                  createMainWindow(`/dataManagement/imandex`);
                  // Send the file path to be opened
                  const sendExcelFile = () => {
                    win?.webContents.send('excel-file-selected', filePath);
                    win?.webContents.send('set-single-file-mode', filePath);
                  };
                  win?.webContents.once('did-finish-load', sendExcelFile);
                  win?.webContents.once('dom-ready', sendExcelFile);
                  setTimeout(sendExcelFile, 1000);
                } else {
                  // For other files, create a workspace with the file
                  const fileDir = filePath.substring(0, filePath.lastIndexOf('/') || filePath.lastIndexOf('\\'));
                  createMainWindow(`/workspace/${encodeURIComponent(fileDir)}`);
                  // Use multiple event listeners to ensure the message is received
                  const sendWorkspaceFile = () => {
                    win?.webContents.send('workspace-file-selected', filePath);
                    win?.webContents.send('set-single-file-mode', filePath);
                  };
                  win?.webContents.once('did-finish-load', sendWorkspaceFile);
                  win?.webContents.once('dom-ready', sendWorkspaceFile);
                  // Also send after a delay to ensure components are mounted
                  setTimeout(sendWorkspaceFile, 1000);
                }
              }
            }
          }
        },
        { type: "separator" },
        { label: "退出", role: "quit" }
      ]
    },
    {
      label: "编辑",
      submenu: [
        { label: "撤销", role: "undo" },
        { label: "重做", role: "redo" },
        { type: "separator" },
        { label: "剪切", role: "cut" },
        { label: "复制", role: "copy" },
        { label: "粘贴", role: "paste" },
        { label: "删除", role: "delete" },
        { label: "全选", role: "selectAll" }
      ]
    },
    {
      label: "显示",
      submenu: [
        { label: "加大", role: "zoomIn" },
        { label: "默认大小", role: "resetZoom" },
        { label: "缩小", role: "zoomOut" },
        { type: "separator" },
        {
          label: fullscreenLabel,
          role: "togglefullscreen"
        }
      ]
    }
  ];

  // Ensure app.name submenu doesn't also have Quit if it's in File menu
  const appNameSubmenu = template[0].submenu as MenuItemConstructorOptions[] | undefined;
  if (Array.isArray(appNameSubmenu)) {
    const quitItemIndex = appNameSubmenu.findIndex(item => item.role === 'quit');
    const fileSubMenu = template[1].submenu as MenuItemConstructorOptions[] | undefined;
    if (quitItemIndex > -1 && Array.isArray(fileSubMenu) && fileSubMenu.find(item => item.role === 'quit')) {
      appNameSubmenu.splice(quitItemIndex, 1);
    }
  }
  return template;
};

// New window example arg: new windows url
ipcMain.handle("open-win", (_, arg) => {
  const childWindow = new BrowserWindow({
    webPreferences: {
      preload,
      nodeIntegration: false, // Then these should also be updated
      contextIsolation: true
    }
  });

  if (process.env.VITE_DEV_SERVER_URL) {
    childWindow.loadURL(`${url}#${arg}`);
  } else {
    childWindow.loadFile(indexHtml, { hash: arg });
  }
});

// // 在文件末尾添加以下 IPC 事件监听器
// ipcMain.handle('select-directory', async () => {
//   const result = await dialog.showOpenDialog({
//     properties: ['openDirectory']
//   })
//   return result.filePaths[0]
// })

// ipcMain.handle('create-directory', (_, path: string) => {
//   if (!existsSync(path)) {
//     mkdirSync(path, { recursive: true })
//     return true
//   }
//   return false
// })

// ipcMain.handle('read-file', (_, filePath: string) => {
//   return readFileSync(filePath, 'utf-8')
// })

// ipcMain.handle('write-file', (_, filePath: string, content: string) => {
//   writeFileSync(filePath, content)
//   return true
// })

// ipcMain.handle('read-directory', (_, dirPath: string) => {
//   try {
//     return readdirSync(dirPath).map(file => {
//       const fullPath = join(dirPath, file)
//       const stats: Stats = statSync(fullPath)
//       return {
//         name: file,
//         path: fullPath,
//         isDirectory: stats.isDirectory(),
//         size: stats.size,
//         modified: stats.mtime
//       }
//     })
//   } catch (error) {
//     console.error('Error reading directory:', error)
//     return []
//   }
// })

// 在文件顶部导入所需模块
import fs from 'fs';
import path from 'path';
import { createRequire } from 'node:module';
import { RabbitMQNotificationService } from '../services/rabbitmq';

// Create require function for dynamic imports
const require = createRequire(import.meta.url);

// 在文件末尾（ipcMain.handle("open-win"...)之后）添加以下代码

// 选择目录对话框
ipcMain.handle('dialog:openDirectory', async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openDirectory']
  });
  return result.filePaths[0]; // 返回选择的第一个路径
});

// 选择文件对话框
ipcMain.handle('dialog:openFile', async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openFile'],
    // You can add filters, e.g., for specific file types
    // filters: [
    //   { name: 'Text Files', extensions: ['txt', 'md'] },
    //   { name: 'All Files', extensions: ['*'] }
    // ]
  });
  if (result.canceled || result.filePaths.length === 0) {
    return null; // Or handle as preferred
  }
  return result.filePaths[0]; // 返回选择的第一个路径
});

// 读取目录内容
ipcMain.handle('fs:readDirectory', async (_, dirPath: string) => {
  const files = await fs.promises.readdir(dirPath, { withFileTypes: true });
  return files.map(dirent => ({
    name: dirent.name,
    isDirectory: dirent.isDirectory(),
    path: path.join(dirPath, dirent.name)
  }));
});

// 创建新目录
ipcMain.handle('fs:createDirectory', async (_, targetPath: string) => {
  await fs.promises.mkdir(targetPath, { recursive: true });
  return { success: true };
});

// 创建新文件
ipcMain.handle('fs:createFile', async (_, filePath: string) => {
  await fs.promises.writeFile(filePath, '');
  return { success: true };
});

// 删除文件/目录
ipcMain.handle('fs:deletePath', async (_, targetPath: string) => {
  const stats = await fs.promises.stat(targetPath);
  if (stats.isDirectory()) {
    await fs.promises.rmdir(targetPath, { recursive: true });
  } else {
    await fs.promises.unlink(targetPath);
  }
  return { success: true };
});

// 检测文件类型
const getFileType = (filePath: string): { type: string; category: string; supported: boolean } => {
  const ext = path.extname(filePath).toLowerCase();

  // 文本文件
  const textExtensions = ['.txt', '.md', '.json', '.xml', '.html', '.css', '.js', '.ts', '.vue', '.py', '.java', '.cpp', '.c', '.h', '.sql', '.log', '.ini', '.cfg', '.conf', '.yaml', '.yml'];

  // Office 文档
  const officeExtensions = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'];

  // PDF 文档
  const pdfExtensions = ['.pdf'];

  // 图片文件
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.ico'];

  // 压缩文件
  const archiveExtensions = ['.zip', '.rar', '.7z', '.tar', '.gz'];

  // 可执行文件
  const executableExtensions = ['.exe', '.msi', '.dmg', '.app', '.deb', '.rpm'];

  // 音视频文件
  const mediaExtensions = ['.mp3', '.mp4', '.avi', '.mov', '.wav', '.flac'];

  if (textExtensions.includes(ext)) {
    return { type: ext, category: 'text', supported: true };
  } else if (officeExtensions.includes(ext)) {
    return { type: ext, category: 'office', supported: true };
  } else if (pdfExtensions.includes(ext)) {
    return { type: ext, category: 'pdf', supported: true };
  } else if (imageExtensions.includes(ext)) {
    return { type: ext, category: 'image', supported: true };
  } else if (archiveExtensions.includes(ext)) {
    return { type: ext, category: 'archive', supported: false };
  } else if (executableExtensions.includes(ext)) {
    return { type: ext, category: 'executable', supported: false };
  } else if (mediaExtensions.includes(ext)) {
    return { type: ext, category: 'media', supported: false };
  } else {
    return { type: ext, category: 'unknown', supported: false };
  }
};

// 读取文件内容
ipcMain.handle('fs:readFile', async (_, filePath: string) => {
  try {
    const content = await fs.promises.readFile(filePath, 'utf-8');
    return content;
  } catch (error) {
    console.error('Error reading file:', error);
    throw error;
  }
});

// 检测文件类型和读取内容
ipcMain.handle('fs:readFileWithType', async (_, filePath: string) => {
  try {
    const fileInfo = getFileType(filePath);

    if (!fileInfo.supported) {
      return {
        success: false,
        fileInfo,
        error: `不支持的文件类型: ${fileInfo.type}`,
        message: getUnsupportedMessage(fileInfo)
      };
    }

    let content = '';
    let imageData = null;

    if (fileInfo.category === 'text') {
      // 直接读取文本文件
      content = await fs.promises.readFile(filePath, 'utf-8');
    } else if (fileInfo.category === 'office') {
      // Office 文档需要特殊处理
      if (fileInfo.type === '.docx') {
        content = await extractDocxText(filePath);
      } else if (fileInfo.type === '.doc') {
        content = '暂不支持 .doc 格式，请转换为 .docx 格式';
      } else {
        content = `不支持的Office 文档 (${fileInfo.type})，请使用Office工具进行编辑`;
      }
    } else if (fileInfo.category === 'pdf') {
      content = 'PDF 文档，暂不支持文本提取';
    } else if (fileInfo.category === 'image') {
      // 读取图像文件为 base64
      const imageBuffer = await fs.promises.readFile(filePath);
      const base64Data = imageBuffer.toString('base64');
      const mimeType = getMimeType(fileInfo.type);
      imageData = `data:${mimeType};base64,${base64Data}`;
      content = ''; // 图像文件不需要文本内容
    }

    return {
      success: true,
      fileInfo,
      content,
      imageData
    };
  } catch (error: any) {
    console.error('Error reading file with type:', error);
    return {
      success: false,
      fileInfo: getFileType(filePath),
      error: error?.message || 'Unknown error',
      message: '读取文件时发生错误'
    };
  }
});

// 获取图像文件的 MIME 类型
const getMimeType = (extension: string): string => {
  const mimeTypes: Record<string, string> = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.bmp': 'image/bmp',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.webp': 'image/webp'
  };
  return mimeTypes[extension.toLowerCase()] || 'image/jpeg';
};

// 获取不支持文件类型的提示信息
const getUnsupportedMessage = (fileInfo: { type: string; category: string }) => {
  switch (fileInfo.category) {
    case 'image':
      return '图片文件不支持文本编辑，请使用图片查看器打开';
    case 'archive':
      return '压缩文件不支持直接编辑，请先解压缩';
    case 'executable':
      return '可执行文件不支持编辑';
    case 'media':
      return '音视频文件不支持文本编辑，请使用媒体播放器打开';
    default:
      return '不支持的文件类型，无法在文本编辑器中打开';
  }
};

// 提取 DOCX 文档的文本内容
const extractDocxText = async (filePath: string): Promise<string> => {
  try {
    // Try to use mammoth if available
    try {
      const mammoth = require('mammoth');
      const result = await mammoth.extractRawText({ path: filePath });
      return result.value || '无法提取文档内容';
    } catch (mammothError) {
      console.log('Mammoth not available, using fallback method');

      // Fallback: Try to read as zip and extract document.xml
      try {
        const AdmZip = require('adm-zip');
        const zip = new AdmZip(filePath);
        const documentXml = zip.readAsText('word/document.xml');

        if (documentXml) {
          // Simple XML text extraction (removes tags)
          const textContent = documentXml
            .replace(/<[^>]*>/g, ' ')  // Remove XML tags
            .replace(/\s+/g, ' ')      // Normalize whitespace
            .trim();

          return textContent || '文档内容为空';
        }
      } catch (zipError: any) {
        console.log('ZIP extraction failed:', zipError?.message || 'Unknown error');
      }

      return `Word 文档预览\n\n文件路径: ${filePath}\n\n注意：无法提取文档内容，请使用 Microsoft Word 或其他兼容软件打开此文件。\n\n要完整支持 Word 文档，请安装 mammoth 库：npm install mammoth`;
    }
  } catch (error: any) {
    console.error('Error extracting DOCX text:', error);
    return `Word 文档预览\n\n文件路径: ${filePath}\n\n错误：无法读取文档内容 - ${error?.message || 'Unknown error'}`;
  }
};

// 读取文件内容为Buffer (用于Excel等二进制文件)
ipcMain.handle('fs:readFileBuffer', async (_, filePath: string) => {
  try {
    const buffer = await fs.promises.readFile(filePath);
    return buffer;
  } catch (error) {
    console.error('Error reading file buffer:', error);
    throw error;
  }
});

// 写入文件内容
ipcMain.handle('fs:writeFile', async (_, filePath: string, content: string) => {
  try {
    await fs.promises.writeFile(filePath, content, 'utf-8');
    return { success: true };
  } catch (error) {
    console.error('Error writing file:', error);
    throw error;
  }
});

// Handle workspace file selection communication
ipcMain.on('workspace-file-selected', (event, filePath: string) => {
  // Forward the event to all renderer processes (in case there are multiple windows)
  if (win && !win.isDestroyed()) {
    win.webContents.send('workspace-file-selected', filePath);
  }
});

// Handle Excel file selection from workspace
ipcMain.on('excel-file-selected', (event, filePath: string) => {
  // Forward the event to all renderer processes
  if (win && !win.isDestroyed()) {
    win.webContents.send('excel-file-selected', filePath);
  }
});

// Handle app quit request
ipcMain.on('app-quit', () => {
  app.quit();
});

// 初始化 RabbitMQ 通知服务
async function initializeRabbitMQ() {
  try {
    if (!win) {
      throw new Error('Main window not available for RabbitMQ service');
    }
    rabbitMQService = new RabbitMQNotificationService(win);

    // 从环境变量构建RabbitMQ连接URL
    const host = process.env.VITE_RABBITMQ_HOST;
    const port = process.env.VITE_RABBITMQ_PORT;
    const username = process.env.VITE_RABBITMQ_USERNAME;
    const password = process.env.VITE_RABBITMQ_PASSWORD;
    const vhost = process.env.VITE_RABBITMQ_VHOST;

    const rabbitmqUrl = `amqp://${username}:${password}@${host}:${port}${vhost === '/' ? '' : '/' + vhost}`;
    console.log('Connecting to RabbitMQ at:', `amqp://${username}:***@${host}:${port}${vhost === '/' ? '' : '/' + vhost}`);

    // 尝试连接到 RabbitMQ
    const connected = await rabbitMQService.connect(rabbitmqUrl);

    if (connected) {
      console.log('RabbitMQ notification service initialized successfully');

      // 通知前端 RabbitMQ 已连接
      if (win && !win.isDestroyed()) {
        win.webContents.send('rabbitmq-connected', { connected: true });
      }
    } else {
      console.warn('Failed to connect to RabbitMQ, notifications may not work');

      // 通知前端 RabbitMQ 连接失败
      if (win && !win.isDestroyed()) {
        win.webContents.send('rabbitmq-connected', {
          connected: false,
          error: 'Failed to connect to RabbitMQ server'
        });
      }
    }
  } catch (error) {
    console.error('Error initializing RabbitMQ notification service:', error);

    // 通知前端初始化失败
    if (win && !win.isDestroyed()) {
      win.webContents.send('rabbitmq-connected', {
        connected: false,
        error: (error as Error).message || 'Unknown error'
      });
    }
  }
}

// RabbitMQ 通知相关的 IPC 处理程序

// 获取 RabbitMQ 连接状态
ipcMain.handle('rabbitmq:get-connection-status', async () => {
  try {
    if (!rabbitMQService) {
      return {
        connected: false,
        error: 'RabbitMQ notification service not initialized'
      };
    }

    const connectionInfo = rabbitMQService.getConnectionInfo();

    return {
      connected: rabbitMQService.isConnectionActive(),
      ...connectionInfo
    };
  } catch (error) {
    console.error('Error getting connection status:', error);
    return {
      connected: false,
      error: (error as Error).message || 'Unknown error'
    };
  }
});

// 测试通知功能（开发用）
ipcMain.handle('rabbitmq:test-notification', async () => {
  try {
    if (!rabbitMQService) {
      throw new Error('RabbitMQ notification service not available');
    }

    await rabbitMQService.testNotification();

    return {
      success: true,
      message: '测试通知已发送'
    };
  } catch (error) {
    console.error('Error sending test notification:', error);
    return {
      success: false,
      error: (error as Error).message || 'Unknown error'
    };
  }
});

// 应用退出时清理 RabbitMQ 连接
app.on('before-quit', async () => {
  if (rabbitMQService) {
    console.log('Disconnecting from RabbitMQ...');
    await rabbitMQService.disconnect();
  }
});