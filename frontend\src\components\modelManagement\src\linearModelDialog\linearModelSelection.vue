<template>
  <el-card shadow="never">
    <el-table :data="tableData" :max-height="280" style="width: 100%" size="small">
      <!-- 变量选择列 -->
      <el-table-column label="变量" width="80" align="center">
        <template #default="scope">
          <el-checkbox v-model="scope.row.isSelected" @change="handleVariableSelect(scope.row)" />
        </template>
      </el-table-column>

      <!-- 变量名称列 -->
      <el-table-column prop="name" label="变量名称" />

      <!-- 序号变量列 -->
      <el-table-column label="序号变量" width="80" align="center">
        <template #default="scope">
          <el-checkbox v-model="scope.row.isIndex" :disabled="!scope.row.isSelected"
            @change="handleTypeSelect(scope.row, 'isIndex')" />
        </template>
      </el-table-column>

      <!-- 目标变量列 -->
      <el-table-column label="目标变量" width="80" align="center">
        <template #default="scope">
          <el-checkbox v-model="scope.row.isTarget" :disabled="!scope.row.isSelected"
            @change="handleTypeSelect(scope.row, 'isTarget')" />
        </template>
      </el-table-column>

      <!-- 自变量列 -->
      <el-table-column label="自变量" width="80" align="center">
        <template #default="scope">
          <el-checkbox v-model="scope.row.isIndependent" :disabled="!scope.row.isSelected"
            @change="handleTypeSelect(scope.row, 'isIndependent')" />
        </template>
      </el-table-column>

      <!-- 注释变量列 -->
      <el-table-column label="注释变量" width="80" align="center">
        <template #default="scope">
          <el-checkbox v-model="scope.row.isNote" :disabled="!scope.row.isSelected"
            @change="handleTypeSelect(scope.row, 'isNote')" />
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";

interface VariableRow {
  name: string;
  isSelected: boolean;
  isIndependent: boolean;
  isTarget: boolean;
  isNote: boolean;
  isIndex: boolean;
}

// 更新选择结果
const updateSelection = () => {
  const result = {
    features: tableData.value
      .filter((row) => row.isIndependent)
      .map((row) => row.name),
    target: tableData.value
      .filter((row) => row.isTarget)
      .map((row) => row.name),
    deletes: tableData.value.filter((row) => row.isNote).map((row) => row.name),
    index: tableData.value.filter((row) => row.isIndex).map((row) => row.name),
    all: tableData.value.map((row) => row.name),
  };
  emit("update:selection", result);
};

const props = defineProps<{
  headers: string[];
}>();

const emit = defineEmits<{
  (
    e: "update:selection",
    value: {
      features: string[];
      target: string[];
      deletes: string[];
      index: string[];
      all: string[];
    },
  ): void;
}>();

const tableData = ref<VariableRow[]>([]);

// 监听headers变化，初始化数据
watch(
  () => props.headers,
  (headers) => {
    if (headers?.length) {
      tableData.value = headers.map((header, index) => {
        const row: VariableRow = {
          name: header,
          isSelected: true, // 默认全选
          isIndependent: false,
          isTarget: false,
          isNote: false,
          isIndex: false,
        };

        // 设置默认选择
        if (index === 0) {
          // 第一行设置为序号变量
          row.isIndex = true;
        } else if (index === 1) {
          // 第二行设置为目标变量
          row.isTarget = true;
        } else {
          // 其他行设置为自变量
          row.isIndependent = true;
        }

        return row;
      });

      // 立即触发选择更新
      updateSelection();
    }
  },
  { immediate: true },
);

// 处理变量选择
const handleVariableSelect = (row: VariableRow) => {
  if (!row.isSelected) {
    row.isIndependent = false;
    row.isTarget = false;
    row.isNote = false;
    row.isIndex = false;
  }
  updateSelection();
};

// 处理类型选择
const handleTypeSelect = (row: VariableRow, type: keyof VariableRow) => {
  if (row[type]) {
    // 如果当前类型被选中，清除该行其他类型的选择
    const types = ["isIndependent", "isTarget", "isNote", "isIndex"] as const;
    types.forEach((t) => {
      if (t !== type) {
        (row[t as keyof VariableRow] as boolean) = false;
      }
    });
  }
  updateSelection();
};
</script>

<style scoped>
:deep(.el-card__body) {
  padding: 10px;
}
</style>
