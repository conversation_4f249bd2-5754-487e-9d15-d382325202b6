export interface TableColumn {
  data: string;
  title?: string;
  type?:
    | "text"
    | "numeric"
    | "date"
    | "checkbox"
    | "select"
    | "dropdown"
    | "autocomplete";
  width?: number;
  readOnly?: boolean;
  settings?: Record<string, any>;
}

export interface DataTableProps {
  data?: any[][];
  columns?: TableColumn[];
  height?: string | number;
  loading?: boolean;
  emptyText?: string;
  emptyImage?: string;
  autoResize?: boolean;
  settings?: Record<string, any>;
}

export interface DataTableEmits {
  ready: [instance: TableInstance];
  change: [changes: any];
  selection: [selection: any];
  "empty-action": [];
}

export interface TableInstance {
  hotInstance: any;
  getData: () => any[][];
  getColumns: () => TableColumn[];
  updateData: (data: any[][]) => void;
  updateColumns: (columns: TableColumn[]) => void;
  exportData: (filename?: string) => void;
  importData: (file: File) => Promise<void>;
  refresh: () => void;
  destroy: () => void;
}
