<template>
  <el-dialog
    :model-value="modelValue"
    :title="title"
    width="70%"
    :class="dialogClass"
    top="5vh"
    destroy-on-close
    @update:model-value="(val) => !val && $emit('close')"
    @close="$emit('close')"
    @opened="handleDialogOpened"
  >
    <el-form :model="modelConfig" label-position="top" label-width="auto">
      <!-- 模型简介（包含模型名称） -->
      <el-form-item label="模型简介" class="model-intro-form-item">
        <ModelIntroduction 
          :model-type="modelType" 
          :model-display-name="modelDisplayName" 
        />
      </el-form-item>

      <el-tabs stretch>
        <!-- 变量选择标签页 -->
        <el-tab-pane label="变量选择">
          <el-form-item label="变量选择" class="hidden-label">
            <slot 
              name="variable-selection"
              :headers="tableDataStore.currentTableHeader"
              :model-type="modelType"
              :on-update-selection="handleVariableSelection"
            />
          </el-form-item>
        </el-tab-pane>

        <!-- 参数设置标签页 -->
        <el-tab-pane label="参数设置">
          <el-form-item label="参数设置" class="hidden-label">
            <slot 
              name="params-setting"
              :algorithm-name="modelType"
              :on-update-params="handleParamsUpdate"
            />
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('close')">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm" 
          :loading="isBuilding"
        >
          {{ isBuilding ? '构建中...' : '确定' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import { useTableDataStore } from "@/store/modules/tableData";
import ModelIntroduction from "./ModelIntroduction.vue";
import { ElMessageBox, ElMessage } from "element-plus";
import type { ModelConfig, ModelAlgorithm, ModelMetaEvaluation } from "@/types/models";

interface Props {
  modelValue: boolean;
  title: string;
  dialogClass: string;
  modelType: string;
  modelDisplayName: string;
  isBuilding?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isBuilding: false
});

const emit = defineEmits<{
  close: [];
  confirm: [config: ModelConfig];
  dialogOpened: [];
}>();

const tableDataStore = useTableDataStore();

const modelConfig = reactive<ModelConfig>({
  dataset: {
    data: [[]],
    meta: {
      headers: {
        index: [],
        target: [],
        features: [],
        deletes: [],
        all: [],
      },
    },
  },
  model: {
    algorithm: {
      name: '',
      params: {},
    },
    meta: {
      evaluation: {
        cv: undefined,
        loocv: false,
        test: undefined,
      },
      headers: {
        index: [],
        target: [],
        features: [],
        all: [],
      },
    },
  },
});

// 监听模型类型变化
watch(
  () => props.modelType,
  (newType: string) => {
    if (newType) {
      modelConfig.model.algorithm.name = newType;
    }
  },
  { immediate: true }
);

// 处理dialog打开事件
const handleDialogOpened = () => {
  console.log("BaseModelDialog opened, emitting dialogOpened event");
  emit('dialogOpened');
};

// 处理变量选择
const handleVariableSelection = (selection: typeof modelConfig.dataset.meta.headers) => {
  modelConfig.dataset.meta.headers = selection;
};

// 处理参数更新
const handleParamsUpdate = (params: {
  algorithm: ModelAlgorithm;
  evaluation: ModelMetaEvaluation;
}) => {
  modelConfig.model.algorithm = params.algorithm;
  modelConfig.model.meta.evaluation = params.evaluation;
};

// 验证变量选择
const validateVariables = async (variables: typeof modelConfig.model.meta.headers) => {
  const errors: string[] = [];

  // 检查序号变量
  if (variables.index.length > 1) {
    errors.push(
      `<li>发现多个序号变量：<span style="color: skyblue">${variables.index.join("、")}</span>，请只保留一个序号变量</li>`
    );
  }

  // 检查目标变量
  if (variables.target.length !== 1) {
    errors.push(
      `<li>目标变量不合法，当前选择：<span style="color: skyblue">${variables.target.length ? variables.target.join("、") : "空"}</span>，请只保留一个目标变量</li>`
    );
  }

  // 检查自变量
  if (!variables.features || variables.features.length === 0) {
    errors.push("<li>请至少选择一个自变量</li>");
  }

  // 如果有错误，显示所有错误信息
  if (errors.length > 0) {
    await ElMessageBox.alert(errors.join(""), "变量选择错误", {
      type: "error",
      confirmButtonText: "确定",
      dangerouslyUseHTMLString: true,
    });
    return false;
  }

  return true;
};

// 验证数据
const validateData = async (data: any[][], meta: {
  headers: string[];
  variables: {
    index: string[];
    target: string[];
    features: string[];
  };
}) => {
  const errors: string[] = [];
  const { headers, variables } = meta;

  const isNumeric = (value: any): boolean => {
    if (typeof value === "number") return true;
    if (typeof value === "string") {
      return value.trim() !== "" && !isNaN(Number(value));
    }
    return false;
  };

  // 获取需要检查的列的索引
  const checkColumns = new Map();
  variables.features.forEach((col: string) => {
    checkColumns.set(headers.indexOf(col), "自变量");
  });
  checkColumns.set(headers.indexOf(variables.target[0]), "目标变量");
  if (variables.index.length) {
    checkColumns.set(headers.indexOf(variables.index[0]), "序号变量");
  }

  // 检查每一行数据
  data.forEach((row, rowIndex) => {
    checkColumns.forEach((type, colIndex) => {
      // 检查空值
      if (
        row[colIndex] === null ||
        row[colIndex] === undefined ||
        row[colIndex] === ""
      ) {
        errors.push(
          `<li>第 ${rowIndex + 1} 行的${type}「${headers[colIndex]}」存在空值,建议进行数据预处理</li>`
        );
        return;
      } else if (type !== "序号变量" && !isNumeric(row[colIndex])) {
        errors.push(
          `<li>第 ${rowIndex + 1} 行的${type}「${headers[colIndex]}」值「${row[colIndex]}」不是数字</li>`
        );
      }
    });
  });

  // 如果有错误，显示所有错误信息
  if (errors.length > 0) {
    await ElMessageBox.alert(
      `<div style="max-height: 300px; overflow-y: auto;">
        <p>发现以下问题：</p>
        <ul>${errors.join("")}</ul>
      </div>`,
      "数据验证错误",
      {
        type: "error",
        confirmButtonText: "确定",
        dangerouslyUseHTMLString: true,
      }
    );
    return false;
  }

  return true;
};

// 处理确认
const handleConfirm = async () => {
  const data = tableDataStore.currentTableData;
  const headers = tableDataStore.currentTableHeader;
  const noteColumns = modelConfig.dataset.meta.headers.deletes;

  // 准备过滤后的变量用于验证
  const filteredFeatures = modelConfig.dataset.meta.headers.features.filter(
    (header: any) => !noteColumns.includes(header)
  );

  // 创建用于验证的变量对象
  const variablesToValidate = {
    index: modelConfig.dataset.meta.headers.index,
    target: modelConfig.dataset.meta.headers.target,
    features: filteredFeatures,
    all: headers.filter((header: any) => !noteColumns.includes(header))
  };

  // 验证变量选择（使用过滤后的features）
  if (!(await validateVariables(variablesToValidate))) {
    return;
  }

  modelConfig.dataset.data = tableDataStore.currentTableData;
  modelConfig.model.meta.headers.index = modelConfig.dataset.meta.headers.index;
  modelConfig.model.meta.headers.target = modelConfig.dataset.meta.headers.target;
  modelConfig.model.meta.headers.features = filteredFeatures;
  modelConfig.model.meta.headers.all = headers.filter(
    (header: any) => !noteColumns.includes(header)
  );

  // 在发送数据前进行数据验证
  if (!(await validateData(data, {
    headers: modelConfig.model.meta.headers.all,
    variables: {
      index: modelConfig.model.meta.headers.index,
      target: modelConfig.model.meta.headers.target,
      features: modelConfig.model.meta.headers.features,
    },
  }))) {
    return;
  }

  // 发送给父组件
  emit("confirm", modelConfig);
};

// 暴露modelConfig给父组件使用
defineExpose({
  modelConfig
});
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.model-intro-form-item {
  margin-bottom: 20px;
}

:deep(.el-form-item__content) {
  flex-wrap: nowrap;
}

:deep(.el-tabs__nav-scroll) {
  width: 50%;
  margin: 0 auto;
}

:deep(.model-intro-form-item .el-form-item__content) {
  width: 100%;
}
</style>

<style>
/* 全局样式 - 基础模型对话框高度限制 */
.el-overlay .ml-model-dialog.el-dialog__wrapper .el-dialog {
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
}

.el-overlay .ml-model-dialog.el-dialog__wrapper .el-dialog .el-dialog__body {
  flex: 1 !important;
  overflow-y: auto !important;
  max-height: calc(90vh - 120px) !important;
  padding-right: 8px !important;
}

/* 自定义滚动条样式 */
.el-overlay .ml-model-dialog.el-dialog__wrapper .el-dialog .el-dialog__body::-webkit-scrollbar {
  width: 6px;
}

.el-overlay .ml-model-dialog.el-dialog__wrapper .el-dialog .el-dialog__body::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
  border-radius: 3px;
}

.el-overlay .ml-model-dialog.el-dialog__wrapper .el-dialog .el-dialog__body::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.el-overlay .ml-model-dialog.el-dialog__wrapper .el-dialog .el-dialog__body::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 确保对话框头部和底部固定 */
.el-overlay .ml-model-dialog.el-dialog__wrapper .el-dialog .el-dialog__header {
  flex-shrink: 0 !important;
}

.el-overlay .ml-model-dialog.el-dialog__wrapper .el-dialog .el-dialog__footer {
  flex-shrink: 0 !important;
}

/* 备用方案 - 使用更通用的选择器 */
.el-dialog.ml-model-dialog {
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
}

.el-dialog.ml-model-dialog .el-dialog__body {
  flex: 1 !important;
  overflow-y: auto !important;
  max-height: calc(90vh - 120px) !important;
  padding-right: 8px !important;
}
</style>
