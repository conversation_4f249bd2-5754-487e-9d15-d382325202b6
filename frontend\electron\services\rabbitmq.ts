import amqp, { Connection, Channel, ConsumeMessage } from 'amqplib';
import { BrowserWindow, Notification } from 'electron';
import { nodeModelConfigManager, getNodeModelDisplayName } from '../utils/modelConfigLoader';

export interface MLModelNotification {
  taskId: string;
  modelType: string;
  status: 'completed' | 'failed';
  message: string;
  result?: {
    accuracy?: number;
    metrics?: Record<string, any>;
    modelPath?: string;
    resultUrl?: string; // 用于打开结果页面的URL
  };
  error?: string;
  timestamp: string;
}

export class RabbitMQNotificationService {
  private connection: Connection | null = null;
  private channel: Channel | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000; // 5 seconds
  private mainWindow: BrowserWindow | null = null;

  // Queue names - 只监听通知队列
  private readonly ML_MODEL_NOTIFICATION_QUEUE = 'ml_model_notifications';

  constructor(mainWindow?: BrowserWindow) {
    this.mainWindow = mainWindow || null;
    // 初始化模型配置管理器
    this.initializeModelConfig();
  }

  // 初始化模型配置管理器
  private async initializeModelConfig() {
    try {
      await nodeModelConfigManager.initialize();
      console.log('RabbitMQ: Model config manager initialized');
    } catch (error) {
      console.error('RabbitMQ: Failed to initialize model config manager:', error);
    }
  }

  // 获取模型显示名称（带降级处理）
  private getModelDisplayName(modelType: string): string {
    try {
      return getNodeModelDisplayName(modelType);
    } catch (error) {
      console.warn('Failed to get model display name, using fallback:', error);
      // 降级到默认映射
      const defaultMap: Record<string, string> = {
        'DecisionTreeRegressor': '决策树',
        'RandomForestRegressor': '随机森林',
        'XGBoost': 'XGBoost',
        'GradientBoostingRegressor': '梯度提升回归',
        'SVR': '支持向量机',
        'MLPRegressor': '人工神经网络'
      };
      return defaultMap[modelType] || modelType;
    }
  }

  // 设置主窗口引用
  setMainWindow(window: BrowserWindow) {
    this.mainWindow = window;
  }

  // 连接到 RabbitMQ
  async connect(url: string = 'amqp://localhost'): Promise<boolean> {
    try {
      console.log('Connecting to RabbitMQ...');
      this.connection = await amqp.connect(url);
      this.channel = await this.connection.createChannel();
      
      // 设置连接事件监听
      this.connection.on('error', this.handleConnectionError.bind(this));
      this.connection.on('close', this.handleConnectionClose.bind(this));

      // 声明队列
      await this.setupQueues();
      
      // 开始监听通知
      await this.startNotificationListener();
      
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      console.log('RabbitMQ connected successfully');
      return true;
    } catch (error) {
      console.error('Failed to connect to RabbitMQ:', error);
      this.isConnected = false;
      
      // 尝试重连
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        setTimeout(() => this.connect(url), this.reconnectDelay);
      }
      
      return false;
    }
  }

  // 设置队列
  private async setupQueues() {
    if (!this.channel) throw new Error('Channel not available');

    // 声明通知队列（持久化）
    await this.channel.assertQueue(this.ML_MODEL_NOTIFICATION_QUEUE, {
      durable: true,
      arguments: {
        'x-message-ttl': 86400000, // 24 hours TTL
      }
    });

    console.log('RabbitMQ notification queue setup completed');
  }

  // 开始监听通知
  private async startNotificationListener() {
    if (!this.channel) return;

    // 监听模型完成通知
    await this.channel.consume(this.ML_MODEL_NOTIFICATION_QUEUE, (msg: ConsumeMessage | null) => {
      if (msg) {
        try {
          const notification: MLModelNotification = JSON.parse(msg.content.toString());
          this.handleModelNotification(notification);
          this.channel?.ack(msg);
        } catch (error) {
          console.error('Error processing ML model notification:', error);
          this.channel?.nack(msg, false, false);
        }
      }
    });

    console.log('ML model notification listener started');
  }

  // 处理模型完成通知
  private handleModelNotification(notification: MLModelNotification) {
    console.log('Received ML model notification:', notification);

    // 显示系统通知
    this.showSystemNotification(notification);

    // 通知前端
    this.notifyFrontend('ml-model-notification', notification);
  }

  // 显示系统通知
  private showSystemNotification(notification: MLModelNotification) {
    try {
      // 使用动态模型配置管理器获取显示名称，如果失败则使用默认名称
      const modelName = this.getModelDisplayName(notification.modelType);

      let title: string;
      let body: string;

      if (notification.status === 'completed') {
        title = `${modelName}模型构建完成`;
        body = notification.message || '模型训练已完成，点击查看结果';

        if (notification.result?.accuracy) {
          body += `\n准确率: ${(notification.result.accuracy * 100).toFixed(2)}%`;
        }
      } else {
        title = `${modelName}模型构建失败`;
        body = notification.error || notification.message || '模型训练过程中出现错误';
      }

      const systemNotification = new Notification({
        title,
        body,
        icon: this.getNotificationIcon(notification.status),
        urgency: notification.status === 'failed' ? 'critical' : 'normal',
        timeoutType: 'never', // 不自动消失
        actions: notification.status === 'completed' ? [
          {
            type: 'button',
            text: '查看结果'
          }
        ] : []
      });

      // 标记通知是否已被关闭
      let notificationClosed = false;

      // 处理通知关闭事件（必须在click事件之前注册）
      systemNotification.on('close', () => {
        console.log('Notification closed by user');
        notificationClosed = true;
      });

      // 处理通知点击事件（排除关闭按钮）
      systemNotification.on('click', () => {
        console.log('Notification clicked, closed status:', notificationClosed);

        // 使用短暂延迟来确保close事件已经触发
        setTimeout(() => {
          if (!notificationClosed) {
            console.log('Processing notification click');
            this.handleNotificationClick(notification);
          } else {
            console.log('Ignoring click - notification was closed');
          }
        }, 50);
      });

      // 处理操作按钮点击
      systemNotification.on('action', (_event, index) => {
        if (index === 0) { // 查看结果按钮
          console.log('Action button clicked');
          this.handleNotificationClick(notification);
        }
      });

      // 处理通知失败事件
      systemNotification.on('failed', (error) => {
        console.error('Notification failed:', error);
      });

      systemNotification.show();

    } catch (error) {
      console.error('Error showing system notification:', error);
    }
  }

  // 获取通知图标
  private getNotificationIcon(_status: string): string | undefined {
    // 这里可以根据状态返回不同的图标路径
    // 暂时返回 undefined 使用默认图标
    return undefined;
  }

  // 处理通知点击
  private handleNotificationClick(notification: MLModelNotification) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      // 聚焦主窗口
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.focus();

      // 通知前端打开结果页面
      this.mainWindow.webContents.send('open-model-result', {
        taskId: notification.taskId,
        modelType: notification.modelType,
        result: notification.result,
        resultUrl: notification.result?.resultUrl
      });
    }
  }

  // 通知前端
  private notifyFrontend(event: string, data: any) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(event, data);
    }
  }

  // 测试通知功能（开发用）
  async testNotification() {
    const testNotification: MLModelNotification = {
      taskId: 'test-' + Date.now(),
      modelType: 'DecisionTree',
      status: 'completed',
      message: '这是一个测试通知',
      result: {
        accuracy: 0.95,
        metrics: { precision: 0.94, recall: 0.96 }
      },
      timestamp: new Date().toISOString()
    };

    this.handleModelNotification(testNotification);
  }

  // 处理连接错误
  private handleConnectionError(error: Error) {
    console.error('RabbitMQ connection error:', error);
    this.isConnected = false;
  }

  // 处理连接关闭
  private handleConnectionClose() {
    console.log('RabbitMQ connection closed');
    this.isConnected = false;
    
    // 尝试重连
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      setTimeout(() => this.connect(), this.reconnectDelay);
    }
  }

  // 断开连接
  async disconnect() {
    try {
      if (this.channel) {
        await this.channel.close();
        this.channel = null;
      }
      
      if (this.connection) {
        await this.connection.close();
        this.connection = null;
      }
      
      this.isConnected = false;
      console.log('RabbitMQ disconnected');
    } catch (error) {
      console.error('Error disconnecting from RabbitMQ:', error);
    }
  }

  // 检查连接状态
  isConnectionActive(): boolean {
    return this.isConnected && this.connection !== null && this.channel !== null;
  }

  // 获取连接信息
  getConnectionInfo() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts
    };
  }
}
