<template>
  <div class="model-selector" :class="{ disabled: props.disabled }">
    <el-button 
      :disabled="props.disabled" 
      @click="toggleDropdown"
      class="selector-button"
      :class="{ active: isDropdownVisible }"
    >
      <span>回归模型构建</span>
      <el-icon class="arrow-icon" :class="{ rotated: isDropdownVisible }">
        <ArrowDown />
      </el-icon>
    </el-button>
    
    <!-- 自定义下拉面板 -->
    <div
      v-show="isDropdownVisible"
      class="dropdown-panel"
      :style="{
        top: dropdownPosition.top + 'px',
        left: dropdownPosition.left === 'auto' ? 'auto' : dropdownPosition.left + 'px',
        right: dropdownPosition.right === 'auto' ? 'auto' : dropdownPosition.right + 'px'
      }"
      @click.stop
    >
      <div class="model-categories">
        <!-- 左侧分类列表 -->
        <div class="category-list">
          <div 
            v-for="category in modelCategories" 
            :key="category.key"
            class="category-item"
            :class="{ active: activeCategory === category.key }"
            @mouseenter="setActiveCategory(category.key)"
          >
            <span class="category-label">{{ category.label }}</span>
            <el-icon class="category-arrow">
              <ArrowRight />
            </el-icon>
          </div>
        </div>
        
        <!-- 右侧模型列表 -->
        <div class="model-list" v-if="activeCategory">
          <div 
            v-for="model in currentModels" 
            :key="model.value"
            class="model-item"
            @click="selectModel(model)"
          >
            <span class="model-label">{{ model.label }}</span>
            <span class="model-description">{{ model.description }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 遮罩层 -->
    <div 
      v-show="isDropdownVisible" 
      class="dropdown-overlay"
      @click="closeDropdown"
    ></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { ArrowDown, ArrowRight } from '@element-plus/icons-vue';
import { dynamicModelManager, getCategories, type ModelCategory } from "@/utils/dynamicModelLoader";

const props = defineProps<{
  disabled?: boolean;
}>();

defineOptions({
  name: "ModelSelector",
});

const emit = defineEmits<{
  (e: "buildLinearModel", modelType: string): void;
  (e: "buildMLModel", modelType: string): void;
  (e: "buildTreeModel", modelType: string): void;
}>();

// 响应式数据
const isDropdownVisible = ref(false);
const activeCategory = ref<string>('');
const modelCategories = ref<ModelCategory[]>([]);
const isLoading = ref(false);

// 计算当前激活分类的模型列表
const currentModels = computed(() => {
  const category = modelCategories.value.find((cat) => cat.key === activeCategory.value);
  return category ? category.models.map(metadata => ({
    label: metadata.config.displayName,
    value: metadata.id,
    description: metadata.config.description
  })) : [];
});

// 初始化动态模型管理器
const initializeModels = async () => {
  try {
    isLoading.value = true;
    await dynamicModelManager.initialize();
    modelCategories.value = getCategories();
    console.log('Dynamic models loaded:', modelCategories.value);
  } catch (error) {
    console.error('Failed to initialize dynamic models:', error);
  } finally {
    isLoading.value = false;
  }
};

// 方法
// 下拉面板位置
const dropdownPosition = ref({
  top: 0,
  left: 'auto' as string | number,
  right: 'auto' as string | number
});

const toggleDropdown = () => {
  if (props.disabled) return;
  isDropdownVisible.value = !isDropdownVisible.value;
  if (isDropdownVisible.value) {
    if (!activeCategory.value) {
      activeCategory.value = 'linear'; // 默认选中线性模型
    }
    // 计算下拉面板位置
    updateDropdownPosition();
  }
};

const updateDropdownPosition = () => {
  const selector = document.querySelector('.model-selector .selector-button');
  if (selector) {
    const rect = selector.getBoundingClientRect();
    const panelWidth = 420; // 下拉面板宽度，与CSS中的width保持一致
    const windowWidth = window.innerWidth;

    // 检查是否会超出右侧窗口边界
    if (rect.left + panelWidth > windowWidth) {
      // 如果超出，则从右侧对齐
      dropdownPosition.value = {
        top: rect.bottom + 6,
        left: 'auto',
        right: windowWidth - rect.right
      };
    } else {
      // 正常左对齐
      dropdownPosition.value = {
        top: rect.bottom + 6,
        left: rect.left,
        right: 'auto'
      };
    }
  }
};

const closeDropdown = () => {
  isDropdownVisible.value = false;
  activeCategory.value = '';
};

const setActiveCategory = (categoryKey: string) => {
  activeCategory.value = categoryKey;
};

const selectModel = (model: any) => {
  // 根据分类发射不同的事件
  if (activeCategory.value === 'linear') {
    emit('buildLinearModel', model.value);
  } else if (activeCategory.value === 'tree') {
    emit('buildTreeModel', model.value);
  } else if (activeCategory.value === 'ml') {
    emit('buildMLModel', model.value);
  }

  // 选择后自动关闭
  closeDropdown();
};

// 点击外部关闭下拉框
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  const selector = document.querySelector('.model-selector');
  if (selector && !selector.contains(target)) {
    closeDropdown();
  }
};

// 监听窗口大小变化，重新计算位置
const handleResize = () => {
  if (isDropdownVisible.value) {
    updateDropdownPosition();
  }
};

onMounted(async () => {
  document.addEventListener('click', handleClickOutside);
  window.addEventListener('resize', handleResize);

  // 初始化动态模型
  await initializeModels();
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.model-selector {
  position: relative;
  display: inline-block;
}

.model-selector.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.selector-button {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  transition: all 0.3s ease;
}

.selector-button.active {
  background-color: var(--el-color-primary);
  color: white;
  border-color: var(--el-color-primary);
}

.arrow-icon {
  transition: transform 0.3s ease;
}

.arrow-icon.rotated {
  transform: rotate(180deg);
}

.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: transparent;
}

.dropdown-panel {
  position: fixed;
  z-index: 1001;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  width: 420px;
  height: 360px;
  overflow: hidden;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.model-categories {
  display: flex;
  height: 100%;
}

.category-list {
  width: 140px;
  border-right: 1px solid var(--el-border-color-light);
  background-color: var(--el-bg-color-page);
}

.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 18px;
  cursor: pointer;
  transition: all 0.25s ease;
  border-bottom: 1px solid var(--el-border-color-lighter);
  position: relative;
}

.category-item:hover,
.category-item.active {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--el-color-primary);
}

.category-label {
  font-size: 14px;
  font-weight: 500;
}

.category-arrow {
  font-size: 12px;
  opacity: 0.6;
}

.model-list {
  flex: 1;
  padding: 8px 0;
  height: 100%;
  overflow: hidden;
}

.model-item {
  padding: 14px 18px;
  cursor: pointer;
  transition: all 0.25s ease;
  border-bottom: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  margin: 2px 8px;
}

.model-item:last-child {
  border-bottom: none;
}

.model-item:hover {
  background-color: var(--el-color-primary-light-9);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.model-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.model-description {
  display: block;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

/* 深色模式适配 */
html.dark .dropdown-panel {
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
}

html.dark .category-list {
  background-color: var(--el-bg-color-page);
  border-right-color: var(--el-border-color);
}

html.dark .category-item {
  border-bottom-color: var(--el-border-color-lighter);
}

html.dark .category-item:hover,
html.dark .category-item.active {
  background-color: var(--el-color-primary-light-9);
}

html.dark .model-item {
  border-bottom-color: var(--el-border-color-lighter);
}

html.dark .model-item:hover {
  background-color: var(--el-color-primary-light-9);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dropdown-panel {
    width: 320px;
    height: 240px;
  }

  .model-categories {
    flex-direction: column;
  }

  .category-list {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .category-item {
    padding: 10px 14px;
  }

  .model-item {
    padding: 10px 14px;
  }
}

/* 键盘导航支持 */
.category-item:focus,
.model-item:focus {
  outline: 2px solid var(--el-color-primary);
  outline-offset: -2px;
}

/* 加载状态 */
.selector-button.loading {
  pointer-events: none;
  opacity: 0.7;
}

.selector-button.loading .arrow-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
