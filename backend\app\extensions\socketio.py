from flask_socketio import So<PERSON><PERSON>, disconnect
from flask import request, current_app
from flask_jwt_extended import decode_token

# 延迟初始化
socketio = SocketIO(cors_allowed_origins="*", max_http_buffer_size=10 * 1024 * 1024)

# WebSocket 事件处理
@socketio.on('connect')
def handle_connect(auth):
    request_sid = request.sid  # 每个连接的唯一标识
    if auth is None or 'token' not in auth:
        # print('Unauthorized connection')
        disconnect()
        return 
    try:
        decoded_data = decode_token(auth['token'])
        username = decoded_data.get('sub')
        # print(f'User {username} connected: {request_sid}')
        current_app.connected_sids[request_sid] = { 'username': username }  # 可以扩展更多信息
    except Exception as err:
        # print(f'Unauthorized connection: {err}')
        disconnect(request_sid)

@socketio.on('disconnect')
def handle_disconnect():
    request_sid = request.sid
    if request_sid in current_app.connected_sids:
        # print(f'User disconnected: {request_sid} ')
        del current_app.connected_sids[request_sid]
