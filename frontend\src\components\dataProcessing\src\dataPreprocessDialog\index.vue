<template>
  <el-dialog
    :model-value="dialogStore.dialogs.preprocessData"
    title="数据预处理"
    width="600px"
    :destroy-on-close="true"
    @update:model-value="(val) => !val && handleCancel()"
    @close="handleCancel"
  >
    <el-form :model="metaConfig" label-width="120px" size="small">
      <!-- 缺失值处理 -->
      <template v-if="showMissing">
        <el-form-item label="缺失值处理">
          <el-radio-group v-model="metaConfig.algorithm.name">
            <el-radio
              v-for="option in missingOptions"
              :key="option.value"
              :label="option.value"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 当选择统计值填充时 -->
        <el-form-item
          v-if="metaConfig.algorithm.name === 'SimpleImputer'"
          label="填充方式"
        >
          <el-select
            v-model="metaConfig.algorithm.params.strategy"
            placeholder="请选择填充方式"
          >
            <el-option
              v-for="option in statisticOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 固定值参数 -->
        <el-form-item
          v-if="
            metaConfig.algorithm.name === 'SimpleImputer' &&
            metaConfig.algorithm.params.strategy === 'constant'
          "
          label="固定值"
        >
          <el-input
            v-model="metaConfig.algorithm.params.fill_value"
            placeholder="请输入固定值"
          />
        </el-form-item>
      </template>

      <!-- 异常值检测 -->
      <template v-if="showAbnormal">
        <el-form-item label="异常值检测">
          <el-radio-group v-model="metaConfig.algorithm.name">
            <el-radio
              v-for="option in abnormalOptions"
              :key="option.value"
              :label="option.value"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </template>
    </el-form>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button style="margin-left: 8px" type="primary" @click="handleConfirm"
        >确定</el-button
      >
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useDialogStore } from "@/store/modules/dialog";
import { useTableDataStore } from "@/store/modules/tableData";
import { ElMessage } from "element-plus";
import { reactive, computed } from "vue";
import type { PreprocessConfig } from "@/types/preprocess";

// props 接收 dataset 及 processType
const props = defineProps<{
  processType: "missingValue" | "outlier";
}>();
const emit = defineEmits<{
  (e: "confirm", config: PreprocessConfig): void;
}>();

// store
const dialogStore = useDialogStore();
const tableDataStore = useTableDataStore();

// 控制显示
const showMissing = computed(() => props.processType === "missingValue");
const showAbnormal = computed(() => props.processType === "outlier");

const metaConfig = reactive<{
  algorithm: {
    name: string;
    params?: {
      strategy?: string;
      fill_value?: string;
    };
  };
}>({
  algorithm: {
    name: "",
    params: {}, // 注意：默认给空对象，后续根据需要可省略
  },
});

// 选项数据
const missingOptions = [
  { label: "按行删除", value: "DropNA" },
  { label: "统计值填充", value: "SimpleImputer" },
  { label: "向前填充", value: "FFill" },
  { label: "向后填充", value: "BFill" },
  { label: "插值填充", value: "Interpolation" },
];
const statisticOptions = [
  { label: "均值", value: "mean" },
  { label: "中位数", value: "median" },
  { label: "众数", value: "most_frequent" },
  { label: "固定值", value: "constant" },
];
const abnormalOptions = [
  { label: "局部离群因子", value: "LocalOutlierFactor" },
  { label: "孤立森林", value: "IsolationForest" },
  { label: "一类支持向量机", value: "OneClassSVM" },
];

function handleCancel() {
  dialogStore.hideDialog("preprocessData");
}

function handleConfirm() {
  // 校验
  if (!metaConfig.algorithm.name) {
    ElMessage.warning("处理方法不能为空");
    return;
  }
  if (
    showMissing.value &&
    metaConfig.algorithm.name === "SimpleImputer" &&
    !metaConfig.algorithm.params?.strategy
  ) {
    ElMessage.warning("请选择填充策略");
    return;
  }
  if (
    showMissing.value &&
    metaConfig.algorithm.name === "SimpleImputer" &&
    metaConfig.algorithm.params?.strategy === "constant" &&
    metaConfig.algorithm.params?.fill_value === ""
  ) {
    ElMessage.warning("请输入固定值");
    return;
  }

  // 构造完整配置
  const algorithm = showMissing.value
    ? {
        name: metaConfig.algorithm.name,
        params: metaConfig.algorithm.params,
      }
    : {
        name: metaConfig.algorithm.name,
      };

  const config: PreprocessConfig = {
    dataset: {
      meta: {
        headers: tableDataStore.currentTableHeader,
      },
      data: tableDataStore.currentTableData,
    },
    preprocess: {
      algorithm,
    },
  };

  emit("confirm", config);
  dialogStore.hideDialog("preprocessData");
}
</script>
