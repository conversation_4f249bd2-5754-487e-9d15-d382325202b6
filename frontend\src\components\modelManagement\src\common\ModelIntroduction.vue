<template>
  <div class="model-introduction">
    <el-card v-if="modelConfig || isMLModel" shadow="never" class="intro-card">
      <template #header>
        <div class="card-header">
          <div class="model-title">
            <span class="model-name">{{ modelDisplayName || (modelConfig?.displayName) }}</span>
            <el-tag v-if="!isMLModel" type="info" size="small">{{ modelConfig?.name }}</el-tag>
            <el-tag v-else-if="isMLModel" type="info" size="small">{{ mlModelConfig?.name }}</el-tag>
          </div>
          <el-button
            link
            size="small"
            @click="showDetailedIntro = !showDetailedIntro"
            class="expand-button"
          >
            <el-icon class="expand-icon" :class="{ 'expanded': showDetailedIntro }">
              <ArrowDown />
            </el-icon>
            {{ showDetailedIntro ? '收起详情' : '展开详情' }}
          </el-button>
        </div>
      </template>

      <!-- 基本描述 - 始终显示 -->
      <div class="basic-description">
        {{ getBasicDescription() }}
      </div>
      
      <!-- 详细介绍 - 可展开/收起 -->
      <el-collapse-transition>
        <div v-show="showDetailedIntro" class="detailed-intro">
          <!-- 算法原理/详细描述 -->
          <div v-if="getDetailedDescription()" class="description-section">
            <h4 class="section-title">
              <el-icon><InfoFilled /></el-icon>
              算法原理
            </h4>
            <div class="description-content">
              {{ getDetailedDescription() }}
            </div>
          </div>

          <!-- 使用建议 -->
          <div v-if="getUsageTips().length > 0" class="tips-section">
            <h4 class="section-title">
              <el-icon><InfoFilled /></el-icon>
              使用建议
            </h4>
            <div class="tips-list">
              <div
                v-for="(tip, index) in getUsageTips()"
                :key="index"
                class="tip-item"
              >
                <el-icon class="tip-icon"><Check /></el-icon>
                <span class="tip-text">{{ tip }}</span>
              </div>
            </div>
          </div>
          
          <!-- 主要参数说明 -->
          <div v-if="getMainParams() && Object.keys(getMainParams()).length > 0" class="params-section">
            <h4 class="section-title">
              <el-icon><Setting /></el-icon>
              主要参数说明
            </h4>
            <div class="params-grid">
              <div
                v-for="(paramConfig, key) in getMainParams()"
                :key="key"
                class="param-card"
              >
                <div class="param-name">{{ paramConfig.name || paramConfig.displayName }}</div>
                <div class="param-description">{{ paramConfig.description }}</div>
                <div v-if="paramConfig.value !== undefined" class="param-default">
                  默认值: <code>{{ formatParamValue(paramConfig.value) }}</code>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 适用场景 -->
          <div class="scenarios-section">
            <h4 class="section-title">
              <el-icon><DataAnalysis /></el-icon>
              适用场景
            </h4>
            <div class="scenarios-content">
              {{ getModelScenarios() }}
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </el-card>
    
    <!-- 加载状态 -->
    <div v-else class="loading-state">
      <el-skeleton :rows="2" animated />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ArrowDown, InfoFilled, Check, Setting, DataAnalysis } from '@element-plus/icons-vue';
import {
  loadModelParams,
  type ModelParamConfig,
  type SupportedModel
} from '@/utils/modelParamsLoader';
import { dynamicModelManager, isMLModel as checkIsMLModel, getModelById } from '@/utils/dynamicModelLoader';

const props = defineProps<{
  modelType: string;
  modelDisplayName?: string;
}>();

// 状态管理
const showDetailedIntro = ref(false);
const modelConfig = ref<ModelParamConfig | null>(null);

// 判断是否为机器学习模型（包括树模型）
const isMLModel = computed(() => {
  return checkIsMLModel(props.modelType);
});

// 机器学习模型配置
const mlModelConfig = ref<ModelParamConfig | null>(null);

// 计算属性 - 主要参数
const mainParams = computed(() => {
  if (isMLModel.value) {
    return getMLMainParams();
  }

  // 优先从introduction.mainParams读取
  if (modelConfig.value?.introduction?.mainParams) {
    const result: Record<string, any> = {};
    modelConfig.value.introduction.mainParams.forEach((param, index) => {
      result[index] = param;
    });
    return result;
  }

  // 如果没有introduction.mainParams，则使用默认逻辑
  if (!modelConfig.value?.defaultParams) return {};

  // 定义每个线性模型的主要参数
  const mainParamKeys: Record<string, string[]> = {
    LinearRegression: ['fit_intercept', 'normalize', 'positive'],
    Ridge: ['alpha', 'fit_intercept', 'solver'],
    Lasso: ['alpha', 'fit_intercept', 'max_iter'],
    ElasticNet: ['alpha', 'l1_ratio', 'fit_intercept']
  };

  const keys = mainParamKeys[props.modelType] || Object.keys(modelConfig.value.defaultParams).slice(0, 3);

  const params: Record<string, any> = {};
  keys.forEach(key => {
    if (modelConfig.value?.defaultParams[key]) {
      params[key] = modelConfig.value.defaultParams[key];
    }
  });

  return params;
});

// 方法
const loadModelConfiguration = async () => {
  if (!props.modelType) return;

  try {
    if (isMLModel.value) {
      // 直接使用模型类型名称作为配置文件名
      const configModule = await import(`@/config/modelParams/${props.modelType}.json`);
      mlModelConfig.value = configModule.default;
    } else {
      // 加载线性模型配置
      const config = await loadModelParams(props.modelType as SupportedModel);
      modelConfig.value = config;
    }
  } catch (error) {
    console.error('Failed to load model configuration:', error);
  }
};

const formatParamValue = (value: any): string => {
  if (value === null) return 'null';
  if (typeof value === 'boolean') return value.toString();
  if (typeof value === 'string') return `"${value}"`;
  return String(value);
};

// 获取基本描述
const getBasicDescription = (): string => {
  if (isMLModel.value) {
    return mlModelConfig.value?.description || '加载中...';
  }

  return modelConfig.value?.description || '加载中...';
};

// 获取详细描述
const getDetailedDescription = (): string => {
  if (isMLModel.value) {
    return mlModelConfig.value?.introduction?.detailedDescription || '';
  }

  return modelConfig.value?.introduction?.detailedDescription || '';
};

// 获取使用建议
const getUsageTips = (): string[] => {
  if (isMLModel.value) {
    return mlModelConfig.value?.introduction?.usageTips || mlModelConfig.value?.tips || [];
  }

  return modelConfig.value?.introduction?.usageTips || modelConfig.value?.tips || [];
};

// 获取机器学习模型的主要参数
const getMLMainParams = () => {
  if (!mlModelConfig.value?.introduction?.mainParams) return {};

  const result: Record<string, any> = {};
  mlModelConfig.value.introduction.mainParams.forEach((param, index) => {
    result[index] = param;
  });
  return result;
};

// 获取主要参数
const getMainParams = () => {
  return mainParams.value;
};

// 获取适用场景
const getModelScenarios = (): string => {
  if (isMLModel.value) {
    return mlModelConfig.value?.introduction?.scenarios || '请根据数据特点选择合适的模型。';
  }

  return modelConfig.value?.introduction?.scenarios || '请根据数据特点选择合适的模型。';
};

// 生命周期
onMounted(() => {
  loadModelConfiguration();
});

// 监听模型类型变化
watch(() => props.modelType, () => {
  loadModelConfiguration();
});
</script>

<style scoped>
.model-introduction {
  width: 100%;
}

.intro-card {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border: 1px solid var(--el-color-primary-light-8);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.model-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.model-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-color-primary);
}

.basic-description {
  color: var(--el-text-color-regular);
  line-height: 1.6;
  font-size: 14px;
  margin: 0;
}

.expand-button {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-text-color-regular);
  transition: color 0.3s ease;
  font-size: 13px;
}

.expand-button:hover {
  color: var(--el-color-primary);
}

.expand-icon {
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.detailed-intro {
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 12px 0;
}

.description-section,
.tips-section,
.params-section,
.scenarios-section {
  margin-bottom: 20px;
}

.description-content {
  color: var(--el-text-color-regular);
  font-size: 13px;
  line-height: 1.6;
  padding: 12px;
  background: var(--el-bg-color);
  border-radius: 6px;
  border-left: 3px solid var(--el-color-primary-light-5);
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 6px;
  border-left: 3px solid var(--el-color-primary-light-5);
}

.tip-icon {
  color: var(--el-color-success);
  margin-top: 2px;
  flex-shrink: 0;
}

.tip-text {
  color: var(--el-text-color-regular);
  font-size: 13px;
  line-height: 1.5;
}

.params-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.param-card {
  padding: 12px;
  background: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.param-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 13px;
  margin-bottom: 4px;
}

.param-description {
  color: var(--el-text-color-regular);
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 6px;
}

.param-default {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.param-default code {
  background: var(--el-color-info-light-9);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 11px;
}

.scenarios-content {
  color: var(--el-text-color-regular);
  font-size: 13px;
  line-height: 1.5;
  padding: 12px;
  background: var(--el-bg-color);
  border-radius: 6px;
  border-left: 3px solid var(--el-color-warning);
}

.loading-state {
  padding: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .params-grid {
    grid-template-columns: 1fr;
  }

  .param-card {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .model-title {
    width: 100%;
  }

  .expand-button {
    align-self: flex-end;
  }

  .model-name {
    font-size: 14px;
  }
}
</style>
