<template>
  <div class="table-content-wrapper">
    <div v-if="loading" class="loading-mask">
      <el-icon class="is-loading" :size="32">
        <Loading />
      </el-icon>
    </div>
    <hot-table
      ref="hotTableRef"
      :settings="settings"
      @after-change="handleAfterChange"
      @after-selection="handleAfterSelection"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed, onBeforeUnmount } from "vue";
import { HotTable } from "@handsontable/vue3";
import { registerAllModules } from "handsontable/registry";
import "handsontable/dist/handsontable.full.css";
import "handsontable/languages/zh-CN";
import { Loading } from "@element-plus/icons-vue";

registerAllModules();

const props = defineProps<{
  settings: any;
  loading?: boolean;
}>();

const emit = defineEmits<{
  ready: [instance: any];
  change: [changes: any];
  selection: [selection: any];
}>();

const hotTableRef = ref();
let hotInstance: any = null;

const handleAfterChange = (changes: any, source: string) => {
  if (source !== "loadData") {
    emit("change", { changes, source });
  }
};

const handleAfterSelection = (
  row: number,
  col: number,
  row2: number,
  col2: number,
) => {
  emit("selection", { row, col, row2, col2 });
};

onMounted(() => {
  if (hotTableRef.value?.hotInstance) {
    hotInstance = hotTableRef.value.hotInstance;
    emit("ready", hotInstance);
  }
});

watch(
  () => hotTableRef.value?.hotInstance,
  (instance) => {
    if (instance) {
      hotInstance = instance;
      emit("ready", instance);
    }
  },
);

onBeforeUnmount(() => {
  // 组件卸载时不需要手动销毁，Vue3 会自动处理
  hotInstance = null;
});

defineExpose({
  hotInstance: computed(() => hotTableRef.value?.hotInstance),
});
</script>
