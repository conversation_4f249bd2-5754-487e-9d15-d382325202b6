// 模型管理模块组件导出

// 线性模型相关组件
export { default as LinearModelDialog } from "./src/linearModelDialog/index.vue";
export { default as LinearModelSelection } from "./src/linearModelDialog/linearModelSelection.vue";
export { default as LinearModelParamsSetting } from "./src/linearModelDialog/linearModelParamsSetting.vue";
export { default as LmModelResult } from "./src/linearModelDialog/lmModelResult.vue";
export { default as ModelIntroduction } from "./src/common/ModelIntroduction.vue";

// 机器学习模型相关组件
export { default as MlModelDialog } from "./src/mlModelDialog/index.vue";
export { default as MlModelSelection } from "./src/mlModelDialog/mlModelSelection.vue";
export { default as MlModelParamsSetting } from "./src/mlModelDialog/mlModelParamsSetting.vue";
export { default as MlModelResult } from "./src/mlModelDialog/mlModelResult.vue";


// 模型结果展示组件
export { default as ModelResultContainer } from "./src/modelResult/ModelResultContainer.vue";
export { default as ModelInfoCard } from "./src/modelResult/ModelInfoCard.vue";
export { default as ModelCharts } from "./src/modelResult/ModelCharts.vue";
export { default as MetricsTable } from "./src/modelResult/MetricsTable.vue";
export { default as PredictionTable } from "./src/modelResult/PredictionTable.vue";
