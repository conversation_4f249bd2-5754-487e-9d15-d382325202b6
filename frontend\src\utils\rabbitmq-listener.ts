// RabbitMQ 通知监听器
import { ElMessage, ElNotification } from 'element-plus';
import { getModelDisplayName } from '@/utils/dynamicModelLoader';

export interface MLModelNotification {
  taskId: string;
  modelType: string;
  status: 'completed' | 'failed';
  message: string;
  result?: {
    accuracy?: number;
    metrics?: Record<string, any>;
    modelPath?: string;
    resultUrl?: string;
  };
  error?: string;
  timestamp: string;
}

export class RabbitMQListener {
  private static instance: RabbitMQListener;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): RabbitMQListener {
    if (!RabbitMQListener.instance) {
      RabbitMQListener.instance = new RabbitMQListener();
    }
    return RabbitMQListener.instance;
  }

  // 初始化监听器
  public initialize() {
    if (this.isInitialized) return;

    // 监听 RabbitMQ 连接状态
    window.ipcRenderer?.on('rabbitmq-connected', (event, data) => {
      console.log('RabbitMQ connection status:', data);
      
      if (data.connected) {
        console.log('RabbitMQ 通知服务已连接');
      } else {
        console.warn('RabbitMQ 通知服务连接失败:', data.error);
        ElMessage.warning('消息通知服务连接失败，可能无法接收模型完成通知');
      }
    });

    // 监听机器学习模型通知
    window.ipcRenderer?.on('ml-model-notification', (event, notification: MLModelNotification) => {
      console.log('Received ML model notification:', notification);
      this.handleModelNotification(notification);
    });

    // 监听打开模型结果的请求
    window.ipcRenderer?.on('open-model-result', (event, data) => {
      console.log('Request to open model result:', data);
      this.handleOpenModelResult(data);
    });

    this.isInitialized = true;
    console.log('RabbitMQ listener initialized');
  }

  // 处理模型通知
  private handleModelNotification(notification: MLModelNotification) {
    // 使用动态模型管理器获取显示名称
    const modelName = getModelDisplayName(notification.modelType);

    if (notification.status === 'completed') {
      // 成功通知
      ElNotification({
        title: `${modelName}模型构建完成`,
        message: this.formatSuccessMessage(notification),
        type: 'success',
        duration: 0, // 不自动关闭
        onClick: () => {
          this.handleOpenModelResult({
            taskId: notification.taskId,
            modelType: notification.modelType,
            result: notification.result
          });
        }
      });

      // 同时显示简短的消息提示
      ElMessage.success(`${modelName}模型训练完成，点击通知查看详细结果`);

    } else if (notification.status === 'failed') {
      // 失败通知
      ElNotification({
        title: `${modelName}模型构建失败`,
        message: notification.error || notification.message || '模型训练过程中出现错误',
        type: 'error',
        duration: 0 // 不自动关闭
      });

      ElMessage.error(`${modelName}模型训练失败`);
    }
  }

  // 格式化成功消息
  private formatSuccessMessage(notification: MLModelNotification): string {
    let message = notification.message || '模型训练已完成';
    
    if (notification.result?.accuracy) {
      message += `\n准确率: ${(notification.result.accuracy * 100).toFixed(2)}%`;
    }

    if (notification.result?.metrics) {
      const metrics = notification.result.metrics;
      if (metrics.precision) {
        message += `\n精确率: ${(metrics.precision * 100).toFixed(2)}%`;
      }
      if (metrics.recall) {
        message += `\n召回率: ${(metrics.recall * 100).toFixed(2)}%`;
      }
      if (metrics.f1_score) {
        message += `\nF1分数: ${(metrics.f1_score * 100).toFixed(2)}%`;
      }
    }

    message += '\n\n点击此通知查看详细结果';
    return message;
  }

  // 处理打开模型结果
  private handleOpenModelResult(data: any) {
    console.log('Opening model result:', data);

    try {
      // 如果有结果URL，直接打开
      if (data.resultUrl) {
        window.open(data.resultUrl, '_blank');
        return;
      }

      // 如果有结果数据，打开模型结果页面
      if (data.result) {
        const resultQuery = {
          taskId: data.taskId,
          modelType: data.modelType,
          result: JSON.stringify(data.result)
        };

        // 构建机器学习模型结果页面URL
        const baseUrl = window.location.origin + window.location.pathname;
        const resultUrl = `${baseUrl}#/modelManagement/mlResult?${new URLSearchParams(resultQuery).toString()}`;

        // 在新窗口中打开结果页面
        window.open(resultUrl, "_blank");

        ElMessage.success('模型结果页面已打开');
      } else {
        // 如果没有结果数据，显示提示信息
        ElMessage.warning('暂无模型结果数据可显示');
        console.warn('No result data available:', data);
      }
    } catch (error) {
      console.error('Error opening model result:', error);
      ElMessage.error('打开模型结果页面失败');
    }
  }

  // 获取 RabbitMQ 连接状态
  public async getConnectionStatus() {
    try {
      const result = await window.ipcRenderer?.invoke('rabbitmq:get-connection-status');
      return result;
    } catch (error) {
      console.error('Error getting RabbitMQ connection status:', error);
      return { connected: false, error: 'Failed to get status' };
    }
  }

  // 测试通知功能
  public async testNotification() {
    try {
      const result = await window.ipcRenderer?.invoke('rabbitmq:test-notification');
      if (result?.success) {
        ElMessage.success('测试通知已发送');
      } else {
        ElMessage.error('发送测试通知失败: ' + (result?.error || 'Unknown error'));
      }
      return result;
    } catch (error) {
      console.error('Error sending test notification:', error);
      ElMessage.error('发送测试通知失败');
      return { success: false, error: 'Failed to send test notification' };
    }
  }

  // 清理监听器
  public cleanup() {
    if (!this.isInitialized) return;

    window.ipcRenderer?.removeAllListeners('rabbitmq-connected');
    window.ipcRenderer?.removeAllListeners('ml-model-notification');
    window.ipcRenderer?.removeAllListeners('open-model-result');

    this.isInitialized = false;
    console.log('RabbitMQ listener cleaned up');
  }
}

// 导出单例实例
export const rabbitmqListener = RabbitMQListener.getInstance();
