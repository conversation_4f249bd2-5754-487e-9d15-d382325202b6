<template>
  <div class="analyze-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <div class="loading-text">正在计算统计信息...</div>
    </div>

    <!-- 数据概览统计卡片 -->
    <div v-else class="overview-cards">
      <el-card class="stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="24" color="#409EFF">
              <DataLine />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ rowCount }}</div>
            <div class="stat-label">数据总量</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="24" color="#67C23A">
              <Grid />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ columnCount }}</div>
            <div class="stat-label">列数</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="24" color="#E6A23C">
              <Warning />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ totalMissingCount }}</div>
            <div class="stat-label">缺失值总数</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 缺失值详细统计 -->
    <el-card v-if="hasMissingValues" class="missing-values-card">
      <template #header>
        <div class="card-header">
          <el-icon><Warning /></el-icon>
          <span>缺失值详细统计</span>
        </div>
      </template>
      <div class="missing-values-grid">
        <div
          v-for="(count, col) in missingValues"
          :key="col"
          class="missing-item"
          v-show="count > 0"
        >
          <span class="column-name">{{ col }}</span>
          <el-tag :type="count > 0 ? 'warning' : 'success'" size="small">
            {{ count }} 个
          </el-tag>
        </div>
      </div>
    </el-card>

    <!-- 列统计分析 -->
    <el-card class="analysis-card">
      <template #header>
        <div class="card-header">
          <el-icon><TrendCharts /></el-icon>
          <span>列统计分析</span>
        </div>
      </template>

      <div class="analysis-controls">
        <el-select
          v-model="selectedColumn"
          placeholder="请选择要分析的列"
          style="width: 240px"
          clearable
        >
          <el-option
            v-for="col in columns"
            :key="col"
            :label="col"
            :value="col"
          />
        </el-select>
        <el-button
          type="primary"
          :disabled="!selectedColumn"
          @click="analyzeColumn"
          :loading="analyzing"
        >
          <el-icon><DataAnalysis /></el-icon>
          分析
        </el-button>
      </div>

      <!-- 统计结果 -->
      <div v-if="stats" class="stats-results">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-item-label">均值</div>
            <div class="stat-item-value">{{ stats.mean_values }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-item-label">中位数</div>
            <div class="stat-item-value">{{ stats.median_values }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-item-label">标准差</div>
            <div class="stat-item-value">{{ stats.std }}</div>
          </div>
        </div>
      </div>

      <el-empty
        v-else-if="selectedColumn && !analyzing"
        description="请点击分析按钮查看统计结果"
        :image-size="80"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from "vue";
import {
  DataLine,
  Grid,
  Warning,
  TrendCharts,
  DataAnalysis
} from "@element-plus/icons-vue";
import type { TableInstance } from "../dataTable/types";

// 使用优化的统计计算工具替代simple-statistics

// 导入性能优化工具
import {
  dataAnalysisCache,
  batchDataProcessor,
  StatsCalculator,
  measureAsyncPerformance,
  type DataStats,
  type ColumnStats
} from "@/utils/dataAnalysisOptimization";

const props = defineProps<{
  tableInstance?: TableInstance;
}>();

// 缓存数据和统计信息
const cachedData = ref<any[][]>([]);
const cachedHeaders = ref<string[]>([]);
const cachedStats = ref<{
  rowCount: number;
  columnCount: number;
  missingValues: Record<string, number>;
  totalMissingCount: number;
  dataHash: string;
}>({
  rowCount: 0,
  columnCount: 0,
  missingValues: {},
  totalMissingCount: 0,
  dataHash: ''
});

// 加载状态
const loading = ref(true);

// 这些函数现在由dataAnalysisCache提供

// 实时获取表格数据 - 确保数据变化时能触发重新计算
const data = computed(() => {
  if (!props.tableInstance) return [];
  try {
    // 使用getData()方法确保获取最新数据
    const tableData = props.tableInstance.getData() || [];
    return tableData;
  } catch (error) {
    console.warn("Failed to get table data:", error);
    return [];
  }
});

// 实时获取表格头部
const header = computed(() => {
  if (!props.tableInstance) return [];
  try {
    const columns = props.tableInstance.getColumns() || [];
    return columns.map((col) => col.title || col.data);
  } catch (error) {
    console.warn("Failed to get table columns:", error);
    return [];
  }
});

// 使用缓存的基本信息
const rowCount = computed(() => cachedStats.value.rowCount);
const columnCount = computed(() => cachedStats.value.columnCount);
const columns = computed(() => cachedHeaders.value);
const missingValues = computed(() => cachedStats.value.missingValues);
const totalMissingCount = computed(() => cachedStats.value.totalMissingCount);

// 是否有缺失值
const hasMissingValues = computed(() => {
  return totalMissingCount.value > 0;
});

// 异步计算统计信息 - 使用优化工具，确保实时更新
const calculateStats = measureAsyncPerformance(async () => {
  const currentData = data.value;
  const currentHeaders = header.value;

  if (currentData.length === 0 || currentHeaders.length === 0) {
    // 重置缓存和状态
    cachedData.value = [];
    cachedHeaders.value = [];
    cachedStats.value = {
      rowCount: 0,
      columnCount: 0,
      missingValues: {},
      totalMissingCount: 0,
      dataHash: "",
    };
    loading.value = false;
    return;
  }

  // 暂时禁用缓存，确保数据变化时能正确重新计算
  // 后续可以根据需要重新启用更智能的缓存机制

  loading.value = true;

  // 使用 requestIdleCallback 或 setTimeout 进行异步计算
  await nextTick();

  try {
    // 使用优化的批量处理器计算缺失值
    const missingCounts = await batchDataProcessor.calculateMissingValues(
      currentData,
      currentHeaders,
      (progress) => {
        // 可以在这里更新进度条
        console.log(`计算进度: ${(progress * 100).toFixed(1)}%`);
      },
    );

    const totalMissing = Object.values(missingCounts).reduce(
      (sum, count) => sum + count,
      0,
    );

    // 生成数据哈希
    const dataHash = dataAnalysisCache.generateDataHash(
      currentData,
      currentHeaders,
    );

    const statsResult: DataStats = {
      rowCount: currentData.length,
      columnCount: currentHeaders.length,
      missingValues: missingCounts,
      totalMissingCount: totalMissing,
      dataHash,
    };

    // 更新缓存和本地状态
    dataAnalysisCache.setStats(dataHash, statsResult);
    cachedData.value = currentData;
    cachedHeaders.value = [...currentHeaders];
    cachedStats.value = statsResult;
  } catch (error) {
    console.error("Failed to calculate stats:", error);
  } finally {
    loading.value = false;
  }
}, "数据统计计算");

// 列选择和结果
const selectedColumn = ref<string>();
const analyzing = ref(false);
const stats = ref<{
  mean_values: number;
  median_values: number;
  std: number;
} | null>(null);

// 当选择的列改变时，清空统计结果
watch(selectedColumn, () => {
  stats.value = null;
});

// 当数据变化时，也清空列统计结果
watch([data, header], () => {
  stats.value = null;
}, { deep: true });

// 优化的列分析函数 - 使用缓存和批量处理
const analyzeColumn = measureAsyncPerformance(async () => {
  if (!selectedColumn.value) return;

  analyzing.value = true;

  try {
    const colIndex = cachedHeaders.value.indexOf(selectedColumn.value);
    if (colIndex === -1) return;

    // 生成列缓存键
    const cacheKey = `${cachedStats.value.dataHash}-${selectedColumn.value}`;

    // 检查列统计缓存
    const cachedColumnStats = dataAnalysisCache.getColumnStats(cacheKey);
    if (cachedColumnStats) {
      stats.value = {
        mean_values: cachedColumnStats.mean,
        median_values: cachedColumnStats.median,
        std: cachedColumnStats.std
      };
      return;
    }

    // 使用优化的批量处理器提取数值
    const values = await batchDataProcessor.extractNumericValues(
      cachedData.value,
      colIndex,
      (progress) => {
        console.log(`列分析进度: ${(progress * 100).toFixed(1)}%`);
      }
    );

    if (values.length === 0) {
      stats.value = null;
      return;
    }

    // 使用优化的统计计算器
    const columnStats = StatsCalculator.calculateBasicStats(values);

    // 缓存结果
    dataAnalysisCache.setColumnStats(cacheKey, columnStats);

    stats.value = {
      mean_values: columnStats.mean,
      median_values: columnStats.median,
      std: columnStats.std
    };
  } catch (error) {
    console.error("Failed to analyze column:", error);
    stats.value = null;
  } finally {
    analyzing.value = false;
  }
}, "列统计分析");

// 强制刷新统计信息的功能已集成到监听器中

// 监听数据变化 - 确保实时更新
watch(
  [data, header],
  () => {
    // 每次数据变化都重新计算，确保实时性
    calculateStats();
  },
  { immediate: true, deep: true },
);

// 组件挂载时初始化
onMounted(() => {
  calculateStats();
});
</script>

<style lang="scss" scoped>
.analyze-container {
  padding: 16px;
  background: #f8f9fa;
  min-height: 100%;
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  min-height: 300px;
}

.loading-text {
  margin-top: 16px;
  color: #909399;
  font-size: 14px;
}

// 概览统计卡片
.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-card__body) {
    padding: 20px;
  }
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(64, 158, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

// 缺失值统计卡片
.missing-values-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;

  :deep(.el-card__header) {
    background: #fff9e6;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 12px 12px 0 0;
  }
}

.missing-values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.missing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.column-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

// 分析卡片
.analysis-card {
  border-radius: 12px;
  border: none;

  :deep(.el-card__header) {
    background: #f0f9ff;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 12px 12px 0 0;
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.analysis-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

// 统计结果
.stats-results {
  margin-top: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
}

.stat-item-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-item-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
}

// 响应式设计
@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }

  .missing-values-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .analysis-controls {
    flex-direction: column;
    align-items: stretch;

    .el-select {
      width: 100% !important;
    }
  }
}
</style>
