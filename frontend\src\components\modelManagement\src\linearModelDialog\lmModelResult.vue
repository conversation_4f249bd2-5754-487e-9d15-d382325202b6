<template>
  <div class="model-result-page">
    <ModelResultContainer
      :model-result="modelResult"
      :info-column-count="2"
      :show-evaluation-method="true"
      :show-metrics-cards="false"
      :show-metrics-table="true"
      :chart-type="'scatter'"
      :show-error-rate="false"
      :dataset-config="datasetConfig"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import ModelResultContainer from "@/components/modelManagement/src/modelResult/ModelResultContainer.vue";

// 数据集配置
const datasetConfig = {
  order: ["train", "test", "cv"] as const,
  titles: {
    train: "训练集",
    test: "测试集",
    cv: "交叉验证",
  },
};

// 模型结果数据
const route = useRoute();
const modelResult = ref<any>(null);

// 加载模型结果数据

onMounted(() => {
  try {
    const resultData = route.query.result as string;
    const taskId = route.query.taskId as string;
    const modelType = route.query.modelType as string;

    if (resultData) {
      modelResult.value = JSON.parse(resultData);

      // 如果有任务ID和模型类型，添加到模型结果中
      if (taskId) {
        modelResult.value.taskId = taskId;
      }
      if (modelType) {
        modelResult.value.modelType = modelType;
      }

      console.log("Model result loaded:", modelResult.value);
      ElMessage.success("模型结果加载成功");
    } else {
      ElMessage.warning("未找到模型结果数据");
    }
  } catch (err) {
    ElMessage.error("数据加载失败: " + (err as Error).message);
    console.error("Error loading model result:", err);
  }
});
</script>

<style scoped>
.model-result-page {
  padding: 20px;
  min-height: 100vh;
  background: var(--el-bg-color);
}

.charts-section {
  padding: 20px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
}

:deep(.chart-card) {
  min-height: 450px;

  .chart {
    width: 100%;
    height: 400px;
    min-height: 400px;
  }
}

@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
}

:deep(.el-tabs--card) {
  > .el-tabs__header {
    margin: 0;
  }
}
</style>
