{"name": "Ridge", "displayName": "岭回归", "description": "带L2正则化的线性回归，用于处理多重共线性问题", "category": "linear", "type": "regression", "defaultParams": {"alpha": {"value": 1.0, "type": "number", "description": "正则化强度，值越大正则化越强", "displayName": "正则化参数(α)", "min": 0.001, "max": 100, "step": 0.001}, "fit_intercept": {"value": true, "type": "boolean", "description": "是否计算截距项", "displayName": "计算截距"}, "solver": {"value": "auto", "type": "select", "description": "求解器选择", "displayName": "求解器", "options": [{"value": "auto", "label": "自动选择"}, {"value": "svd", "label": "奇异值分解"}, {"value": "cholesky", "label": "Cholesky分解"}, {"value": "lsqr", "label": "最小二乘QR"}, {"value": "sparse_cg", "label": "稀疏共轭梯度"}, {"value": "sag", "label": "随机平均梯度"}, {"value": "saga", "label": "SAGA算法"}]}, "positive": {"value": false, "type": "boolean", "description": "是否强制系数为正数", "displayName": "强制正系数"}, "random_state": {"value": null, "type": "number", "description": "随机种子", "displayName": "随机种子", "min": 0, "max": 1000, "nullable": true}}, "evaluation": {"splitDataset": {"value": false, "description": "是否划分训练/测试集"}, "trainRatio": {"value": 70, "min": 50, "max": 90, "step": 5, "description": "训练集比例(%)"}, "randomState": {"value": 42, "min": 0, "max": 1000, "description": "随机种子"}, "useModelValidation": {"value": false, "description": "是否使用交叉验证"}, "validationType": {"value": "k-fold", "options": ["k-fold", "leave-one-out"], "description": "验证方法"}, "kFolds": {"value": 5, "min": 2, "max": 10, "description": "k折交叉验证的k值"}}, "tips": ["岭回归通过L2正则化解决多重共线性问题", "α值越大，正则化越强，模型越简单但可能欠拟合", "α值越小，正则化越弱，模型越复杂但可能过拟合", "建议通过交叉验证选择最优的α值"], "introduction": {"detailedDescription": "岭回归在普通线性回归的基础上加入L2正则化项，通过惩罚回归系数的平方和来防止过拟合。正则化参数α控制惩罚强度，有效解决多重共线性问题，提高模型的稳定性和泛化能力。", "usageTips": ["适用于存在多重共线性问题的数据", "通过L2正则化提高模型稳定性", "α参数需要通过交叉验证调优", "对特征缩放敏感，建议标准化"], "scenarios": "适用于存在多重共线性问题的数据，通过L2正则化提高模型稳定性。", "mainParams": [{"name": "alpha", "description": "L2正则化强度参数"}, {"name": "fit_intercept", "description": "是否计算截距项"}, {"name": "solver", "description": "求解器选择"}]}}