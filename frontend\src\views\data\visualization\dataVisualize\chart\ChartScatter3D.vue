<template>
  <div style="width: 96%; height: 100vh">
    <el-card style="margin-top: 30px">
      <!-- 选择列部分 -->
      <div class="select-container">
        <div class="select-item">
          <span class="select-label">X轴：</span>
          <el-select
            v-model="selectedX"
            placeholder="选择X轴"
            style="width: 200px"
          >
            <el-option
              v-for="item in header_table"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </div>
        <div class="select-item">
          <span class="select-label">Y轴：</span>
          <el-select
            v-model="selectedY"
            placeholder="选择Y轴"
            style="width: 200px"
          >
            <el-option
              v-for="item in header_table"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </div>
        <div class="select-item">
          <span class="select-label">Z轴：</span>
          <el-select
            v-model="selectedZ"
            placeholder="选择Z轴"
            style="width: 200px"
          >
            <el-option
              v-for="item in header_table"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </div>
      </div>

      <!-- 图表部分 -->
      <div ref="chartRef" style="width: 100%; height: 60vh" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { useDark, useECharts } from "@pureadmin/utils";
// import "echarts-gl";

// 父组件传入的数据
const { data: data_table, header: header_table } = defineProps([
  "data",
  "header"
]);

// 主题
const { isDark } = useDark();
const theme = computed(() => (isDark.value ? "dark" : "default"));

// 图表相关
const chartRef = ref();
const { setOptions, getInstance } = useECharts(chartRef, { theme });

// 选择的列
const selectedX = ref("");
const selectedY = ref("");
const selectedZ = ref("");

// 监听选择变化，更新图表
watch([selectedX, selectedY, selectedZ], ([newX, newY, newZ]) => {
  if (newX && newY && newZ) {
    updateChart();
  }
});

// 更新图表配置
function updateChart() {
  const xIndex = header_table.indexOf(selectedX.value);
  const yIndex = header_table.indexOf(selectedY.value);
  const zIndex = header_table.indexOf(selectedZ.value);

  if (xIndex === -1 || yIndex === -1 || zIndex === -1) return;

  const chartData = data_table.map(row => [
    row[xIndex],
    row[yIndex],
    row[zIndex]
  ]);

  setOptions({
    grid3D: {},
    xAxis3D: {
      type: "value",
      name: selectedX.value
    },
    yAxis3D: {
      type: "value",
      name: selectedY.value
    },
    zAxis3D: {
      type: "value",
      name: selectedZ.value
    },
    dataset: {
      dimensions: [selectedX.value, selectedY.value, selectedZ.value],
      source: chartData
    },
    series: [
      {
        // @ts-ignore
        type: "scatter3D",
        encode: {
          x: selectedX.value,
          y: selectedY.value,
          z: selectedZ.value,
          tooltip: [0, 1, 2]
        }
      }
    ]
  });
}

// 页面加载后，默认选择前3列
onMounted(() => {
  if (header_table.length >= 3) {
    selectedX.value = header_table[0];
    selectedY.value = header_table[1];
    selectedZ.value = header_table[2];
  }
});

import { exportChartInstance } from "@/utils/chartExport";
// 暴露给父组件的方法-用于按钮点击时的下载
function exportChartImage(type: "png" | "svg" = "png") {
  const chart = getInstance();
  exportChartInstance(chart, type);
}
defineExpose({ exportChartImage });
</script>

<style lang="scss" scoped>
.select-container {
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  margin-bottom: 20px;
  gap: 30px; /* 每个下拉框间隔 */
}

.select-item {
  display: flex;
  align-items: center; /* label和下拉框垂直居中对齐 */
}

.select-label {
  margin-right: 8px;
  font-weight: bold;
}
</style>
