import request from "@/utils/request";
import type { ModelConfig, RegModelResult } from "@/types/models";
import { AxiosHeaders } from "axios";

export type LinearModelResponse = {
  code: number;
  msg: string;
  data: RegModelResult;
};

export const buildLinearModel = (data?: ModelConfig) => {
  return request.post<LinearModelResponse>("/linear/build", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};
