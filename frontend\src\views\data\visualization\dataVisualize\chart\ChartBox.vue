<template>
  <div style="width: 96%; height: 100vh">
    <el-card style="margin-top: 30px">
      <!-- 下拉框，选择需要分析的列 -->
      <el-select
        v-model="selectedColumn"
        placeholder="选择分析的列"
        style="width: 200px; margin-bottom: 20px"
      >
        <el-option
          v-for="(col, index) in header_table"
          :key="index"
          :label="col"
          :value="index"
        />
        <!-- 这里传递列索引 -->
      </el-select>

      <div ref="chartRef" style="width: 100%; height: 50vh" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useDark, useECharts } from "@pureadmin/utils";

// 主题切换
const { isDark } = useDark();
const theme = computed(() => (isDark.value ? "dark" : "default"));

// ECharts 设置
const chartRef = ref();
const { setOptions, getInstance } = useECharts(chartRef, { theme });

// 父组件传入的数据
const { data: data_table, header: header_table } = defineProps<{
  data: number[][];
  header: string[];
}>();

// 用户选择的列（默认为第一个列）
const selectedColumn = ref<number>(0);

// 转置并清洗数据函数
const cleanTranspose = (matrix: (number | string)[][]) => {
  return matrix[0].map((_, colIndex) =>
    matrix
      .map(row => row[colIndex])
      .filter(
        val => val !== "null" && val !== null && val !== undefined && val !== ""
      )
      .map(val => Number(val))
      .filter(val => !isNaN(val))
  );
};

// 转置后的数据
const transposedData = cleanTranspose(data_table);

// 动态更新图表
const updateChart = () => {
  const columnData = transposedData[selectedColumn.value]; // 获取选中列的数据

  let options = {
    title: [
      {
        text: `箱线图 - ${header_table[selectedColumn.value]}`,
        left: "center"
      },
      {
        text: "upper: Q3 + 1.5 * IQR \nlower: Q1 - 1.5 * IQR",
        borderColor: "#999",
        borderWidth: 1,
        textStyle: {
          fontWeight: "normal",
          fontSize: 14,
          lineHeight: 20
        },
        left: "10%",
        top: "82%"
      }
    ],
    dataset: [
      {
        source: [columnData] // 仅显示当前选中列的数据
      },
      {
        transform: {
          type: "boxplot",
          config: {
            itemNameFormatter: header_table[selectedColumn.value]
            // ({ value }: { value: number }) =>header_table[value]
          },
          print: true
        }
      },
      {
        fromDatasetIndex: 1,
        fromTransformResult: 1
      }
    ],
    tooltip: {
      trigger: "item",
      axisPointer: {
        type: "shadow"
      }
    },
    grid: {
      bottom: "28%"
    },
    xAxis: {
      type: "category",
      data: [header_table[selectedColumn.value]], // x轴为当前列的名称
      boundaryGap: true,
      nameGap: 30,
      splitArea: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: "value",
      name: "值",
      splitArea: {
        show: true
      }
    },
    series: [
      {
        name: "boxplot",
        type: "boxplot",
        datasetIndex: 1
      },
      {
        name: "outlier",
        type: "scatter",
        datasetIndex: 2
      }
    ]
  };
  // 这里需要忽略一下ts类型检查
  //@ts-ignore
  setOptions(options);
};

import { exportChartInstance } from "@/utils/chartExport";
// 暴露给父组件的方法-用于按钮点击时的下载
function exportChartImage(type: "png" | "svg" = "png") {
  const chart = getInstance();
  exportChartInstance(chart, type);
}
defineExpose({ exportChartImage });

// 在组件加载时，初始化图表
onMounted(() => {
  updateChart();
});

// 监听列的选择变化，更新图表
watch(selectedColumn, updateChart);
</script>

<style lang="scss" scoped>
.analyze-container {
  padding: 10px;
}
.mb-4 {
  margin-bottom: 16px;
}
</style>
