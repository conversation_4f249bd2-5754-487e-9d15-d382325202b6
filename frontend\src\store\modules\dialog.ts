import { defineStore } from "pinia";

export const useDialogStore = defineStore("dialog", {
  state: () => ({
    dialogs: {
      dataAnalyze: false,
      linearModel: false,
      mlModel: false,
      preprocessData: false,

      // 其他对话框状态...
    },
  }),
  actions: {
    showDialog(name: keyof typeof this.dialogs) {
      this.dialogs[name] = true;
    },
    hideDialog(name: keyof typeof this.dialogs) {
      this.dialogs[name] = false;
    },
    toggleDialog(name: keyof typeof this.dialogs) {
      this.dialogs[name] = !this.dialogs[name];
    },
  },
});
