from app import create_app
app = create_app()
if __name__ == '__main__':
    port = 5561
    app.logger.info(f'Flask 应用启动 - 项目目录: {app.config["PROJECTS_FOLDER"]}')
    app.logger.info(f'允许的文件类型: {app.config["ALLOWED_EXTENSIONS"]}')
    app.logger.info(f'最大文件大小: {app.config["MAX_CONTENT_LENGTH"]/1024/1024}MB')
    app.logger.info(f'启动服务器 http://localhost:{port}/')
    app.run(host="0.0.0.0", port=port, debug=False, processes=True)