import unittest
import numpy as np
import pandas as pd
from sklearn.datasets import make_regression
from sklearn.model_selection import train_test_split

from app.utils.Regression import Regression, reg_model_config


class RegressionTests(unittest.TestCase):
    """
    test the class of Regression
    """
    def setUp(self):
        # generate a regression dataset
        X, y = make_regression(n_samples=100, n_features=20, n_informative=10, random_state=42)
        self.X = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(X.shape[1])])
        self.y = pd.Series(y)
        x_train, X_test, y_train, y_test = train_test_split(self.X, self.y, test_size=0.2, random_state=42)
        # 准备测试数据
        self.x_train = x_train
        self.y_train = y_train
        self.x_test = X_test
        self.y_test = y_test
        self.reg = Regression(
            x_train=self.x_train,
            y_train=self.y_train,
            cv_fold=5
        )

    def test_train(self):
        # 初始化回归对象
        self.reg = Regression(
            x_train=self.x_train,
            y_train=self.y_train,
        )
        # training with RF
        model, info = self.reg.train()
        print(info)

    def test_train_cv_and_test(self):
        # 初始化回归对象
        self.reg = Regression(
            x_train=self.x_train,
            y_train=self.y_train,
            x_test=self.x_test,
            y_test=self.y_test,
            cv_fold=5
        )
        # training with RF
        model, info = self.reg.train()
        # save and load model and info
        self.reg.save("model.pkl")
        self.reg = Regression.load("model.pkl")
        print(self.reg.info)

    def test_train_cv(self):
        # 初始化回归对象
        self.reg = Regression(
            x_train=self.x_train,
            y_train=self.y_train,
            cv_fold=5
        )
        model, info = self.reg.train()
        print(info)
        self.assertIsNotNone(model)
        self.assertIsNotNone(info)

    def test_train_with_params(self):
        """测试使用自定义参数训练"""
        self.reg = Regression(
            x_train=self.x_train,
            y_train=self.y_train,
            cv_fold=5
        )
        params = {"n_estimators": 50}
        model, info = self.reg.train(alg_name="RandomForestRegressor", alg_param=params)
        print(info['model_params'])
        self.assertIsNotNone(model)

    def test_predict(self):
        """测试预测功能"""
        self.reg.train()
        predictions = self.reg.predict(self.x_test)
        self.assertEqual(len(predictions), len(self.x_test))

    def test_save_load(self):
        """测试模型的保存和加载"""
        import os
        import tempfile

        # 训练并保存模型
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            self.reg.train()
            self.reg.save(tmp.name)
            self.reg = Regression.load(tmp.name)
        os.unlink(tmp.name)

    def test_invalid_algorithm(self):
        """测试无效算法名称"""
        with self.assertRaises(ValueError):
            self.reg.train(alg_name="InvalidAlgorithm")

    def test_train_multiple_models(self):
        """测试训练多个模型"""
        alg_names = ["RandomForestRegressor", "LinearRegression"]
        all_model_info = self.reg.train_models(alg_names)

        # 验证返回结果包含所有指定的算法
        self.assertEqual(len(all_model_info), len(alg_names))
        for alg in alg_names:
            self.assertIn(alg, all_model_info)

    def test_train_models(self):
        best_model, best_model_info, infos = self.reg.train_models(["SVR", "XGBoost", "GradientBoostingRegressor"])
        print(best_model)
        print(best_model_info)
        print(infos)
        self.reg.save("model.pkl")


if __name__ == '__main__':
    unittest.main()
