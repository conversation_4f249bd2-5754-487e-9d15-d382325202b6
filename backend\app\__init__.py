from flask import Flask, jsonify
import logging
import platform
from app.extensions import cors

def create_app(mode="dev"):
    
    app = Flask(__name__)
    app.config.from_object(f"configs.{mode}") # 从configs.py中加载配置

    app.url_prefix = "/" if platform.system() == "Windows"  else f"/{app.config['SERVER_PREFIX']}/" # 服务器路径统一后缀

    app.temp_folder = app.config["TEMP_FOLDER"]
    app.statics_folder = app.config["STATIC_FOLDER"]
    app.upload_folder = app.config["UPLOAD_FOLDER"]
    app.projects_folder = app.config["PROJECTS_FOLDER"]
    for i in [
        app.temp_folder,
        app.statics_folder,
        app.upload_folder,
        app.projects_folder
    ]: 
        if not i.exists(): i.mkdir()

    # 配置日志
    logging.basicConfig(
        level=app.config["LOG_LEVEL"],
        format=app.config["LOG_FORMAT"],
        datefmt=app.config["LOG_DATE_FORMAT"],
        filename=app.config["LOG_PATH"],
        filemode="a",
        encoding="utf-8"
    )
    app.logger = logging.getLogger()

    # 配置flask插件
    cors.init_app(app)

    # 配置蓝图
    from .routers import linear_bp
    app.register_blueprint(linear_bp, url_prefix=f"{app.url_prefix}/linear")
    from .routers import project_bp
    app.register_blueprint(project_bp, url_prefix=f"{app.url_prefix}/project")
    from .routers import fill_bp
    app.register_blueprint(fill_bp, url_prefix=f"{app.url_prefix}/fill")
    from .routers import outlier_bp
    app.register_blueprint(outlier_bp, url_prefix=f"{app.url_prefix}/outlier")

    # global error handling
    # @app.errorhandler(Exception)
    # def handle_error(error):
    #     error_msg = f'unhandled error: {str(error)}'
    #     app.logger.error(error_msg)
    #     return jsonify({'error': error_msg}), 500
    
    print(app.url_map)

    return app
        
