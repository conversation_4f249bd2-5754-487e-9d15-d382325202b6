<template>
  <div class="workspace-file-tree">
    <!-- Workspace header with actions -->
    <div class="workspace-header" :class="{ 'collapsed': isCollapse }">
      <div class="workspace-title">
        <el-icon :size="16"><Folder /></el-icon>
        <span v-if="!isCollapse">工作区</span>
      </div>
      <div v-if="!isCollapse" class="workspace-actions">
        <el-button
          link
          :icon="FolderOpened"
          size="small"
          @click="selectWorkspaceDirectory"
          title="选择工作区目录"
        />
        <el-button
          link
          :icon="Refresh"
          size="small"
          @click="refreshWorkspace"
          :disabled="!currentWorkspacePath"
          title="刷新"
        />
      </div>
    </div>

    <!-- File tree -->
    <div class="file-tree-container">
      <div v-if="!currentWorkspacePath && workspaceStore.getOpenedFiles.length === 0" class="empty-workspace" :class="{ 'collapsed': isCollapse }">
        <template v-if="!isCollapse">
          <p>未选择工作区</p>
          <el-button type="primary" size="small" @click="selectWorkspaceDirectory">
            选择目录
          </el-button>
        </template>
        <el-button v-else
          link
          :icon="Upload"
          size="small"
          @click="selectWorkspaceDirectory"
          title="选择工作区目录"
          class="upload-button"
        />
      </div>

      <el-tree
        v-else-if="filteredTreeData.length > 0"
        ref="treeRef"
        :data="filteredTreeData"
        :props="defaultProps"
        :load="loadNode"
        :lazy="!isCollapse"
        highlight-current
        node-key="path"
        :expand-on-click-node="false"
        :current-node-key="currentSelectedFile"
        :default-expanded-keys="expandedKeys"
        @node-click="handleNodeClick"
        @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse"
        class="workspace-tree"
        :class="{ 'collapsed': isCollapse }"
      >
        <template #default="{ node, data }">
          <div
            class="tree-node"
            :class="{ 'collapsed': isCollapse }"
          >
            <template v-if="isCollapse">
              <el-tooltip
                :content="node.label"
                placement="right"
                :show-after="300"
                :hide-after="50"
                effect="dark"
                :offset="8"
              >
                <el-icon :size="16" class="node-icon">
                  <Folder v-if="data.isDirectory" />
                  <Document v-else />
                </el-icon>
              </el-tooltip>
            </template>
            <template v-else>
              <el-icon :size="16" class="node-icon">
                <Folder v-if="data.isDirectory" />
                <Document v-else />
              </el-icon>
              <span class="node-label">{{ node.label }}</span>
            </template>
          </div>
        </template>
      </el-tree>

      <div v-else-if="loading" class="loading-container" :class="{ 'collapsed': isCollapse }">
        <el-icon class="is-loading" :size="20">
          <Loading />
        </el-icon>
        <span v-if="!isCollapse">加载中...</span>
      </div>

      <div v-else class="empty-directory" :class="{ 'collapsed': isCollapse }">
        <p v-if="!isCollapse">目录为空</p>
        <el-icon v-else :size="20" class="empty-icon">
          <Folder />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed, watch, nextTick } from "vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import {
  Folder,
  FolderOpened,
  Document,
  Refresh,
  Loading,
  Upload
} from "@element-plus/icons-vue";
import type Node from "element-plus/es/components/tree/src/model/node";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";
import { useRouter } from "vue-router";
import { useAppStoreHook } from "@/store/modules/app";

interface TreeNode {
  label: string;
  path: string;
  isDirectory: boolean;
  isLeaf?: boolean;
  children?: TreeNode[];
}

const emit = defineEmits<{
  fileSelected: [filePath: string];
  directorySelected: [dirPath: string];
  workspaceChanged: [workspacePath: string | null];
}>();

const workspaceStore = useWorkspaceStoreHook();
const router = useRouter();
const appStore = useAppStoreHook();

// 检测侧边栏是否收起
const isCollapse = computed(() => !appStore.sidebar.opened);

// 计算收起状态下应该显示的文件
const filteredTreeData = computed(() => {
  if (!isCollapse.value || !treeData.value.length) {
    return treeData.value;
  }

  // 如果没有当前选中的文件，只显示根目录下的文件（不包括目录）
  if (!currentSelectedFile.value) {
    return treeData.value.filter(item => !item.isDirectory);
  }

  // 获取当前文件的目录路径
  const currentFilePath = currentSelectedFile.value;
  const pathSeparator = currentFilePath.includes('/') ? '/' : '\\';
  const currentDir = currentFilePath.substring(0, currentFilePath.lastIndexOf(pathSeparator));

  // 在单文件模式下，直接返回所有文件（它们都在同一层级）
  if (!currentWorkspacePath.value) {
    return treeData.value.filter(item => !item.isDirectory);
  }

  // 创建一个扁平的文件列表，包含当前目录下的所有文件
  const flattenFiles = (items: TreeNode[]): TreeNode[] => {
    const result: TreeNode[] = [];

    for (const item of items) {
      const itemPath = item.path;
      const itemDir = itemPath.substring(0, itemPath.lastIndexOf(pathSeparator));

      // 如果是文件且在当前目录下，添加到结果中
      if (!item.isDirectory && itemDir === currentDir) {
        result.push(item);
      }
    }

    return result;
  };

  const filteredFiles = flattenFiles(treeData.value);

  // 如果当前目录没有其他文件，至少显示当前文件
  if (filteredFiles.length === 0) {
    const currentFile = treeData.value.find(item => item.path === currentFilePath);
    if (currentFile) {
      return [currentFile];
    }
  }

  return filteredFiles;
});
const treeRef = ref();
const loading = ref(false);
const treeData = ref<TreeNode[]>([]);

// Use computed properties from store
const currentWorkspacePath = computed(() => workspaceStore.getCurrentWorkspacePath);
const currentSelectedFile = computed(() => workspaceStore.getCurrentFilePath);

// Expanded keys for tree state management
const expandedKeys = computed(() => workspaceStore.getExpandedNodes);

const defaultProps = {
  children: "children",
  label: "label",
  isLeaf: "isLeaf"
};

// Select workspace directory
const selectWorkspaceDirectory = async () => {
  try {
    const path = await window.ipcRenderer.invoke("dialog:openDirectory");
    if (path) {
      // Clear all opened files and tabs when switching workspace
      console.log('Switching to new workspace, clearing all files and tabs');
      workspaceStore.clearAllFiles();

      // Clear all tabs by dispatching a custom event
      window.dispatchEvent(new CustomEvent('clear-all-tabs'));

      // Set new workspace path
      workspaceStore.setWorkspacePath(path);
      workspaceStore.clearSingleFileMode(); // Exit single file mode

      // Don't call loadWorkspace here as the watcher will handle it
      emit('workspaceChanged', path);
    }
  } catch (error) {
    console.error("Error selecting workspace directory:", error);
    ElMessage.error("选择目录失败");
  }
};

// Load workspace files
const loadWorkspace = async () => {
  loading.value = true;
  try {
    if (currentWorkspacePath.value) {
      // 项目模式：有工作区路径，显示项目目录中的所有文件
      console.log('Loading project mode for:', currentWorkspacePath.value);
      const fileList = await window.ipcRenderer.invoke(
        "fs:readDirectory",
        currentWorkspacePath.value
      );

      treeData.value = fileList.map((file: any) => ({
        label: file.name,
        path: file.path,
        isDirectory: file.isDirectory,
        isLeaf: !file.isDirectory
      }));
      console.log('Project mode tree data set:', treeData.value);
    } else {
      // 单文件模式：没有工作区路径，只显示打开的文件
      const openedFiles = workspaceStore.getOpenedFiles;
      if (openedFiles.length > 0) {
        console.log('Loading single file mode, opened files:', openedFiles);
        treeData.value = openedFiles.map((filePath: string) => {
          const fileName = filePath.split(/[/\\]/).pop() || filePath;
          return {
            label: fileName,
            path: filePath,
            isDirectory: false,
            isLeaf: true
          };
        });
        console.log('Single file mode tree data set:', treeData.value);
      } else {
        // 没有工作区路径也没有打开的文件
        treeData.value = [];
        console.log('No files to display');
      }
    }
  } catch (error) {
    console.error("Error loading workspace:", error);
    ElMessage.error("加载工作区失败");
    treeData.value = [];
  } finally {
    loading.value = false;
  }
};

// Refresh workspace
const refreshWorkspace = async () => {
  await loadWorkspace();
};

// Lazy load child nodes
const loadNode = async (node: Node, resolve: (data: TreeNode[]) => void) => {
  if (node.level === 0) {
    return resolve(treeData.value);
  }

  const parent = node.data as TreeNode;
  if (!parent.isDirectory) return resolve([]);

  try {
    const fileList = await window.ipcRenderer.invoke(
      "fs:readDirectory",
      parent.path
    );
    const children: TreeNode[] = fileList.map((file: any) => ({
      label: file.name,
      path: file.path,
      isDirectory: file.isDirectory,
      isLeaf: !file.isDirectory
    }));
    resolve(children);
  } catch (err) {
    console.error("Error loading child directory:", err);
    resolve([]);
  }
};

// Handle node expand
const handleNodeExpand = (data: TreeNode) => {
  if (data.isDirectory) {
    workspaceStore.addExpandedNode(data.path);
  }
};

// Handle node collapse
const handleNodeCollapse = (data: TreeNode) => {
  if (data.isDirectory) {
    workspaceStore.removeExpandedNode(data.path);
  }
};

// Handle node click
const handleNodeClick = async (data: TreeNode, node: any, component: any) => {
  if (data.isDirectory) {
    // Toggle expand/collapse on single click for directories
    if (node.expanded) {
      node.collapse();
    } else {
      node.expand();
    }
    emit('directorySelected', data.path);
  } else {
    console.log('File clicked in workspace:', data.path);

    const currentFilePath = workspaceStore.getCurrentFilePath;

    // 如果点击的是当前文件，不需要切换
    if (currentFilePath === data.path) {
      return;
    }

    // 只有在已经有当前文件且该文件有修改时才提示保存
    // 如果没有当前文件（比如从首页打开），直接切换
    if (currentFilePath &&
        workspaceStore.isDataModified(currentFilePath) &&
        workspaceStore.isExcelFile(currentFilePath)) {
      try {
        await ElMessageBox.confirm(
          `文件 "${currentFilePath.split(/[/\\]/).pop()}" 已被修改，是否要保存更改？`,
          '确认切换文件',
          {
            confirmButtonText: '保存并切换',
            cancelButtonText: '不保存',
            distinguishCancelAndClose: true,
            type: 'warning'
          }
        );

        // 用户选择保存，触发导出操作
        window.dispatchEvent(new CustomEvent('export-current-file'));
        // 等待导出完成后再切换
        setTimeout(() => {
          switchToFile(data.path);
        }, 500);
      } catch (action) {
        if (action === 'cancel') {
          // 用户选择不保存，直接切换
          switchToFile(data.path);
        } else {
          // 用户点击了X或按了ESC，取消切换操作
          return;
        }
      }
    } else {
      // 没有修改或没有当前文件，直接切换
      switchToFile(data.path);
    }
  }
};

// 切换到指定文件
const switchToFile = (filePath: string) => {
  // 显示文件切换加载动画
  const loadingInstance = ElLoading.service({
    fullscreen: true,
    text: `正在打开文件: ${filePath.split(/[/\\]/).pop()}`,
    background: "rgba(0, 0, 0, 0.7)",
  });

  // 使用 setTimeout 确保加载动画能够显示
  setTimeout(() => {
    try {
      // Set current file in store
      workspaceStore.setCurrentFile(filePath);

      // Check if file is an Excel file and navigate accordingly
      if (workspaceStore.isExcelFile(filePath)) {
        // For Excel files, navigate to dataImandEx page
        workspaceStore.markDataAsSaved(filePath);
        router.push('/dataManagement/imandex');
      } else {
        // For other files, emit the selection event
        emit('fileSelected', filePath);
      }
    } finally {
      // 延迟关闭加载动画，确保用户能看到切换效果
      setTimeout(() => {
        loadingInstance.close();
      }, 300);
    }
  }, 100);
};

// Watch for workspace path changes and auto-load
watch(currentWorkspacePath, async (newPath, oldPath) => {
  if (newPath && newPath !== oldPath) {
    await loadWorkspace();
  }
}, { immediate: true });

// Watch for single file mode changes
watch(() => workspaceStore.singleFileMode, async (newMode, oldMode) => {
  if (newMode !== oldMode) {
    console.log("Single file mode changed:", { newMode, oldMode });
    await loadWorkspace();
  }
}, { immediate: false });

// Watch for sidebar collapse/expand to restore tree state
watch(isCollapse, async (newCollapse, oldCollapse) => {
  if (oldCollapse && !newCollapse) {
    // Sidebar is expanding, restore expanded state
    console.log("Sidebar expanding, restoring tree state");
    await nextTick(() => {
      // Restore expanded nodes after tree is rendered
      const expandedNodes = workspaceStore.getExpandedNodes;
      if (expandedNodes.length > 0 && treeRef.value) {
        expandedNodes.forEach((nodePath: string) => {
          const node = treeRef.value.getNode(nodePath);
          if (node && node.data.isDirectory) {
            node.expand();
          }
        });
      }
    });
  }
}, { immediate: false });

// 简化watch逻辑，避免不必要的重新加载导致目录收起
// 只在真正需要重新构建树结构时才调用loadWorkspace



// Listen for menu-triggered events
onMounted(() => {
  console.log("WorkspaceFileTree mounted");
  console.log("Current workspace path:", currentWorkspacePath.value);
  console.log("Single file mode:", workspaceStore.singleFileMode);
  console.log("Current file path:", currentSelectedFile.value);

  // Listen for menu-triggered import project
  window.ipcRenderer.on("menu-triggered-import-project", async () => {
    await selectWorkspaceDirectory();
  });

  // Load workspace if path is already set or if in single file mode
  if (currentWorkspacePath.value || workspaceStore.singleFileMode) {
    console.log("Loading workspace on mount");
    // Add a small delay to ensure all state is properly initialized
    setTimeout(() => {
      loadWorkspace();
    }, 100);
  }
});

onBeforeUnmount(() => {
  window.ipcRenderer.removeAllListeners("menu-triggered-import-project");
});

// Expose methods for parent component
defineExpose({
  selectWorkspaceDirectory,
  refreshWorkspace,
  currentWorkspacePath
});
</script>

<style scoped>
.workspace-file-tree {
  height: calc(100vh - 100px); /* 减去顶部导航栏和标签栏的高度 */
  max-height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  background: #ffffff;
  color: #303133;
  border-right: 1px solid #e4e7ed;
  overflow: hidden;
}

/* 深色模式适配 */
html.dark .workspace-file-tree {
  background: var(--el-bg-color);
  color: var(--el-text-color-primary);
  border-right: 1px solid var(--pure-border-color);
}

.workspace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
}

/* 深色模式适配 */
html.dark .workspace-header {
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--pure-border-color);
}

/* 侧边栏收起状态样式 */
.workspace-header.collapsed {
  justify-content: center;
  padding: 8px 4px;
}

.workspace-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #606266;
}

/* 深色模式适配 */
html.dark .workspace-title {
  color: var(--el-text-color-primary);
}

.workspace-actions {
  display: flex;
  gap: 4px;
}

.workspace-actions .el-button {
  color: #909399;
  padding: 4px;
  min-height: auto;
}

/* 深色模式适配 */
html.dark .workspace-actions .el-button {
  color: var(--el-text-color-primary);
}

.workspace-actions .el-button:hover {
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.file-tree-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 8px 0;
  min-height: 0; /* 确保flex子元素可以正确收缩 */
  max-height: calc(100vh - 150px); /* 为文件树内容设置最大高度 */
}

.file-tree-container::-webkit-scrollbar {
  width: 6px;
}

.file-tree-container::-webkit-scrollbar-track {
  background: transparent;
}

.file-tree-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.file-tree-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.empty-workspace,
.empty-directory,
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;
  font-size: 12px;
}

/* 深色模式适配 */
html.dark .empty-workspace,
html.dark .empty-directory,
html.dark .loading-container {
  color: var(--el-text-color-primary);
}

.loading-container {
  gap: 8px;
}

/* 收起状态下的空状态和加载状态样式 */
.loading-container.collapsed,
.empty-directory.collapsed,
.empty-workspace.collapsed {
  padding: 12px 4px;
}

.empty-icon {
  color: #909399;
}

/* 深色模式适配 */
html.dark .empty-icon {
  color: var(--el-text-color-primary);
}

/* 上传按钮样式 */
.upload-button {
  color: #409eff !important;
  padding: 8px !important;
  min-height: auto !important;
}

.upload-button:hover {
  color: #66b1ff !important;
  background-color: rgba(64, 158, 255, 0.1) !important;
}

/* 深色模式适配 */
html.dark .upload-button {
  color: #409eff !important;
}

html.dark .upload-button:hover {
  color: #66b1ff !important;
  background-color: rgba(64, 158, 255, 0.2) !important;
}

.workspace-tree {
  background: transparent;
  color: #303133;
}

/* 深色模式适配 */
html.dark .workspace-tree {
  color: var(--el-text-color-primary);
}

/* 文件树收起状态样式 */
.workspace-tree.collapsed :deep(.el-tree-node__content) {
  justify-content: center;
  padding: 0 !important;
  margin: 2px 0;
  height: 32px;
  display: flex !important;
  align-items: center;
  width: 100%;
}

.workspace-tree.collapsed :deep(.el-tree-node__expand-icon) {
  display: none !important;
  width: 0 !important;
  margin: 0 !important;
}

/* 收起状态下的树节点间距调整 */
.workspace-tree.collapsed :deep(.el-tree-node) {
  margin: 0;
  width: 100%;
}

.workspace-tree.collapsed :deep(.el-tree-node__children) {
  display: none;
}

/* 强制隐藏收起状态下的所有文字内容 */
.workspace-tree.collapsed :deep(.el-tree-node__label) {
  display: none !important;
}

.workspace-tree.collapsed .node-label {
  display: none !important;
}

/* 确保所有可能的文字元素都被隐藏 */
.workspace-tree.collapsed :deep(.el-tree-node__content) span {
  display: none !important;
}

.workspace-tree.collapsed :deep(.el-tree-node__content) .tree-node span {
  display: none !important;
}

/* 只保留图标显示 */
.workspace-tree.collapsed :deep(.el-tree-node__content) .el-icon {
  display: inline-flex !important;
}

.workspace-tree.collapsed :deep(.el-tree-node__content) .node-icon {
  display: inline-flex !important;
}

/* 强制居中对齐，消除任何可能的偏移 */
.workspace-tree.collapsed :deep(.el-tree-node__content) {
  text-align: center !important;
  position: relative !important;
}

.workspace-tree.collapsed :deep(.el-tree-node__content) > * {
  position: absolute !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
}

/* Tooltip在收起状态下的样式优化 */
.workspace-tree.collapsed :deep(.el-tooltip__trigger) {
  position: absolute !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.el-tree-node__content) {
  background: transparent;
  color: #303133;
  height: 28px;
  padding-left: 8px;
}

/* 深色模式适配 */
html.dark :deep(.el-tree-node__content) {
  color: var(--el-text-color-primary);
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

/* 深色模式适配 */
html.dark :deep(.el-tree-node__content:hover) {
  background-color: var(--el-fill-color-light);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #e6f7ff;
  color: #409eff;
}

/* 深色模式适配 */
html.dark :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(64, 158, 255, 0.25);
  color: #000000;
}

/* 深色模式下选中状态的图标和文字颜色 */
html.dark :deep(.el-tree-node.is-current > .el-tree-node__content) .tree-node {
  color: #000000;
}

html.dark :deep(.el-tree-node.is-current > .el-tree-node__content) .node-icon {
  color: #000000;
}

html.dark :deep(.el-tree-node.is-current > .el-tree-node__content) .node-label {
  color: #000000;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

/* 深色模式适配 */
html.dark .tree-node {
  color: var(--el-text-color-primary);
}

/* 树节点收起状态样式 */
.tree-node.collapsed {
  justify-content: center !important;
  gap: 0;
  width: 100% !important;
  display: flex !important;
  align-items: center;
  padding: 0 !important;
  margin: 0 !important;
}

.tree-node.collapsed .node-icon {
  margin: 0 auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
}

.file-modified-indicator {
  color: #f56c6c;
  font-size: 10px;
  font-weight: bold;
  margin-right: 2px;
}

.node-icon {
  color: #909399;
}

/* 深色模式适配 */
html.dark .node-icon {
  color: var(--el-text-color-primary);
}

.node-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 深色模式适配 */
html.dark .node-label {
  color: var(--el-text-color-primary);
}

:deep(.el-tree-node__expand-icon) {
  color: #c0c4cc;
}

/* 深色模式适配 */
html.dark :deep(.el-tree-node__expand-icon) {
  color: var(--el-text-color-regular);
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}
</style>
