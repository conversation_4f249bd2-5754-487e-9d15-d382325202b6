<template>
  <div class="data-table-container">
    <!-- 工具栏插槽 -->
    <div v-if="$slots.toolbar" class="data-table-toolbar">
      <slot
        name="toolbar"
        :table-instance="tableInstance"
        :has-data="hasData"
      />
    </div>

    <!-- 表格主体 -->
    <div
      class="data-table-body"
      :class="{ 'sidebar-animating': isSidebarAnimating }"
      :style="containerStyle"
    >
      <!-- 始终渲染TableContent以确保ready事件能够触发 -->
      <TableContent
        ref="tableContentRef"
        :key="tableKey"
        :settings="mergedSettings"
        :loading="loading"
        @ready="handleTableReady"
        @change="handleDataChange"
      />
      <!-- 当没有数据时显示空状态覆盖层 -->
      <EmptyState
        v-if="!hasData && !loading"
        :description="emptyText"
        :image="emptyImage"
        @action="$emit('empty-action')"
        style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 10;"
      >
        <template v-if="$slots.empty" #default>
          <slot name="empty" />
        </template>
      </EmptyState>
    </div>

    <!-- 底部插槽 -->
    <div v-if="$slots.footer" class="data-table-footer">
      <slot name="footer" :table-instance="tableInstance" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  ref,
  computed,
  watch,
  onMounted,
  onBeforeUnmount,
  nextTick,
} from "vue";
import { emitter } from "@/utils/mitt";
import TableContent from "./components/TableContent.vue";
import EmptyState from "./components/EmptyState.vue";
import { useTable } from "./composables/useTable";
import { useTableResize } from "./composables/useTableResize";
import type { DataTableProps, DataTableEmits, TableInstance } from "./types";

// Props 定义
const props = withDefaults(defineProps<DataTableProps>(), {
  data: () => [],
  columns: () => [],
  height: "auto",
  loading: false,
  emptyText: "暂无数据",
  autoResize: true,
  settings: () => ({}),
});

// Emits 定义
const emit = defineEmits<DataTableEmits>();

// Refs
const tableContentRef = ref();
const tableInstance = ref<TableInstance>();
const tableKey = ref(0);
const isDestroyed = ref(false);
const isModified = ref(false);
const originalData = ref<any[][]>([]);
const isSidebarAnimating = ref(false);

// 使用组合式函数
const {
  mergedSettings,
  hasData,
  updateData,
  updateColumns,
  getData,
  getColumns,
  exportData,
  importData,
} = useTable(props);

// 容器样式
const containerStyle = computed(() => ({
  height: props.height === "auto" ? "auto" : props.height,
  width: "100%",
}));

// 自动调整尺寸
const { startObserving, stopObserving, updateDimensions, pauseResize, resumeResize } = useTableResize(
  tableContentRef,
  props.autoResize,
);

// 处理表格准备就绪
const handleTableReady = (instance: any) => {
  if (isDestroyed.value) return;

  tableInstance.value = {
    hotInstance: instance,
    getData,
    getColumns,
    updateData,
    updateColumns,
    exportData,
    importData,
    refresh: () => {
      if (!isDestroyed.value && instance && !instance.isDestroyed) {
        instance.render();
      }
    },
    destroy: () => {
      if (!isDestroyed.value && instance && !instance.isDestroyed) {
        instance.destroy();
      }
    },
  };
  emit("ready", tableInstance.value);
};

// 处理数据变化
const handleDataChange = (changes: any) => {
  if (changes && changes.length > 0) {
    isModified.value = true;
  }
  emit("change", changes);
};

// 重置修改状态
const resetModifiedState = () => {
  isModified.value = false;
  if (tableInstance.value) {
    originalData.value = JSON.parse(JSON.stringify(tableInstance.value.getData()));
  }
};

// 检查是否有修改
const hasModifications = () => {
  return isModified.value;
};

// 强制更新表格
const forceUpdate = () => {
  if (tableInstance.value?.hotInstance && !tableInstance.value.hotInstance.isDestroyed) {
    try {
      tableInstance.value.hotInstance.render();
    } catch (error) {
      console.warn('Force update failed:', error);
      // 如果渲染失败，尝试重新创建表格
      tableKey.value++;
    }
  } else {
    // 表格实例不存在时重新创建
    tableKey.value++;
  }
};

// 优化数据变化监听 - 避免频繁重新创建表格
watch(
  () => props.data,
  async (newData, oldData) => {
    // 使用更高效的比较方式
    const dataChanged = newData !== oldData &&
      (newData?.length !== oldData?.length ||
       newData?.some((row, i) => row !== oldData?.[i]));

    if (dataChanged) {
      // 优化数据更新策略
      const shouldRecreateTable = !tableInstance.value?.hotInstance ||
                                  tableInstance.value.hotInstance.isDestroyed ||
                                  (newData && newData.length > 5000); // 大数据集时重新创建

      if (!shouldRecreateTable) {
        try {
          // 使用更高效的批量更新
          const hotInstance = tableInstance.value.hotInstance;

          // 暂停渲染以提升性能
          hotInstance.suspendRender();

          try {
            // 批量更新数据和设置
            hotInstance.batch(() => {
              // 如果数据为空，清空表格
              if (!newData || newData.length === 0) {
                hotInstance.loadData([]);
              } else {
                // 分批加载大数据集
                if (newData.length > 1000) {
                  // 先加载前1000行
                  hotInstance.loadData(newData.slice(0, 1000));
                  // 异步加载剩余数据
                  setTimeout(() => {
                    if (!hotInstance.isDestroyed) {
                      hotInstance.loadData(newData);
                    }
                  }, 100);
                } else {
                  hotInstance.loadData(newData);
                }
              }
            });
          } finally {
            // 恢复渲染
            hotInstance.resumeRender();
          }
        } catch (error) {
          console.warn('Failed to update data via instance, recreating table:', error);
          tableKey.value++; // 仅在实例更新失败时重新创建
          await nextTick();
        }
      } else {
        tableKey.value++; // 表格实例不存在或大数据集时重新创建
        await nextTick();
      }

      updateData(newData);
      // 重置修改状态，因为这是新数据
      isModified.value = false;
      originalData.value = newData ? JSON.parse(JSON.stringify(newData)) : [];
    }
  },
  { deep: false }, // 改为浅层监听以提升性能
);

watch(
  () => props.columns,
  async (newColumns, oldColumns) => {
    // 使用更高效的比较方式
    const columnsChanged = newColumns !== oldColumns &&
      (newColumns?.length !== oldColumns?.length ||
       newColumns?.some((col, i) => col !== oldColumns?.[i]));

    if (columnsChanged) {
      // 优先使用实例更新
      if (tableInstance.value?.hotInstance && !tableInstance.value.hotInstance.isDestroyed) {
        try {
          const colHeaders = newColumns?.map(col => col.title || col.data) || [];
          const columns = newColumns?.map(col => ({
            data: col.data,
            type: col.type || 'text',
            readOnly: col.readOnly || false,
            width: col.width,
            ...col.settings,
          })) || [];

          // 批量更新设置
          tableInstance.value.hotInstance.batch(() => {
            tableInstance.value.hotInstance.updateSettings({
              colHeaders,
              columns,
            });
          });
        } catch (error) {
          console.warn('Failed to update columns via instance, recreating table:', error);
          tableKey.value++; // 仅在实例更新失败时重新创建
          await nextTick();
        }
      } else {
        tableKey.value++; // 表格实例不存在时重新创建
        await nextTick();
      }

      updateColumns(newColumns);
    }
  },
  { deep: false }, // 改为浅层监听以提升性能
);

// 生命周期
onMounted(() => {
  isDestroyed.value = false;
  if (props.autoResize) {
    startObserving();
  }

  // 监听侧边栏切换事件
  emitter.on("pauseTableResize", () => {
    isSidebarAnimating.value = true;
    pauseResize();
  });
  emitter.on("resumeTableResize", () => {
    isSidebarAnimating.value = false;
    resumeResize();
  });
});

onBeforeUnmount(() => {
  isDestroyed.value = true;
  stopObserving();

  // 清理事件监听
  emitter.off("pauseTableResize");
  emitter.off("resumeTableResize");

  // 安全销毁
  if (tableInstance.value && tableInstance.value.hotInstance) {
    try {
      const hotInstance = tableInstance.value.hotInstance;
      if (!hotInstance.isDestroyed) {
        hotInstance.destroy();
      }
    } catch (error) {
      // 忽略销毁错误
      console.warn("Table destroy error:", error);
    }
  }
});

// 暴露方法
defineExpose({
  tableInstance,
  updateDimensions,
  refresh: () => tableInstance.value?.refresh(),
  forceUpdate, // 使用优化后的forceUpdate方法
  hasModifications,
  resetModifiedState,
  isModified: () => isModified.value,
  pauseResize,
  resumeResize,
});
</script>

<style scoped>
.data-table-body {
  position: relative;
}

/* 侧边栏动画期间的性能优化 */
.data-table-body.sidebar-animating {
  pointer-events: none; /* 暂时禁用交互 */
}

.data-table-body.sidebar-animating :deep(.handsontable) {
  /* 在动画期间减少重绘 */
  transform: translateZ(0); /* 启用硬件加速 */
  backface-visibility: hidden; /* 隐藏背面 */
}
</style>
