{"name": "<PERSON><PERSON>", "displayName": "Lasso回归", "description": "带L1正则化的线性回归，具有特征选择功能", "category": "linear", "type": "regression", "defaultParams": {"alpha": {"value": 1.0, "type": "number", "description": "正则化强度，值越大正则化越强", "displayName": "正则化参数(α)", "min": 0.001, "max": 100, "step": 0.001}, "fit_intercept": {"value": true, "type": "boolean", "description": "是否计算截距项", "displayName": "计算截距"}, "positive": {"value": false, "type": "boolean", "description": "是否强制系数为正数", "displayName": "强制正系数"}, "random_state": {"value": null, "type": "number", "description": "随机种子", "displayName": "随机种子", "min": 0, "max": 1000, "nullable": true}, "selection": {"value": "cyclic", "type": "select", "description": "坐标下降算法中特征选择的策略", "displayName": "特征选择策略", "options": [{"value": "cyclic", "label": "循环选择"}, {"value": "random", "label": "随机选择"}]}}, "evaluation": {"splitDataset": {"value": false, "description": "是否划分训练/测试集"}, "trainRatio": {"value": 70, "min": 50, "max": 90, "step": 5, "description": "训练集比例(%)"}, "randomState": {"value": 42, "min": 0, "max": 1000, "description": "随机种子"}, "useModelValidation": {"value": false, "description": "是否使用交叉验证"}, "validationType": {"value": "k-fold", "options": ["k-fold", "leave-one-out"], "description": "验证方法"}, "kFolds": {"value": 5, "min": 2, "max": 10, "description": "k折交叉验证的k值"}}, "tips": ["Lasso回归通过L1正则化实现自动特征选择", "α值越大，越多特征的系数会被压缩为0", "适用于高维数据和需要特征选择的场景", "建议通过交叉验证选择最优的α值", "当特征数量大于样本数量时，Lasso表现更好"], "introduction": {"detailedDescription": "Lasso回归通过在损失函数中加入L1正则化项来实现特征选择。L1正则化会将不重要特征的系数压缩为零，从而自动进行特征选择。这使得Lasso特别适用于高维数据和稀疏模型的构建。", "usageTips": ["适用于需要特征选择的高维数据", "通过L1正则化自动筛选重要特征", "α参数控制特征选择的强度", "结果具有稀疏性，易于解释"], "scenarios": "适用于需要特征选择的高维数据，通过L1正则化自动筛选重要特征。", "mainParams": [{"name": "alpha", "description": "L1正则化强度参数"}, {"name": "fit_intercept", "description": "是否计算截距项"}, {"name": "max_iter", "description": "最大迭代次数"}]}}