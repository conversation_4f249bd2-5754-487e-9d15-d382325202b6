const Layout = () => import("@/layout/index.vue");

export default [
  {
    path: "/login",
    name: "<PERSON><PERSON>",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "登录",
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/visualize",
    name: "dataVisualize",
    component: () => import("@/views/data/visualization/dataVisualize/index.vue"),
    meta: {
      title: "数据可视化",
      showLink: false,
    },
  },
  {
    path: "/modelManagement/lmResult",
    name: "LMModelResult",
    component: () => import("@/components/modelManagement/src/linearModelDialog/lmModelResult.vue"),
    meta: {
      title: "模型结果",
      showLink: false, // 在菜单中隐藏此路由
    },
  },
  {
    path: "/modelManagement/mlResult",
    name: "MLModelResult",
    component: () => import("@/components/modelManagement/src/mlModelDialog/mlModelResult.vue"),
    meta: {
      title: "机器学习模型结果",
      showLink: false, // 在菜单中隐藏此路由
    },
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      title: "加载中...",
      showLink: false,
      rank: 102
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      }
    ]
  }
] satisfies Array<RouteConfigsTable>;
