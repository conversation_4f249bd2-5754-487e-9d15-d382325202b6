<template>
  <div class="metrics-table">
    <!-- 表格展示 -->
    <div v-if="showTable && metricsTableData.length > 0">
      <el-table :data="metricsTableData" border style="width: 100%">
        <el-table-column
          v-for="col in tableColumns"
          :key="col.prop"
          v-bind="col"
        />
      </el-table>
    </div>

    <!-- 空状态 -->
    <el-empty v-if="!metricsTableData.length && !metricsCards.length" description="暂无评估指标数据" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { QuestionFilled } from '@element-plus/icons-vue';

interface Props {
  modelResult: any;
  showCards?: boolean;
  showTable?: boolean;
  cardSpan?: number;
  datasetConfig?: {
    order: readonly string[];
    titles: Record<string, string>;
  };
}

const props = withDefaults(defineProps<Props>(), {
  showCards: true,
  showTable: true,
  cardSpan: 6,
  datasetConfig: () => ({
    order: ["train", "test", "cv", "validation"] as const,
    titles: {
      train: "训练集",
      test: "测试集",
      cv: "交叉验证",
      validation: "验证集"
    }
  })
});

// 指标名称映射
const metricNameMap: Record<string, { name: string; description?: string }> = {
  'accuracy': { name: '准确率', description: '正确预测的样本数占总样本数的比例' },
  'precision': { name: '精确率', description: '预测为正例中实际为正例的比例' },
  'recall': { name: '召回率', description: '实际正例中被正确预测为正例的比例' },
  'f1_score': { name: 'F1分数', description: '精确率和召回率的调和平均数' },
  'mse': { name: '均方误差', description: '预测值与真实值差值平方的平均数' },
  'rmse': { name: '均方根误差', description: '均方误差的平方根' },
  'mae': { name: '平均绝对误差', description: '预测值与真实值差值绝对值的平均数' },
  'r2_score': { name: 'R²分数', description: '决定系数，衡量模型对数据变异的解释程度' },
  'auc': { name: 'AUC', description: 'ROC曲线下的面积' },
  'log_loss': { name: '对数损失', description: '逻辑回归的损失函数' }
};

// 可用数据集
const availableDatasets = computed(() => {
  if (!props.modelResult?.eval) return [];
  return props.datasetConfig.order.filter(key => props.modelResult.eval[key]);
});

// 表格列定义
const tableColumns = computed(() => [
  { prop: "metric", label: "指标", width: 120 },
  ...availableDatasets.value.map((key) => ({
    prop: key,
    label: props.datasetConfig.titles[key],
  })),
]);

// 表格数据
const metricsTableData = computed(() => {
  const evalData = props.modelResult?.eval;
  if (!evalData) return [];
  
  const metrics = new Set<string>();
  Object.values(evalData).forEach((dataset: any) => {
    if (dataset?.metrics) {
      Object.keys(dataset.metrics).forEach((metric) => metrics.add(metric));
    }
  });
  
  return Array.from(metrics).map((metric) => ({
    metric: metricNameMap[metric]?.name || metric,
    ...availableDatasets.value.reduce(
      (acc, key) => ({
        ...acc,
        [key]: evalData[key]?.metrics?.[metric]?.toFixed(4) ?? "-",
      }),
      {},
    ),
  }));
});

// 卡片数据（使用第一个可用数据集的指标）
const metricsCards = computed(() => {
  if (!props.modelResult?.eval || availableDatasets.value.length === 0) return [];
  
  const firstDataset = availableDatasets.value[0];
  const metrics = props.modelResult.eval[firstDataset]?.metrics;
  
  if (!metrics) return [];
  
  return Object.entries(metrics).map(([key, value]) => ({
    key,
    title: metricNameMap[key]?.name || key,
    description: metricNameMap[key]?.description,
    value: Number(value),
    precision: getMetricPrecision(key),
    suffix: getMetricSuffix(key)
  }));
});

const getMetricPrecision = (metricKey: string): number => {
  // 根据指标类型设置精度
  const precisionMap: Record<string, number> = {
    'accuracy': 4,
    'precision': 4,
    'recall': 4,
    'f1_score': 4,
    'r2_score': 4,
    'auc': 4,
    'mse': 6,
    'rmse': 6,
    'mae': 6,
    'log_loss': 6
  };
  return precisionMap[metricKey] || 4;
};

const getMetricSuffix = (metricKey: string): string => {
  // 根据指标类型设置后缀
  const suffixMap: Record<string, string> = {
    'accuracy': '%',
    'precision': '%',
    'recall': '%'
  };
  return suffixMap[metricKey] || '';
};
</script>

<style scoped>
.metrics-table {
  width: 100%;
}

.metrics-cards {
  margin-bottom: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
