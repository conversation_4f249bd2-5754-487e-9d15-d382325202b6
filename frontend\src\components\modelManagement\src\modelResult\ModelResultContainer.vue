<template>
  <div class="model-result-container">
    <!-- 模型基本信息 -->
    <ModelInfoCard 
      :model-result="modelResult"
      :column-count="infoColumnCount"
      :show-evaluation-method="showEvaluationMethod"
      :custom-fields="customFields"
    />

    <!-- 内容展示tabs -->
    <el-tabs type="border-card" v-model="activeTab">
      <!-- 评估指标标签页 -->
      <el-tab-pane label="评估指标" name="metrics">
        <MetricsTable 
          :model-result="modelResult"
          :show-cards="showMetricsCards"
          :show-table="showMetricsTable"
          :card-span="metricsCardSpan"
          :dataset-config="datasetConfig"
        />
      </el-tab-pane>

      <!-- 预测结果图表标签页 -->
      <el-tab-pane label="预测结果图表" name="charts">
        <ModelCharts 
          :model-result="modelResult"
          :chart-type="chartType"
          :dataset-config="datasetConfig"
        />
      </el-tab-pane>

      <!-- 预测数据标签页 -->
      <el-tab-pane label="预测数据" name="predictions">
        <PredictionTable 
          :model-result="modelResult"
          :show-error-rate="showErrorRate"
          :dataset-config="datasetConfig"
        />
      </el-tab-pane>

      <!-- 自定义标签页 -->
      <el-tab-pane 
        v-for="tab in customTabs" 
        :key="tab.name"
        :label="tab.label" 
        :name="tab.name"
      >
        <component :is="tab.component" :model-result="modelResult" v-bind="tab.props" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ModelInfoCard from './ModelInfoCard.vue';
import MetricsTable from './MetricsTable.vue';
import ModelCharts from './ModelCharts.vue';
import PredictionTable from './PredictionTable.vue';

interface CustomField {
  key: string;
  label: string;
  formatter?: (value: any) => string;
}

interface CustomTab {
  name: string;
  label: string;
  component: any;
  props?: Record<string, any>;
}

interface Props {
  modelResult: any;
  // 模型信息配置
  infoColumnCount?: number;
  showEvaluationMethod?: boolean;
  customFields?: CustomField[];
  // 指标配置
  showMetricsCards?: boolean;
  showMetricsTable?: boolean;
  metricsCardSpan?: number;
  // 图表配置
  chartType?: 'scatter' | 'line' | 'bar';
  // 预测数据配置
  showErrorRate?: boolean;
  // 数据集配置
  datasetConfig?: {
    order: readonly string[];
    titles: Record<string, string>;
  };
  // 自定义标签页
  customTabs?: CustomTab[];
  // 默认激活的标签页
  defaultActiveTab?: string;
}

const props = withDefaults(defineProps<Props>(), {
  infoColumnCount: 3,
  showEvaluationMethod: true,
  customFields: () => [],
  showMetricsCards: true,
  showMetricsTable: true,
  metricsCardSpan: 6,
  chartType: 'scatter',
  showErrorRate: true,
  datasetConfig: () => ({
    order: ["train", "test", "cv", "validation"] as const,
    titles: {
      train: "训练集",
      test: "测试集",
      cv: "交叉验证",
      validation: "验证集"
    }
  }),
  customTabs: () => [],
  defaultActiveTab: 'metrics'
});

const activeTab = ref(props.defaultActiveTab);
</script>

<style scoped>
.model-result-container {
  padding: 20px;
  min-height: 100vh;
  background: var(--el-bg-color);
}

:deep(.el-tabs--card) {
  > .el-tabs__header {
    margin: 0;
  }
}
</style>
