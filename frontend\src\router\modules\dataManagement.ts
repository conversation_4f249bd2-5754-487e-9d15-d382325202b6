export default {
  path: "/dataManagement",
  // redirect: "/error/403",
  meta: {
    icon: "ri:information-line",
    // showLink: false,
    title: "数据集管理",
    rank: 9
  },
  children: [
    {
      path: "/dataManagement/imandex",
      name: "dataImandEx",
      component: () => import("@/views/data/management/dataImandEx/index.vue"),
      meta: {
        title: "数据集导入/导出"
      }
    },
    {
      path: "/dataManagement/modify",
      name: "dataModify",
      component: () => import("@/views/data/modification/dataModify/index.vue"),
      meta: {
        title: "数据集修改"
      }
    },
    // {
    //   path: "/error/500",
    //   name: "500",
    //   component: () => import("@/views/error/500.vue"),
    //   meta: {
    //     title: "500"
    //   }
    // }
  ]
} satisfies RouteConfigsTable;
