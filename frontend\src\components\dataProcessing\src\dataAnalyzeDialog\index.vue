<template>
  <el-dialog
    :model-value="dialogStore.dialogs.dataAnalyze"
    title="数据分析"
    width="80%"
    destroy-on-close
    @update:model-value="(val) => !val && dialogStore.hideDialog('dataAnalyze')"
  >
    <data-analyze :table-instance="tableInstance" />
  </el-dialog>
</template>

<script lang="ts" setup>
import { useDialogStore } from "@/store/modules/dialog";
import dataAnalyze from "./dataAnalyze.vue";
import type { TableInstance } from "../dataTable/types";

const props = defineProps<{
  tableInstance?: TableInstance;
}>();

const dialogStore = useDialogStore();
</script>
