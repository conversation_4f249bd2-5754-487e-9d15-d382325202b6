<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { emitter } from "@/utils/mitt";
import { useNav } from "@/layout/hooks/useNav";
import { responsiveStorageNameSpace } from "@/config";
import { storageLocal, isAllEmpty } from "@pureadmin/utils";
import { findRouteByPath, getParentPaths } from "@/router/utils";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from "vue";
import LaySidebarLogo from "../lay-sidebar/components/SidebarLogo.vue";
import LaySidebarItem from "../lay-sidebar/components/SidebarItem.vue";
import LaySidebarLeftCollapse from "../lay-sidebar/components/SidebarLeftCollapse.vue";
import LaySidebarCenterCollapse from "../lay-sidebar/components/SidebarCenterCollapse.vue";
import WorkspaceFileTree from "@/components/fileManagement/src/WorkspaceFileTree/index.vue";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";
import { ElIcon } from 'element-plus';
import { Folder, Menu as IconMenu, CaretRight, Select as IconSelect, Setting } from '@element-plus/icons-vue';

// Icons are now component instances
// const workspaceIconName = "ri:folder-3-line";
// const navigationIconName = "ri:list-check-2";
// const modificationDotIconName = "ri:checkbox-blank-circle-fill";

const route = useRoute();
const router = useRouter();
const workspaceStore = useWorkspaceStoreHook();
const isShow = ref(false);
const showLogo = ref(
  storageLocal().getItem<StorageConfigs>(
    `${responsiveStorageNameSpace()}configure`
  )?.showLogo ?? true
);

const {
  device,
  pureApp,
  isCollapse,
  tooltipEffect,
  menuSelect,
  toggleSideBar,
  onPanel
} = useNav();

const subMenuData = ref([]);

// Use computed properties from store with defensive checks
const currentView = computed(() => {
  try {
    return workspaceStore.getCurrentView || 'workspace';
  } catch (error) {
    console.warn('Error accessing currentView:', error);
    return 'workspace';
  }
});

const isWorkspaceModified = computed(() => {
  try {
    return workspaceStore.getIsWorkspaceModified || false;
  } catch (error) {
    console.warn('Error accessing isWorkspaceModified:', error);
    return false;
  }
});

const currentWorkspacePath = computed(() => {
  try {
    return workspaceStore.getCurrentWorkspacePath || '';
  } catch (error) {
    console.warn('Error accessing currentWorkspacePath:', error);
    return '';
  }
});

const menuData = computed(() => {
  return pureApp.layout === "mix" && device.value !== "mobile"
    ? subMenuData.value
    : usePermissionStoreHook().wholeMenus;
});

const loading = computed(() =>
  pureApp.layout === "mix" ? false : menuData.value.length === 0 ? true : false
);

const defaultActive = computed(() =>
  !isAllEmpty(route.meta?.activePath) ? route.meta.activePath : route.path
);

function getSubMenuData() {
  let path = "";
  path = defaultActive.value;
  subMenuData.value = [];
  const parentPathArr = getParentPaths(
    path,
    usePermissionStoreHook().wholeMenus
  );
  const parenetRoute = findRouteByPath(
    parentPathArr[0] || path,
    usePermissionStoreHook().wholeMenus
  );
  if (!parenetRoute?.children) return;
  subMenuData.value = parenetRoute?.children;
}

// Workspace event handlers
const handleFileSelected = async (filePath: string) => {
  console.log('File selected:', filePath);

  // Check if it's an Excel file
  if (workspaceStore.isExcelFile(filePath)) {
    // For Excel files, navigate to dataImandEx page
    console.log('Excel file detected:', filePath);
    try {
      // 只在单文件模式下添加到工作区
      if (!workspaceStore.getCurrentWorkspacePath) {
        workspaceStore.addFileToWorkspace(filePath);
      }
      workspaceStore.setCurrentFile(filePath);
      workspaceStore.markDataAsSaved(filePath);
      await router.push('/dataManagement/imandex');
    } catch (error) {
      console.error('Error navigating to dataImandEx:', error);
    }
    return; // Exit early for Excel files
  } else {
    // For other files, set current file in store first (but don't mark as modified)
    workspaceStore.setCurrentFile(filePath);

    // Check if we need to navigate to workspace route
    const needsNavigation = !route.path.startsWith('/workspace/');

    console.log('File selection - needsNavigation:', needsNavigation, 'current route:', route.path);

    if (needsNavigation) {
      // Navigate to workspace route and wait for completion
      try {
        console.log('Navigating to workspace route...');
        await router.push(`/workspace/${encodeURIComponent(workspaceStore.getCurrentWorkspacePath)}`);
        console.log('Navigation completed, waiting for DOM update...');
        // Use nextTick to ensure DOM is updated
        await nextTick();
        // Additional wait to ensure component is fully mounted
        await new Promise(resolve => setTimeout(resolve, 300));
        console.log('DOM update completed');
      } catch (error) {
        console.error('Navigation error:', error);
      }
    }

    // Use a more reliable method to trigger file opening
    console.log('Triggering file opening for:', filePath);

    // Method 1: Store-based approach - set file and let workspace view react
    workspaceStore.setCurrentFile(filePath);

    // Method 2: Direct IPC with appropriate delay
    const delay = needsNavigation ? 500 : 150;
    setTimeout(() => {
      console.log('Sending IPC event for file:', filePath);
      window.ipcRenderer.send('workspace-file-selected', filePath);
    }, delay);

    // Method 3: Custom event as backup with longer delay
    setTimeout(() => {
      console.log('Sending custom event for file:', filePath);
      window.dispatchEvent(new CustomEvent('workspace-open-file', {
        detail: { filePath }
      }));
    }, delay + 100);

    // Method 4: Additional IPC event for redundancy
    setTimeout(() => {
      console.log('Sending additional IPC event for file:', filePath);
      window.ipcRenderer.send('workspace-file-selected', filePath);
    }, delay + 200);
  }
};

const handleDirectorySelected = (dirPath: string) => {
  console.log('Directory selected:', dirPath);
  // TODO: Handle directory selection (maybe expand in tree or show in breadcrumb)
};

const handleWorkspaceChanged = (workspacePath: string | null) => {
  console.log('Workspace changed:', workspacePath);

  // Navigate to workspace route if workspace path is provided
  if (workspacePath) {
    router.push(`/workspace/${encodeURIComponent(workspacePath)}`);
  }
};

watch(
  () => [route.path, usePermissionStoreHook().wholeMenus],
  () => {
    if (route.path.includes("/redirect")) return;
    getSubMenuData();
    menuSelect(route.path);
  }
);

onMounted(() => {
  getSubMenuData();
  emitter.on("logoChange", key => {
    showLogo.value = key;
  });

  // Initialize workspace store to ensure arrays are properly set up
  workspaceStore.initializeStore();

  // Initialize workspace view if there's a persisted workspace
  if (workspaceStore.hasWorkspace) {
    workspaceStore.setCurrentView('workspace');
  }

  // Example: Listen for workspace changes from main process
  // Assuming window.electron.ipcRenderer is set up in preload
  // window.electron.ipcRenderer.on('workspace-modified-status', (_event, modified: boolean) => {
  //   isWorkspaceModified.value = modified;
  // });
});

onBeforeUnmount(() => {
  emitter.off("logoChange");
  // window.electron.ipcRenderer.removeAllListeners('workspace-modified-status');
});

</script>

<template>
  <div
    v-loading="loading"
    :class="[
      'sidebar-container',
      showLogo ? 'has-logo' : 'no-logo',
      `view-${currentView}`
    ]"
    @mouseenter.prevent="isShow = true"
    @mouseleave.prevent="isShow = false"
  >
    <!-- Logo always at the top if showLogo is true - 隐藏 -->
    <!-- <LaySidebarLogo v-if="showLogo" :collapse="isCollapse" /> -->

    <!-- Icons below the logo - arranged horizontally -->
    <div class="sidebar-activity-icons">
      <button
        v-if="!isCollapse || currentView === 'workspace'"
        @click="workspaceStore.setCurrentView('workspace')"
        :class="{ active: currentView === 'workspace' }"
        title="工作区"
      >
        <span v-if="isWorkspaceModified" class="modification-dot">
          <el-icon :size="8"><IconSelect /></el-icon>
        </span>
        <el-icon :size="20"><Folder /></el-icon>
      </button>
      <button
        v-if="!isCollapse || currentView === 'navigation'"
        @click="workspaceStore.setCurrentView('navigation')"
        :class="{ active: currentView === 'navigation' }"
        title="导航"
      >
        <el-icon :size="20"><IconMenu /></el-icon>
      </button>
      <button
        v-if="!isCollapse"
        @click="onPanel"
        title="设置"
        class="settings-button"
      >
        <el-icon :size="20"><Setting /></el-icon>
      </button>
    </div>

    <!-- Navigation view, logo is already shown above if currentView is navigation -->
    <div v-show="currentView === 'navigation'" class="navigation-view">
      <el-scrollbar
        wrap-class="scrollbar-wrapper"
        :class="[device === 'mobile' ? 'mobile' : 'pc']"
      >
        <el-menu
          unique-opened
          mode="vertical"
          popper-class="pure-scrollbar"
          class="outer-most select-none"
          :collapse="isCollapse"
          :collapse-transition="false"
          :popper-effect="tooltipEffect"
          :default-active="defaultActive"
        >
          <LaySidebarItem
            v-for="routesItem in menuData"
            :key="routesItem.path"
            :item="routesItem"
            :base-path="routesItem.path"
            class="outer-most select-none"
          />
        </el-menu>
      </el-scrollbar>
    </div>

    <div v-show="currentView === 'workspace'" class="workspace-view">
      <WorkspaceFileTree
        @file-selected="handleFileSelected"
        @directory-selected="handleDirectorySelected"
        @workspace-changed="handleWorkspaceChanged"
      />
    </div>

    <!-- Collapse buttons might need adjustment based on the new layout -->
    <LaySidebarCenterCollapse
      v-if="device !== 'mobile' && (isShow || isCollapse)"
      :is-active="pureApp.sidebar.opened"
      @toggleClick="toggleSideBar"
    />
    <LaySidebarLeftCollapse
      v-if="device !== 'mobile'"
      :is-active="pureApp.sidebar.opened"
      @toggleClick="toggleSideBar"
    />
  </div>
</template>

<style scoped>
/* Styles for the activity icons section - horizontal layout */
.sidebar-activity-icons {
  display: flex;
  flex-direction: row; /* Horizontal layout */
  align-items: center;
  justify-content: flex-start; /* Align icons to the left */
  padding: 8px 10px;
  /* border-top: 1px solid #444; */ /* 去掉分割线 */
  margin-top: 10px; /* 增加顶部间距 */
  gap: 8px; /* Space between icons */
}

.sidebar-activity-icons button {
  background: none;
  border: none;
  color: #8a8a8a; /* Muted color for inactive icons */
  padding: 8px;
  margin: 0; /* Remove margin since we use gap */
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
  min-width: 36px;
  min-height: 36px;
}

/* 深色模式适配 */
html.dark .sidebar-activity-icons button {
  color: var(--el-text-color-regular);
}

.sidebar-activity-icons button:hover {
  background-color: rgba(64, 158, 255, 0.2); /* 改为蓝色半透明背景 */
  color: #409EFF; /* 改为蓝色文字 */
}

.sidebar-activity-icons button.active {
  color: #409EFF;
  background-color: rgba(64, 158, 255, 0.1);
  border-left: 2px solid #409EFF;
}

.sidebar-activity-icons button.settings-button {
  /* 设置按钮的特殊样式 */
  margin-left: auto; /* 将设置按钮推到右边 */
}

.sidebar-activity-icons button.settings-button:hover {
  background-color: rgba(64, 158, 255, 0.2);
  color: #409EFF;
}

.modification-dot {
  position: absolute;
  top: 0px;
  right: 0px;
  line-height: 0;
  color: #f56c6c;
}

/* Adjust existing styles if needed */
.sidebar-container {
  background: #304156; /* Default dark background, adjust as needed */
  /* Add transition for width if sidebar resizes */
}

/* 深色模式适配 */
html.dark .sidebar-container {
  background: var(--el-bg-color);
}

.navigation-view, .workspace-view {
  /* Ensure these views take up the remaining space */
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent scrollbars from overlapping */
}

/* Ensure el-scrollbar takes full height within its container */
.navigation-view .el-scrollbar {
  height: 100%;
}

:deep(.el-loading-mask) {
  opacity: 0.45;
}
</style>
