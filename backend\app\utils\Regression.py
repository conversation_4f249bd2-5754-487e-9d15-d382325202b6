import traceback

import numpy as np
from sklearn.linear_model import LinearRegression, Lasso, Ridge, ElasticNet
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.svm import SVR
from sklearn.pipeline import make_pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.neural_network import MLPRegressor
import json
import os
import pickle
import uuid
from typing import List, Tuple, Any, Dict
import xgboost as xgb

from .eval import cal_reg_metric, cv, score_dp_by_forward_holdout


def lasso(**param):
    return make_pipeline(StandardScaler(with_mean=False), Lasso(**param))


def ridge(**param):
    return make_pipeline(StandardScaler(with_mean=False), Ridge(**param))


def svr(**param):
    return make_pipeline(StandardScaler(with_mean=False), SVR(**param))


reg_model_config = {
    "RandomForestRegressor": {
        "model": RandomForestRegressor,
        "model_params": {
            "n_estimators": 100,
            "min_samples_split": 2,
            "random_state": 0
        },
        "model_params_type": {
            "n_estimators": "int",
            "min_samples_split": "int",
            "random_state": "int"
        },
        "hyper_param_search": {
            "n_estimators": [50, 100, 200, 500],
            "learning_rate": [0.5, 0.1, 0.05, 0.01],
        },
    },
    "GradientBoostingRegressor": {
        "model": GradientBoostingRegressor,
        "model_params": {
            "n_estimators": 100,
            "max_depth": 3,
            "random_state": 0
        },
        "model_params_type": {
            "n_estimators": "int",
            "max_depth": "int",
            "random_state": "int"
        },
        "hyper_param_search": {
            "n_estimators": [50, 100, 200, 500],
            "learning_rate": [0.5, 0.1, 0.05, 0.01]
        }
    },
    "LinearRegression": {
        "model": LinearRegression,
        "model_params": {
        },
        "model_params_type": {
        },
        "hyper_param_search": {
        }
    },

    "Lasso": {
        "model": lasso,
        "model_params": {
            "alpha": 1
        },
        "model_params_type": {
            "alpha": "float"
        },
        "hyper_param_search": {
            "alpha": [0.1, 1.0, 10.0]
        }
    },

    "Ridge": {
        "model": ridge,
        "model_params": {
            "alpha": 1
        },
        "model_params_type": {
            "alpha": "float"
        },
        "hyper_param_search": {
            "alpha": [0.1, 1.0, 10.0]
        }
    },

    "ElasticNet": {
        "model": ElasticNet,
        "model_params": {
            "alpha": 1,
            "l1_ratio": 0.5
        },
        "model_params_type": {
            "alpha": "float",
            "l1_ratio": "float"
        },
        "hyper_param_search": {
            "alpha": [0.1, 1.0, 10.0],
            "l1_ratio": [0.1, 0.5, 0.9]
        }
    },

    "SVR": {
        "model": svr,
        "model_params": {
            "C": 1,
            "kernel": "rbf",
        },
        "model_params_type": {
            "C": "float",
            "kernel": "list"
        },
        "hyper_param_search": {
            "C": [0.1, 1.0, 10.0, 100],
            "kernel": ["linear", "rbf"]
        }
    },
    "MLPRegressor": {
        "model": MLPRegressor,
        "model_params": {
            "solver": "adam",
            "max_iter": 200,
            "random_state": 0
        },
        "model_params_type": {
            "solver": "list",
            "max_iter": "list",
            "random_state": "int"
        },
        "hyper_param_search": {
            "solver": ["adam", "sgd"],
            "max_iter": [200, 300, 500]
        }
    },
    "XGBoost": {
        "model": xgb.XGBRegressor,
        "model_params": {
            "n_estimators": 100,
            "max_depth": 3,
            "learning_rate": 0.1,
            "random_state": 0
        },
        "model_params_type": {
            "n_estimators": "int",
            "max_depth": "int",
            "learning_rate": "float",
            "random_state": "int"
        },
        "hyper_param_search": {
            "n_estimators": [50, 100, 200],
            "max_depth": [3, 5, 7],
            "learning_rate": [0.1, 0.01, 0.001]
        }
    }
}


class Regression(object):
    def __init__(self, x_train, y_train, x_test=None, y_test=None, cv_fold=None):
        self.x_train = x_train
        self.y_train = y_train
        self.x_test = x_test
        self.y_test = y_test
        self.cv_fold = cv_fold
        self.model = None
        self.info = None  # model info

        # used for train models
        self.best_model = None
        self.best_model_info = None
        self.infos = []

    def train(self, alg_name="RandomForestRegressor", alg_param=None, **args):
        # check params
        if alg_name not in reg_model_config.keys():
            raise ValueError("Invalid algorithm name")
        alg = reg_model_config[alg_name]["model"]
        if alg_param is None:  # use default param
            params = reg_model_config[alg_name]["model_params"]
        else:
            params = reg_model_config[alg_name]["model_params"].copy()
            for key in alg_param.keys():
                params[key] = alg_param[key]
        reg_model = alg(**params)
        reg_model.fit(self.x_train, self.y_train)
        self.model = reg_model
        train_y_predict = list(np.array(self.model.predict(self.x_train), dtype=float))
        train_y_true = list(np.array(self.y_train, dtype=float))
        train_metrics_dict = cal_reg_metric(train_y_true, train_y_predict)

        # Serialize the model parameters as JSON
        model_params_json = json.dumps(params)
        self.info = {
            "task_type": "reg",
            "cv_fold": self.cv_fold,
            "alg": alg_name,
            "model_params": model_params_json,
            "eval": {
                "train": {
                    "y_true": train_y_true,
                    "y_predict": train_y_predict,
                    "metrics": train_metrics_dict
                }
            }
        }
        # cv
        if self.cv_fold is not None:
            cv_y_true, cv_y_predict = cv(self.model, self.x_train, self.y_train, k=self.cv_fold)
            cv_y_true = list(np.array(cv_y_true, dtype=float))  # make it serializable
            cv_y_predict = list(np.array(cv_y_predict, dtype=float))
            cv_metrics_dict = cal_reg_metric(cv_y_true, cv_y_predict)
            self.info["eval"]["cv"] = {
                "y_true": cv_y_true,
                "y_predict": cv_y_predict,
                "metrics": cv_metrics_dict
            }
        # test
        if self.x_test is not None:
            test_y_predict = list(np.array(self.model.predict(self.x_test), dtype=float))
            test_y_true = list(np.array(self.y_test, dtype=float))
            test_metrics_dict = cal_reg_metric(test_y_true, test_y_predict)
            self.info["eval"]["test"] = {
                "y_true": test_y_true,
                "y_predict": test_y_predict,
                "metrics": test_metrics_dict
            }
        return self.model, self.info

    def predict(self, x_test):
        return self.model.predict(x_test)

    def save(self, save_path):
        """save the object"""
        with open(save_path, "wb") as file:
            pickle.dump(self, file)

    @staticmethod
    def load(load_path):
        with open(load_path, "rb") as file:
            return pickle.load(file)

    def get_info(self):
        return self.info

    def get_model(self):
        return self.model

    def train_models(self, alg_names: List[str], models_params=None, scoring="RMSE"):
        """
        training models and select the best model

        Parameters
        ----------
        scoring:
        alg_names: ["SVR","LinearRegression"]
        models_params
        """
        if models_params is None:
            models_params = {}
        if self.cv_fold is None:
            raise ValueError("cv_fold must be set when training models")
        # TODO: 适应R2等越大越好的score  超参数优化 加入CatBoost 等其他模型
        best_rmse = float('inf')

        for alg_name in alg_names:
            if alg_name not in reg_model_config:
                print(f"警告: 未知算法 {alg_name}, 已跳过")
                continue
            try:
                model, model_info = self.train(alg_name=alg_name, alg_param=models_params.get(alg_name))
                self.infos.append(model_info)
                # check if better
                current_rmse = model_info['eval']['cv']['metrics'][scoring]
                if current_rmse < best_rmse:
                    best_rmse = current_rmse
                    self.best_model = model  # 更新最佳模型
                    self.best_model_info = model_info
            except Exception as e:
                print(f"训练算法 {alg_name} 时出错: {str(e)}")
                print(traceback.format_exc())
                continue

        return self.best_model, self.best_model_info, self.infos
