<template>
  <BaseParamsSetting
    ref="baseParamsRef"
    :algorithm-name="algorithmName"
    :model-config="modelConfig"
    :evaluation-config="evaluationConfig"
    :custom-params-placeholder="getCustomParamsPlaceholder()"
    @update:params="handleParamsUpdate"
  />
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { ModelMetaEvaluation, ModelAlgorithm } from "@/types/models";
import BaseParamsSetting from "../common/BaseParamsSetting.vue";
import type { ModelParamConfig } from "@/utils/modelParamsLoader";
import { getModelById } from "@/utils/dynamicModelLoader";

const emit = defineEmits<{
  (e: "update:params", value: {
    algorithm: ModelAlgorithm,
    evaluation: ModelMetaEvaluation,
  }): void;
}>();

const props = defineProps<{
  algorithmName: string;
}>();

// 状态管理
const loading = ref(false);
const modelConfig = ref<ModelParamConfig | null>(null);
const evaluationConfig = ref<Record<string, any>>({});
const baseParamsRef = ref();

// 加载模型配置
const loadModelConfiguration = async () => {
  if (!props.algorithmName) return;

  loading.value = true;
  try {
    // 直接使用算法名称作为配置文件名
    const configModule = await import(`@/config/modelParams/${props.algorithmName}.json`);
    const config = configModule.default;
    modelConfig.value = config;
    evaluationConfig.value = config.evaluation;

  } catch (error) {
    console.error("Failed to load model configuration:", error);
    ElMessage.error("加载模型配置失败");
  } finally {
    loading.value = false;
  }
};

// 处理参数更新
const handleParamsUpdate = (params: {
  algorithm: ModelAlgorithm;
  evaluation: ModelMetaEvaluation;
}) => {
  emit("update:params", params);
};

// 获取自定义参数占位符
const getCustomParamsPlaceholder = (): string => {
  const examples: Record<string, string> = {
    DecisionTreeRegressor: `请输入JSON格式的参数，例如：
{
  "max_depth": null,
  "min_samples_split": 2,
  "min_samples_leaf": 1,
  "criterion": "squared_error"
}`,
    RandomForestRegressor: `请输入JSON格式的参数，例如：
{
  "n_estimators": 100,
  "max_depth": null,
  "min_samples_split": 2,
  "random_state": 42
}`,
    XGBoost: `请输入JSON格式的参数，例如：
{
  "n_estimators": 100,
  "max_depth": 6,
  "learning_rate": 0.1,
  "random_state": 42
}`,
    GradientBoostingRegressor: `请输入JSON格式的参数，例如：
{
  "n_estimators": 100,
  "max_depth": 3,
  "learning_rate": 0.1,
  "random_state": 42
}`,
    SVR: `请输入JSON格式的参数，例如：
{
  "C": 1.0,
  "kernel": "rbf",
  "gamma": "scale",
  "epsilon": 0.1
}`,
    MLPRegressor: `请输入JSON格式的参数，例如：
{
  "hidden_layer_sizes": [100],
  "activation": "relu",
  "learning_rate_init": 0.001,
  "max_iter": 200
}`
  };

  return (
    examples[props.algorithmName] ||
    `请输入JSON格式的参数，例如：
{
  "param1": "value1",
  "param2": 123,
  "param3": true
}`
  );
};

// 监听算法名称变化
watch(
  () => props.algorithmName,
  () => {
    loadModelConfiguration();
  },
  { immediate: true }
);

// 重置参数的方法
const resetParams = async () => {
  console.log("resetParams called for ML model");

  // 重置状态
  modelConfig.value = null;
  evaluationConfig.value = {};

  // 调用BaseParamsSetting的重置方法
  if (baseParamsRef.value?.resetParams) {
    baseParamsRef.value.resetParams();
  }

  // 重新加载配置
  await loadModelConfiguration();

  console.log("resetParams completed for ML model");
};

// 强制重新加载配置的方法
const forceReloadConfig = async () => {
  console.log("forceReloadConfig called for ML model");

  // 完全清空所有状态
  modelConfig.value = null;
  evaluationConfig.value = {};

  // 调用BaseParamsSetting的强制重新加载方法
  if (baseParamsRef.value?.forceReloadConfig) {
    baseParamsRef.value.forceReloadConfig();
  }

  // 重新加载配置
  await loadModelConfiguration();

  console.log("forceReloadConfig completed for ML model");
};

// 暴露重置方法给父组件
defineExpose({
  resetParams,
  forceReloadConfig,
});

// 生命周期
onMounted(() => {
  loadModelConfiguration();
});
</script>
