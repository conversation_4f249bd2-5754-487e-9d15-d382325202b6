<template>
  <el-dropdown :disabled="props.disabled" trigger="hover" :hide-on-click="true">
    <el-button :disabled="props.disabled">数据预处理</el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item
          v-for="option in menuOptions"
          :key="option.value"
          :disabled="props.disabled"
          @click="processData(option.value)"
        >
          {{ option.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script lang="ts" setup>
const props = defineProps<{
  disabled?: boolean;
}>();
const menuOptions = [
  { label: "缺失值处理", value: "missingValue" },
  { label: "异常值检测", value: "outlier" },
];

const emit = defineEmits<{
  (e: "processData", value: string): void;
}>();

function processData(value: string) {
  emit("processData", value);
}
</script>
