import { type Ref, nextTick } from "vue";
import { debounce } from "lodash-es";

export function useTableResize(
  tableContentRef: Ref<any>,
  autoResize: boolean = true,
) {
  let resizeObserver: ResizeObserver | null = null;
  let isResizePaused = false;
  let lastDimensions = { width: 0, height: 0 };

  const updateDimensions = async (width?: string, height?: string) => {
    await nextTick();

    if (!tableContentRef.value?.hotInstance || isResizePaused) return;

    const hotInstance = tableContentRef.value.hotInstance;

    // 检查尺寸是否真的发生了变化
    const currentWidth = parseInt(width || '0');
    const currentHeight = parseInt(height || '0');

    if (Math.abs(currentWidth - lastDimensions.width) < 2 &&
        Math.abs(currentHeight - lastDimensions.height) < 2) {
      return; // 尺寸变化太小，跳过更新
    }

    lastDimensions = { width: currentWidth, height: currentHeight };

    try {
      // 使用最高效的更新方式
      if (width || height) {
        // 直接设置容器尺寸，避免updateSettings的开销
        const container = hotInstance.rootElement;
        if (container) {
          if (width) container.style.width = width;
          if (height) container.style.height = height;
        }
      }

      // 使用requestAnimationFrame确保在下一帧执行
      requestAnimationFrame(() => {
        if (!hotInstance.isDestroyed) {
          hotInstance.refreshDimensions();
        }
      });
    } catch (error) {
      console.warn('Table resize update failed:', error);
    }
  };

  // 优化的resize处理 - 使用requestAnimationFrame
  const handleResize = debounce((entries: ResizeObserverEntry[]) => {
    if (!autoResize || isResizePaused) return;

    // 使用requestAnimationFrame确保在下一帧执行
    requestAnimationFrame(async () => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        await updateDimensions(`${width}px`, `${height}px`);
      }
    });
  }, 8); // 进一步减少防抖时间

  const startObserving = () => {
    if (!autoResize) return;

    const container = tableContentRef.value?.$el?.parentElement;
    if (!container) return;

    resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(container);
  };

  const stopObserving = () => {
    resizeObserver?.disconnect();
    resizeObserver = null;
  };

  // 暂停resize监听（在侧边栏动画期间使用）
  const pauseResize = () => {
    isResizePaused = true;
  };

  // 恢复resize监听
  const resumeResize = () => {
    isResizePaused = false;
    // 恢复后立即检查一次尺寸
    if (tableContentRef.value?.hotInstance) {
      // 使用requestAnimationFrame确保在下一帧立即执行
      requestAnimationFrame(() => {
        const container = tableContentRef.value?.$el?.parentElement;
        if (container) {
          const rect = container.getBoundingClientRect();
          updateDimensions(`${rect.width}px`, `${rect.height}px`);
        }
      });
    }
  };

  return {
    updateDimensions,
    startObserving,
    stopObserving,
    pauseResize,
    resumeResize,
  };
}
