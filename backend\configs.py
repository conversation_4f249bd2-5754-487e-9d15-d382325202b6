from pathlib import Path
import logging

class BasicConfig(object):
    
    # 项目路径配置
    BASE_FOLDER = Path(__file__).parent
    STATIC_FOLDER = BASE_FOLDER.joinpath("static")
    TEMP_FOLDER = BASE_FOLDER.joinpath("temp")
    PROJECTS_FOLDER = BASE_FOLDER.joinpath("projects")
    UPLOAD_FOLDER = TEMP_FOLDER.joinpath("uploads")

    SECRET_KEY = "42mldesktop@szw"

    SERVICE_PREFIX = "mldesktop_backend" # 服务器路径统一后缀

    JSON_AS_ASCII = False

    # 日志配置
    LOG_PATH = TEMP_FOLDER.joinpath("app.log")
    LOG_LEVEL = logging.INFO
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

    # 文件上传配置
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'csv', 'xlsx', 'zip'}
    MAX_CONTENT_LENGTH = 1024 * 1024 * 1024  # limit file size to 1GB
    
dev = BasicConfig()