<template>
  <div class="prediction-table">
    <div class="flex items-center justify-between mb-4">
      <el-select v-model="selectedDataset" placeholder="请选择数据集" style="width: 200px;">
        <el-option
          v-for="key in availableDatasets"
          :key="key"
          :value="key"
          :label="datasetConfig.titles[key]"
        />
      </el-select>
      <el-button type="primary" @click="exportPredictData">
        <el-icon><Download /></el-icon>
        导出数据
      </el-button>
    </div>
    
    <el-table
      :data="currentPredictData"
      style="width: 100%"
      height="400"
      border
      stripe
    >
      <el-table-column label="序号" type="index" width="80" />
      <el-table-column label="真实值" prop="true" sortable />
      <el-table-column label="预测值" prop="predict" sortable />
      <el-table-column label="误差" prop="error" sortable>
        <template #default="{ row }">
          <span :class="getErrorClass(row.error)">
            {{ row.error }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="误差率" prop="errorRate" sortable v-if="showErrorRate">
        <template #default="{ row }">
          <span :class="getErrorClass(row.errorRate)">
            {{ row.errorRate }}%
          </span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 统计信息 -->
    <div class="statistics mt-4" v-if="currentPredictData.length > 0">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="样本数量" :value="currentPredictData.length" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="平均误差" :value="averageError" :precision="4" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="最大误差" :value="maxError" :precision="4" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="最小误差" :value="minError" :precision="4" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Download } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as XLSX from 'xlsx';

interface Props {
  modelResult: any;
  showErrorRate?: boolean;
  datasetConfig?: {
    order: readonly string[];
    titles: Record<string, string>;
  };
}

const props = withDefaults(defineProps<Props>(), {
  showErrorRate: true,
  datasetConfig: () => ({
    order: ["train", "test", "cv", "validation"] as const,
    titles: {
      train: "训练集",
      test: "测试集",
      cv: "交叉验证", 
      validation: "验证集"
    }
  })
});

const selectedDataset = ref("train");

// 可用数据集
const availableDatasets = computed(() =>
  props.modelResult?.eval
    ? props.datasetConfig.order.filter((key) => props.modelResult.eval[key])
    : [],
);

// 当前预测数据
const currentPredictData = computed(() => {
  const ds = props.modelResult?.eval?.[selectedDataset.value];
  if (!ds || !ds.y_true || !ds.y_predict) return [];
  
  return ds.y_true.map((val: number, i: number) => {
    const trueVal = Number(val);
    const predictVal = Number(ds.y_predict[i]);
    const error = Math.abs(trueVal - predictVal);
    const errorRate = trueVal !== 0 ? (error / Math.abs(trueVal)) * 100 : 0;
    
    return {
      true: Number(trueVal.toFixed(2)),
      predict: Number(predictVal.toFixed(2)),
      error: Number(error.toFixed(4)),
      errorRate: Number(errorRate.toFixed(2))
    };
  });
});

// 统计信息
const averageError = computed(() => {
  if (currentPredictData.value.length === 0) return 0;
  const sum = currentPredictData.value.reduce((acc, item) => acc + item.error, 0);
  return sum / currentPredictData.value.length;
});

const maxError = computed(() => {
  if (currentPredictData.value.length === 0) return 0;
  return Math.max(...currentPredictData.value.map(item => item.error));
});

const minError = computed(() => {
  if (currentPredictData.value.length === 0) return 0;
  return Math.min(...currentPredictData.value.map(item => item.error));
});

// 根据误差大小设置样式类
const getErrorClass = (error: number) => {
  if (error < 0.1) return 'error-low';
  if (error < 0.5) return 'error-medium';
  return 'error-high';
};

// 导出预测数据
const exportPredictData = () => {
  if (currentPredictData.value.length === 0) {
    ElMessage.warning('没有可导出的数据');
    return;
  }

  const headers = ['序号', '真实值', '预测值', '误差'];
  if (props.showErrorRate) {
    headers.push('误差率(%)');
  }

  const data = [
    headers,
    ...currentPredictData.value.map((item, index) => {
      const row = [index + 1, item.true, item.predict, item.error];
      if (props.showErrorRate) {
        row.push(item.errorRate);
      }
      return row;
    }),
  ];

  const ws = XLSX.utils.aoa_to_sheet(data);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "PredictData");
  
  const fileName = `predict_data_${props.datasetConfig.titles[selectedDataset.value]}_${Date.now()}.xlsx`;
  XLSX.writeFile(wb, fileName);
  
  ElMessage.success("数据导出成功");
};

// 监听可用数据集变化，自动选择第一个
watch(availableDatasets, (newDatasets) => {
  if (newDatasets.length > 0 && !newDatasets.includes(selectedDataset.value)) {
    selectedDataset.value = newDatasets[0];
  }
}, { immediate: true });
</script>

<style scoped>
.prediction-table {
  width: 100%;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.statistics {
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
}

/* 误差颜色样式 */
:deep(.error-low) {
  color: #67c23a;
  font-weight: 500;
}

:deep(.error-medium) {
  color: #e6a23c;
  font-weight: 500;
}

:deep(.error-high) {
  color: #f56c6c;
  font-weight: 500;
}
</style>
