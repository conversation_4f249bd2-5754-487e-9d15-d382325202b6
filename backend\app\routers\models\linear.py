from flask import Blueprint, request, make_response, current_app, jsonify
import traceback, uuid, numpy as np, pandas as pd
from app.utils.Regression import Regression
from app.utils.params import camel_to_snake, snake_to_camel
from sklearn.model_selection import train_test_split

linear_bp = Blueprint("linear", __name__)

@linear_bp.route("/build", methods=["POST"])
def linear_build():
    post_data = request.json
    post_data = camel_to_snake(post_data)

    model_info = post_data.get("model")
    dataset_info = post_data.get("dataset")

    model_meta = model_info.get("meta", {})
    algorithm = model_info.get("algorithm", {})
    evaluation = model_meta.get("evaluation", {})

    dataset_meta = dataset_info.get("meta", {})
    data = dataset_info.get("data", [])

    headers = dataset_meta.get("headers", {})
    all_headers = headers.get("all", [])
    dataset = pd.DataFrame(data, columns=all_headers)
    
    X = dataset[headers.get("features", [])]
    Y = dataset[headers.get("target", [])[0]]

    if evaluation.get("test"):
        x_train, x_test, y_train, y_test = train_test_split(
            X, Y, 
            test_size=evaluation.get("test", {}).get("size", 0.2), 
            random_state=evaluation.get("test", {}).get("random_state", 42)
        )
    else:
        x_train = X.copy()
        y_train = Y.copy()
        x_test = None
        y_test = None
    
    if evaluation.get("cv"):
        cv_fold = evaluation.get("cv", {}).get("k", 5)
    else:
        cv_fold = None
    
    alg_name = algorithm.get("name", "")
    alg_param = algorithm.get("params", {})

    reg = Regression(x_train, y_train, x_test, y_test, cv_fold)
    model, info = reg.train(alg_name, alg_param)
    return make_response(jsonify({"code": 200, "msg": "success", "data": info}), 200)
