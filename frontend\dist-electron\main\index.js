import { release } from "node:os";
import { fileURLToPath as fileURLToPath$1 } from "node:url";
import { dirname, join } from "node:path";
import { Notification, app, BrowserWindow, ipcMain, dialog, Menu } from "electron";
import fs from "fs";
import path from "path";
import { createRequire } from "node:module";
import require$$1$1 from "querystring";
import require$$0 from "assert";
import require$$3 from "stream";
import require$$0$1 from "events";
import require$$1 from "util";
import require$$6 from "net";
import require$$7 from "tls";
import { fileURLToPath } from "url";
var commonjsGlobal = typeof globalThis !== "undefined" ? globalThis : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : {};
function getDefaultExportFromCjs(x) {
  return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, "default") ? x["default"] : x;
}
var channel_api = {};
var connect = {};
var requiresPort;
var hasRequiredRequiresPort;
function requireRequiresPort() {
  if (hasRequiredRequiresPort) return requiresPort;
  hasRequiredRequiresPort = 1;
  requiresPort = function required(port, protocol) {
    protocol = protocol.split(":")[0];
    port = +port;
    if (!port) return false;
    switch (protocol) {
      case "http":
      case "ws":
        return port !== 80;
      case "https":
      case "wss":
        return port !== 443;
      case "ftp":
        return port !== 21;
      case "gopher":
        return port !== 70;
      case "file":
        return false;
    }
    return port !== 0;
  };
  return requiresPort;
}
var querystringify = {};
var hasRequiredQuerystringify;
function requireQuerystringify() {
  if (hasRequiredQuerystringify) return querystringify;
  hasRequiredQuerystringify = 1;
  var has = Object.prototype.hasOwnProperty, undef;
  function decode(input) {
    try {
      return decodeURIComponent(input.replace(/\+/g, " "));
    } catch (e) {
      return null;
    }
  }
  function encode(input) {
    try {
      return encodeURIComponent(input);
    } catch (e) {
      return null;
    }
  }
  function querystring(query) {
    var parser = /([^=?#&]+)=?([^&]*)/g, result = {}, part;
    while (part = parser.exec(query)) {
      var key = decode(part[1]), value = decode(part[2]);
      if (key === null || value === null || key in result) continue;
      result[key] = value;
    }
    return result;
  }
  function querystringify$1(obj, prefix) {
    prefix = prefix || "";
    var pairs = [], value, key;
    if ("string" !== typeof prefix) prefix = "?";
    for (key in obj) {
      if (has.call(obj, key)) {
        value = obj[key];
        if (!value && (value === null || value === undef || isNaN(value))) {
          value = "";
        }
        key = encode(key);
        value = encode(value);
        if (key === null || value === null) continue;
        pairs.push(key + "=" + value);
      }
    }
    return pairs.length ? prefix + pairs.join("&") : "";
  }
  querystringify.stringify = querystringify$1;
  querystringify.parse = querystring;
  return querystringify;
}
var urlParse;
var hasRequiredUrlParse;
function requireUrlParse() {
  if (hasRequiredUrlParse) return urlParse;
  hasRequiredUrlParse = 1;
  var required = requireRequiresPort(), qs = requireQuerystringify(), controlOrWhitespace = /^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/, CRHTLF = /[\n\r\t]/g, slashes = /^[A-Za-z][A-Za-z0-9+-.]*:\/\//, port = /:\d+$/, protocolre = /^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i, windowsDriveLetter = /^[a-zA-Z]:/;
  function trimLeft(str) {
    return (str ? str : "").toString().replace(controlOrWhitespace, "");
  }
  var rules = [
    ["#", "hash"],
    // Extract from the back.
    ["?", "query"],
    // Extract from the back.
    function sanitize(address, url2) {
      return isSpecial(url2.protocol) ? address.replace(/\\/g, "/") : address;
    },
    ["/", "pathname"],
    // Extract from the back.
    ["@", "auth", 1],
    // Extract from the front.
    [NaN, "host", void 0, 1, 1],
    // Set left over value.
    [/:(\d*)$/, "port", void 0, 1],
    // RegExp the back.
    [NaN, "hostname", void 0, 1, 1]
    // Set left over.
  ];
  var ignore = { hash: 1, query: 1 };
  function lolcation(loc) {
    var globalVar;
    if (typeof window !== "undefined") globalVar = window;
    else if (typeof commonjsGlobal !== "undefined") globalVar = commonjsGlobal;
    else if (typeof self !== "undefined") globalVar = self;
    else globalVar = {};
    var location = globalVar.location || {};
    loc = loc || location;
    var finaldestination = {}, type = typeof loc, key;
    if ("blob:" === loc.protocol) {
      finaldestination = new Url(unescape(loc.pathname), {});
    } else if ("string" === type) {
      finaldestination = new Url(loc, {});
      for (key in ignore) delete finaldestination[key];
    } else if ("object" === type) {
      for (key in loc) {
        if (key in ignore) continue;
        finaldestination[key] = loc[key];
      }
      if (finaldestination.slashes === void 0) {
        finaldestination.slashes = slashes.test(loc.href);
      }
    }
    return finaldestination;
  }
  function isSpecial(scheme) {
    return scheme === "file:" || scheme === "ftp:" || scheme === "http:" || scheme === "https:" || scheme === "ws:" || scheme === "wss:";
  }
  function extractProtocol(address, location) {
    address = trimLeft(address);
    address = address.replace(CRHTLF, "");
    location = location || {};
    var match = protocolre.exec(address);
    var protocol = match[1] ? match[1].toLowerCase() : "";
    var forwardSlashes = !!match[2];
    var otherSlashes = !!match[3];
    var slashesCount = 0;
    var rest;
    if (forwardSlashes) {
      if (otherSlashes) {
        rest = match[2] + match[3] + match[4];
        slashesCount = match[2].length + match[3].length;
      } else {
        rest = match[2] + match[4];
        slashesCount = match[2].length;
      }
    } else {
      if (otherSlashes) {
        rest = match[3] + match[4];
        slashesCount = match[3].length;
      } else {
        rest = match[4];
      }
    }
    if (protocol === "file:") {
      if (slashesCount >= 2) {
        rest = rest.slice(2);
      }
    } else if (isSpecial(protocol)) {
      rest = match[4];
    } else if (protocol) {
      if (forwardSlashes) {
        rest = rest.slice(2);
      }
    } else if (slashesCount >= 2 && isSpecial(location.protocol)) {
      rest = match[4];
    }
    return {
      protocol,
      slashes: forwardSlashes || isSpecial(protocol),
      slashesCount,
      rest
    };
  }
  function resolve(relative, base) {
    if (relative === "") return base;
    var path2 = (base || "/").split("/").slice(0, -1).concat(relative.split("/")), i = path2.length, last = path2[i - 1], unshift = false, up = 0;
    while (i--) {
      if (path2[i] === ".") {
        path2.splice(i, 1);
      } else if (path2[i] === "..") {
        path2.splice(i, 1);
        up++;
      } else if (up) {
        if (i === 0) unshift = true;
        path2.splice(i, 1);
        up--;
      }
    }
    if (unshift) path2.unshift("");
    if (last === "." || last === "..") path2.push("");
    return path2.join("/");
  }
  function Url(address, location, parser) {
    address = trimLeft(address);
    address = address.replace(CRHTLF, "");
    if (!(this instanceof Url)) {
      return new Url(address, location, parser);
    }
    var relative, extracted, parse, instruction, index, key, instructions = rules.slice(), type = typeof location, url2 = this, i = 0;
    if ("object" !== type && "string" !== type) {
      parser = location;
      location = null;
    }
    if (parser && "function" !== typeof parser) parser = qs.parse;
    location = lolcation(location);
    extracted = extractProtocol(address || "", location);
    relative = !extracted.protocol && !extracted.slashes;
    url2.slashes = extracted.slashes || relative && location.slashes;
    url2.protocol = extracted.protocol || location.protocol || "";
    address = extracted.rest;
    if (extracted.protocol === "file:" && (extracted.slashesCount !== 2 || windowsDriveLetter.test(address)) || !extracted.slashes && (extracted.protocol || extracted.slashesCount < 2 || !isSpecial(url2.protocol))) {
      instructions[3] = [/(.*)/, "pathname"];
    }
    for (; i < instructions.length; i++) {
      instruction = instructions[i];
      if (typeof instruction === "function") {
        address = instruction(address, url2);
        continue;
      }
      parse = instruction[0];
      key = instruction[1];
      if (parse !== parse) {
        url2[key] = address;
      } else if ("string" === typeof parse) {
        index = parse === "@" ? address.lastIndexOf(parse) : address.indexOf(parse);
        if (~index) {
          if ("number" === typeof instruction[2]) {
            url2[key] = address.slice(0, index);
            address = address.slice(index + instruction[2]);
          } else {
            url2[key] = address.slice(index);
            address = address.slice(0, index);
          }
        }
      } else if (index = parse.exec(address)) {
        url2[key] = index[1];
        address = address.slice(0, index.index);
      }
      url2[key] = url2[key] || (relative && instruction[3] ? location[key] || "" : "");
      if (instruction[4]) url2[key] = url2[key].toLowerCase();
    }
    if (parser) url2.query = parser(url2.query);
    if (relative && location.slashes && url2.pathname.charAt(0) !== "/" && (url2.pathname !== "" || location.pathname !== "")) {
      url2.pathname = resolve(url2.pathname, location.pathname);
    }
    if (url2.pathname.charAt(0) !== "/" && isSpecial(url2.protocol)) {
      url2.pathname = "/" + url2.pathname;
    }
    if (!required(url2.port, url2.protocol)) {
      url2.host = url2.hostname;
      url2.port = "";
    }
    url2.username = url2.password = "";
    if (url2.auth) {
      index = url2.auth.indexOf(":");
      if (~index) {
        url2.username = url2.auth.slice(0, index);
        url2.username = encodeURIComponent(decodeURIComponent(url2.username));
        url2.password = url2.auth.slice(index + 1);
        url2.password = encodeURIComponent(decodeURIComponent(url2.password));
      } else {
        url2.username = encodeURIComponent(decodeURIComponent(url2.auth));
      }
      url2.auth = url2.password ? url2.username + ":" + url2.password : url2.username;
    }
    url2.origin = url2.protocol !== "file:" && isSpecial(url2.protocol) && url2.host ? url2.protocol + "//" + url2.host : "null";
    url2.href = url2.toString();
  }
  function set(part, value, fn) {
    var url2 = this;
    switch (part) {
      case "query":
        if ("string" === typeof value && value.length) {
          value = (fn || qs.parse)(value);
        }
        url2[part] = value;
        break;
      case "port":
        url2[part] = value;
        if (!required(value, url2.protocol)) {
          url2.host = url2.hostname;
          url2[part] = "";
        } else if (value) {
          url2.host = url2.hostname + ":" + value;
        }
        break;
      case "hostname":
        url2[part] = value;
        if (url2.port) value += ":" + url2.port;
        url2.host = value;
        break;
      case "host":
        url2[part] = value;
        if (port.test(value)) {
          value = value.split(":");
          url2.port = value.pop();
          url2.hostname = value.join(":");
        } else {
          url2.hostname = value;
          url2.port = "";
        }
        break;
      case "protocol":
        url2.protocol = value.toLowerCase();
        url2.slashes = !fn;
        break;
      case "pathname":
      case "hash":
        if (value) {
          var char = part === "pathname" ? "/" : "#";
          url2[part] = value.charAt(0) !== char ? char + value : value;
        } else {
          url2[part] = value;
        }
        break;
      case "username":
      case "password":
        url2[part] = encodeURIComponent(value);
        break;
      case "auth":
        var index = value.indexOf(":");
        if (~index) {
          url2.username = value.slice(0, index);
          url2.username = encodeURIComponent(decodeURIComponent(url2.username));
          url2.password = value.slice(index + 1);
          url2.password = encodeURIComponent(decodeURIComponent(url2.password));
        } else {
          url2.username = encodeURIComponent(decodeURIComponent(value));
        }
    }
    for (var i = 0; i < rules.length; i++) {
      var ins = rules[i];
      if (ins[4]) url2[ins[1]] = url2[ins[1]].toLowerCase();
    }
    url2.auth = url2.password ? url2.username + ":" + url2.password : url2.username;
    url2.origin = url2.protocol !== "file:" && isSpecial(url2.protocol) && url2.host ? url2.protocol + "//" + url2.host : "null";
    url2.href = url2.toString();
    return url2;
  }
  function toString(stringify) {
    if (!stringify || "function" !== typeof stringify) stringify = qs.stringify;
    var query, url2 = this, host = url2.host, protocol = url2.protocol;
    if (protocol && protocol.charAt(protocol.length - 1) !== ":") protocol += ":";
    var result = protocol + (url2.protocol && url2.slashes || isSpecial(url2.protocol) ? "//" : "");
    if (url2.username) {
      result += url2.username;
      if (url2.password) result += ":" + url2.password;
      result += "@";
    } else if (url2.password) {
      result += ":" + url2.password;
      result += "@";
    } else if (url2.protocol !== "file:" && isSpecial(url2.protocol) && !host && url2.pathname !== "/") {
      result += "@";
    }
    if (host[host.length - 1] === ":" || port.test(url2.hostname) && !url2.port) {
      host += ":";
    }
    result += host + url2.pathname;
    query = "object" === typeof url2.query ? stringify(url2.query) : url2.query;
    if (query) result += "?" !== query.charAt(0) ? "?" + query : query;
    if (url2.hash) result += url2.hash;
    return result;
  }
  Url.prototype = { set, toString };
  Url.extractProtocol = extractProtocol;
  Url.location = lolcation;
  Url.trimLeft = trimLeft;
  Url.qs = qs;
  urlParse = Url;
  return urlParse;
}
var connection = {};
var defs = {};
var codec = {};
var bufferMoreInts = { exports: {} };
bufferMoreInts.exports;
var hasRequiredBufferMoreInts;
function requireBufferMoreInts() {
  if (hasRequiredBufferMoreInts) return bufferMoreInts.exports;
  hasRequiredBufferMoreInts = 1;
  (function(module) {
    var SHIFT_LEFT_32 = (1 << 16) * (1 << 16);
    var SHIFT_RIGHT_32 = 1 / SHIFT_LEFT_32;
    var MAX_INT = 9007199254740991;
    function isContiguousInt(val) {
      return val <= MAX_INT && val >= -9007199254740991;
    }
    function assertContiguousInt(val) {
      if (!isContiguousInt(val)) {
        throw new TypeError("number cannot be represented as a contiguous integer");
      }
    }
    module.exports.isContiguousInt = isContiguousInt;
    module.exports.assertContiguousInt = assertContiguousInt;
    ["UInt", "Int"].forEach(function(sign) {
      var suffix = sign + "8";
      module.exports["read" + suffix] = Buffer.prototype["read" + suffix].call;
      module.exports["write" + suffix] = Buffer.prototype["write" + suffix].call;
      ["16", "32"].forEach(function(size) {
        ["LE", "BE"].forEach(function(endian) {
          var suffix2 = sign + size + endian;
          var read = Buffer.prototype["read" + suffix2];
          module.exports["read" + suffix2] = function(buf, offset) {
            return read.call(buf, offset);
          };
          var write = Buffer.prototype["write" + suffix2];
          module.exports["write" + suffix2] = function(buf, val, offset) {
            return write.call(buf, val, offset);
          };
        });
      });
    });
    function check_value(val, min, max) {
      val = +val;
      if (typeof val != "number" || val < min || val > max || Math.floor(val) !== val) {
        throw new TypeError('"value" argument is out of bounds');
      }
      return val;
    }
    function check_bounds(buf, offset, len) {
      if (offset < 0 || offset + len > buf.length) {
        throw new RangeError("Index out of range");
      }
    }
    function readUInt24BE(buf, offset) {
      return buf.readUInt8(offset) << 16 | buf.readUInt16BE(offset + 1);
    }
    module.exports.readUInt24BE = readUInt24BE;
    function writeUInt24BE(buf, val, offset) {
      val = check_value(val, 0, 16777215);
      check_bounds(buf, offset, 3);
      buf.writeUInt8(val >>> 16, offset);
      buf.writeUInt16BE(val & 65535, offset + 1);
    }
    module.exports.writeUInt24BE = writeUInt24BE;
    function readUInt40BE(buf, offset) {
      return (buf.readUInt8(offset) || 0) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 1);
    }
    module.exports.readUInt40BE = readUInt40BE;
    function writeUInt40BE(buf, val, offset) {
      val = check_value(val, 0, 1099511627775);
      check_bounds(buf, offset, 5);
      buf.writeUInt8(Math.floor(val * SHIFT_RIGHT_32), offset);
      buf.writeInt32BE(val & -1, offset + 1);
    }
    module.exports.writeUInt40BE = writeUInt40BE;
    function readUInt48BE(buf, offset) {
      return buf.readUInt16BE(offset) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 2);
    }
    module.exports.readUInt48BE = readUInt48BE;
    function writeUInt48BE(buf, val, offset) {
      val = check_value(val, 0, 281474976710655);
      check_bounds(buf, offset, 6);
      buf.writeUInt16BE(Math.floor(val * SHIFT_RIGHT_32), offset);
      buf.writeInt32BE(val & -1, offset + 2);
    }
    module.exports.writeUInt48BE = writeUInt48BE;
    function readUInt56BE(buf, offset) {
      return ((buf.readUInt8(offset) || 0) << 16 | buf.readUInt16BE(offset + 1)) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 3);
    }
    module.exports.readUInt56BE = readUInt56BE;
    function writeUInt56BE(buf, val, offset) {
      val = check_value(val, 0, 72057594037927940);
      check_bounds(buf, offset, 7);
      if (val < 72057594037927940) {
        var hi = Math.floor(val * SHIFT_RIGHT_32);
        buf.writeUInt8(hi >>> 16, offset);
        buf.writeUInt16BE(hi & 65535, offset + 1);
        buf.writeInt32BE(val & -1, offset + 3);
      } else {
        buf[offset] = 255;
        buf[offset + 1] = 255;
        buf[offset + 2] = 255;
        buf[offset + 3] = 255;
        buf[offset + 4] = 255;
        buf[offset + 5] = 255;
        buf[offset + 6] = 255;
      }
    }
    module.exports.writeUInt56BE = writeUInt56BE;
    function readUInt64BE(buf, offset) {
      return buf.readUInt32BE(offset) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 4);
    }
    module.exports.readUInt64BE = readUInt64BE;
    function writeUInt64BE(buf, val, offset) {
      val = check_value(val, 0, 18446744073709552e3);
      check_bounds(buf, offset, 8);
      if (val < 18446744073709552e3) {
        buf.writeUInt32BE(Math.floor(val * SHIFT_RIGHT_32), offset);
        buf.writeInt32BE(val & -1, offset + 4);
      } else {
        buf[offset] = 255;
        buf[offset + 1] = 255;
        buf[offset + 2] = 255;
        buf[offset + 3] = 255;
        buf[offset + 4] = 255;
        buf[offset + 5] = 255;
        buf[offset + 6] = 255;
        buf[offset + 7] = 255;
      }
    }
    module.exports.writeUInt64BE = writeUInt64BE;
    function readUInt24LE(buf, offset) {
      return buf.readUInt8(offset + 2) << 16 | buf.readUInt16LE(offset);
    }
    module.exports.readUInt24LE = readUInt24LE;
    function writeUInt24LE(buf, val, offset) {
      val = check_value(val, 0, 16777215);
      check_bounds(buf, offset, 3);
      buf.writeUInt16LE(val & 65535, offset);
      buf.writeUInt8(val >>> 16, offset + 2);
    }
    module.exports.writeUInt24LE = writeUInt24LE;
    function readUInt40LE(buf, offset) {
      return (buf.readUInt8(offset + 4) || 0) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);
    }
    module.exports.readUInt40LE = readUInt40LE;
    function writeUInt40LE(buf, val, offset) {
      val = check_value(val, 0, 1099511627775);
      check_bounds(buf, offset, 5);
      buf.writeInt32LE(val & -1, offset);
      buf.writeUInt8(Math.floor(val * SHIFT_RIGHT_32), offset + 4);
    }
    module.exports.writeUInt40LE = writeUInt40LE;
    function readUInt48LE(buf, offset) {
      return buf.readUInt16LE(offset + 4) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);
    }
    module.exports.readUInt48LE = readUInt48LE;
    function writeUInt48LE(buf, val, offset) {
      val = check_value(val, 0, 281474976710655);
      check_bounds(buf, offset, 6);
      buf.writeInt32LE(val & -1, offset);
      buf.writeUInt16LE(Math.floor(val * SHIFT_RIGHT_32), offset + 4);
    }
    module.exports.writeUInt48LE = writeUInt48LE;
    function readUInt56LE(buf, offset) {
      return ((buf.readUInt8(offset + 6) || 0) << 16 | buf.readUInt16LE(offset + 4)) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);
    }
    module.exports.readUInt56LE = readUInt56LE;
    function writeUInt56LE(buf, val, offset) {
      val = check_value(val, 0, 72057594037927940);
      check_bounds(buf, offset, 7);
      if (val < 72057594037927940) {
        buf.writeInt32LE(val & -1, offset);
        var hi = Math.floor(val * SHIFT_RIGHT_32);
        buf.writeUInt16LE(hi & 65535, offset + 4);
        buf.writeUInt8(hi >>> 16, offset + 6);
      } else {
        buf[offset] = 255;
        buf[offset + 1] = 255;
        buf[offset + 2] = 255;
        buf[offset + 3] = 255;
        buf[offset + 4] = 255;
        buf[offset + 5] = 255;
        buf[offset + 6] = 255;
      }
    }
    module.exports.writeUInt56LE = writeUInt56LE;
    function readUInt64LE(buf, offset) {
      return buf.readUInt32LE(offset + 4) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);
    }
    module.exports.readUInt64LE = readUInt64LE;
    function writeUInt64LE(buf, val, offset) {
      val = check_value(val, 0, 18446744073709552e3);
      check_bounds(buf, offset, 8);
      if (val < 18446744073709552e3) {
        buf.writeInt32LE(val & -1, offset);
        buf.writeUInt32LE(Math.floor(val * SHIFT_RIGHT_32), offset + 4);
      } else {
        buf[offset] = 255;
        buf[offset + 1] = 255;
        buf[offset + 2] = 255;
        buf[offset + 3] = 255;
        buf[offset + 4] = 255;
        buf[offset + 5] = 255;
        buf[offset + 6] = 255;
        buf[offset + 7] = 255;
      }
    }
    module.exports.writeUInt64LE = writeUInt64LE;
    function readInt24BE(buf, offset) {
      return (buf.readInt8(offset) << 16) + buf.readUInt16BE(offset + 1);
    }
    module.exports.readInt24BE = readInt24BE;
    function writeInt24BE(buf, val, offset) {
      val = check_value(val, -8388608, 8388607);
      check_bounds(buf, offset, 3);
      buf.writeInt8(val >> 16, offset);
      buf.writeUInt16BE(val & 65535, offset + 1);
    }
    module.exports.writeInt24BE = writeInt24BE;
    function readInt40BE(buf, offset) {
      return (buf.readInt8(offset) || 0) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 1);
    }
    module.exports.readInt40BE = readInt40BE;
    function writeInt40BE(buf, val, offset) {
      val = check_value(val, -549755813888, 549755813887);
      check_bounds(buf, offset, 5);
      buf.writeInt8(Math.floor(val * SHIFT_RIGHT_32), offset);
      buf.writeInt32BE(val & -1, offset + 1);
    }
    module.exports.writeInt40BE = writeInt40BE;
    function readInt48BE(buf, offset) {
      return buf.readInt16BE(offset) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 2);
    }
    module.exports.readInt48BE = readInt48BE;
    function writeInt48BE(buf, val, offset) {
      val = check_value(val, -140737488355328, 140737488355327);
      check_bounds(buf, offset, 6);
      buf.writeInt16BE(Math.floor(val * SHIFT_RIGHT_32), offset);
      buf.writeInt32BE(val & -1, offset + 2);
    }
    module.exports.writeInt48BE = writeInt48BE;
    function readInt56BE(buf, offset) {
      return (((buf.readInt8(offset) || 0) << 16) + buf.readUInt16BE(offset + 1)) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 3);
    }
    module.exports.readInt56BE = readInt56BE;
    function writeInt56BE(buf, val, offset) {
      val = check_value(val, -576460752303423500, 36028797018963970);
      check_bounds(buf, offset, 7);
      if (val < 36028797018963970) {
        var hi = Math.floor(val * SHIFT_RIGHT_32);
        buf.writeInt8(hi >> 16, offset);
        buf.writeUInt16BE(hi & 65535, offset + 1);
        buf.writeInt32BE(val & -1, offset + 3);
      } else {
        buf[offset] = 127;
        buf[offset + 1] = 255;
        buf[offset + 2] = 255;
        buf[offset + 3] = 255;
        buf[offset + 4] = 255;
        buf[offset + 5] = 255;
        buf[offset + 6] = 255;
      }
    }
    module.exports.writeInt56BE = writeInt56BE;
    function readInt64BE(buf, offset) {
      return buf.readInt32BE(offset) * SHIFT_LEFT_32 + buf.readUInt32BE(offset + 4);
    }
    module.exports.readInt64BE = readInt64BE;
    function writeInt64BE(buf, val, offset) {
      val = check_value(val, -23611832414348226e5, 9223372036854776e3);
      check_bounds(buf, offset, 8);
      if (val < 9223372036854776e3) {
        buf.writeInt32BE(Math.floor(val * SHIFT_RIGHT_32), offset);
        buf.writeInt32BE(val & -1, offset + 4);
      } else {
        buf[offset] = 127;
        buf[offset + 1] = 255;
        buf[offset + 2] = 255;
        buf[offset + 3] = 255;
        buf[offset + 4] = 255;
        buf[offset + 5] = 255;
        buf[offset + 6] = 255;
        buf[offset + 7] = 255;
      }
    }
    module.exports.writeInt64BE = writeInt64BE;
    function readInt24LE(buf, offset) {
      return (buf.readInt8(offset + 2) << 16) + buf.readUInt16LE(offset);
    }
    module.exports.readInt24LE = readInt24LE;
    function writeInt24LE(buf, val, offset) {
      val = check_value(val, -8388608, 8388607);
      check_bounds(buf, offset, 3);
      buf.writeUInt16LE(val & 65535, offset);
      buf.writeInt8(val >> 16, offset + 2);
    }
    module.exports.writeInt24LE = writeInt24LE;
    function readInt40LE(buf, offset) {
      return (buf.readInt8(offset + 4) || 0) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);
    }
    module.exports.readInt40LE = readInt40LE;
    function writeInt40LE(buf, val, offset) {
      val = check_value(val, -549755813888, 549755813887);
      check_bounds(buf, offset, 5);
      buf.writeInt32LE(val & -1, offset);
      buf.writeInt8(Math.floor(val * SHIFT_RIGHT_32), offset + 4);
    }
    module.exports.writeInt40LE = writeInt40LE;
    function readInt48LE(buf, offset) {
      return buf.readInt16LE(offset + 4) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);
    }
    module.exports.readInt48LE = readInt48LE;
    function writeInt48LE(buf, val, offset) {
      val = check_value(val, -140737488355328, 140737488355327);
      check_bounds(buf, offset, 6);
      buf.writeInt32LE(val & -1, offset);
      buf.writeInt16LE(Math.floor(val * SHIFT_RIGHT_32), offset + 4);
    }
    module.exports.writeInt48LE = writeInt48LE;
    function readInt56LE(buf, offset) {
      return (((buf.readInt8(offset + 6) || 0) << 16) + buf.readUInt16LE(offset + 4)) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);
    }
    module.exports.readInt56LE = readInt56LE;
    function writeInt56LE(buf, val, offset) {
      val = check_value(val, -36028797018963970, 36028797018963970);
      check_bounds(buf, offset, 7);
      if (val < 36028797018963970) {
        buf.writeInt32LE(val & -1, offset);
        var hi = Math.floor(val * SHIFT_RIGHT_32);
        buf.writeUInt16LE(hi & 65535, offset + 4);
        buf.writeInt8(hi >> 16, offset + 6);
      } else {
        buf[offset] = 255;
        buf[offset + 1] = 255;
        buf[offset + 2] = 255;
        buf[offset + 3] = 255;
        buf[offset + 4] = 255;
        buf[offset + 5] = 255;
        buf[offset + 6] = 127;
      }
    }
    module.exports.writeInt56LE = writeInt56LE;
    function readInt64LE(buf, offset) {
      return buf.readInt32LE(offset + 4) * SHIFT_LEFT_32 + buf.readUInt32LE(offset);
    }
    module.exports.readInt64LE = readInt64LE;
    function writeInt64LE(buf, val, offset) {
      val = check_value(val, -9223372036854776e3, 9223372036854776e3);
      check_bounds(buf, offset, 8);
      if (val < 9223372036854776e3) {
        buf.writeInt32LE(val & -1, offset);
        buf.writeInt32LE(Math.floor(val * SHIFT_RIGHT_32), offset + 4);
      } else {
        buf[offset] = 255;
        buf[offset + 1] = 255;
        buf[offset + 2] = 255;
        buf[offset + 3] = 255;
        buf[offset + 4] = 255;
        buf[offset + 5] = 255;
        buf[offset + 6] = 255;
        buf[offset + 7] = 127;
      }
    }
    module.exports.writeInt64LE = writeInt64LE;
  })(bufferMoreInts);
  return bufferMoreInts.exports;
}
var hasRequiredCodec;
function requireCodec() {
  if (hasRequiredCodec) return codec;
  hasRequiredCodec = 1;
  var ints = requireBufferMoreInts();
  function isFloatingPoint(n) {
    return n >= 9223372036854776e3 || Math.abs(n) < 1125899906842624 && Math.floor(n) !== n;
  }
  function encodeTable(buffer, val, offset) {
    var start = offset;
    offset += 4;
    for (var key in val) {
      if (val[key] !== void 0) {
        var len = Buffer.byteLength(key);
        buffer.writeUInt8(len, offset);
        offset++;
        buffer.write(key, offset, "utf8");
        offset += len;
        offset += encodeFieldValue(buffer, val[key], offset);
      }
    }
    var size = offset - start;
    buffer.writeUInt32BE(size - 4, start);
    return size;
  }
  function encodeArray(buffer, val, offset) {
    var start = offset;
    offset += 4;
    for (var i = 0, num = val.length; i < num; i++) {
      offset += encodeFieldValue(buffer, val[i], offset);
    }
    var size = offset - start;
    buffer.writeUInt32BE(size - 4, start);
    return size;
  }
  function encodeFieldValue(buffer, value, offset) {
    var start = offset;
    var type = typeof value, val = value;
    if (value && type === "object" && value.hasOwnProperty("!")) {
      val = value.value;
      type = value["!"];
    }
    if (type == "number") {
      if (isFloatingPoint(val)) {
        type = "double";
      } else {
        if (val < 128 && val >= -128) {
          type = "byte";
        } else if (val >= -32768 && val < 32768) {
          type = "short";
        } else if (val >= -2147483648 && val < 2147483648) {
          type = "int";
        } else {
          type = "long";
        }
      }
    }
    function tag(t) {
      buffer.write(t, offset);
      offset++;
    }
    switch (type) {
      case "string":
        var len = Buffer.byteLength(val, "utf8");
        tag("S");
        buffer.writeUInt32BE(len, offset);
        offset += 4;
        buffer.write(val, offset, "utf8");
        offset += len;
        break;
      case "object":
        if (val === null) {
          tag("V");
        } else if (Array.isArray(val)) {
          tag("A");
          offset += encodeArray(buffer, val, offset);
        } else if (Buffer.isBuffer(val)) {
          tag("x");
          buffer.writeUInt32BE(val.length, offset);
          offset += 4;
          val.copy(buffer, offset);
          offset += val.length;
        } else {
          tag("F");
          offset += encodeTable(buffer, val, offset);
        }
        break;
      case "boolean":
        tag("t");
        buffer.writeUInt8(val ? 1 : 0, offset);
        offset++;
        break;
      // These are the types that are either guessed above, or
      // explicitly given using the {'!': type} notation.
      case "double":
      case "float64":
        tag("d");
        buffer.writeDoubleBE(val, offset);
        offset += 8;
        break;
      case "byte":
      case "int8":
        tag("b");
        buffer.writeInt8(val, offset);
        offset++;
        break;
      case "unsignedbyte":
      case "uint8":
        tag("B");
        buffer.writeUInt8(val, offset);
        offset++;
        break;
      case "short":
      case "int16":
        tag("s");
        buffer.writeInt16BE(val, offset);
        offset += 2;
        break;
      case "unsignedshort":
      case "uint16":
        tag("u");
        buffer.writeUInt16BE(val, offset);
        offset += 2;
        break;
      case "int":
      case "int32":
        tag("I");
        buffer.writeInt32BE(val, offset);
        offset += 4;
        break;
      case "unsignedint":
      case "uint32":
        tag("i");
        buffer.writeUInt32BE(val, offset);
        offset += 4;
        break;
      case "long":
      case "int64":
        tag("l");
        ints.writeInt64BE(buffer, val, offset);
        offset += 8;
        break;
      // Now for exotic types, those can _only_ be denoted by using
      // `{'!': type, value: val}
      case "timestamp":
        tag("T");
        ints.writeUInt64BE(buffer, val, offset);
        offset += 8;
        break;
      case "float":
        tag("f");
        buffer.writeFloatBE(val, offset);
        offset += 4;
        break;
      case "decimal":
        tag("D");
        if (val.hasOwnProperty("places") && val.hasOwnProperty("digits") && val.places >= 0 && val.places < 256) {
          buffer[offset] = val.places;
          offset++;
          buffer.writeUInt32BE(val.digits, offset);
          offset += 4;
        } else throw new TypeError(
          "Decimal value must be {'places': 0..255, 'digits': uint32}, got " + JSON.stringify(val)
        );
        break;
      default:
        throw new TypeError("Unknown type to encode: " + type);
    }
    return offset - start;
  }
  function decodeFields(slice) {
    var fields = {}, offset = 0, size = slice.length;
    var len, key, val;
    function decodeFieldValue() {
      var tag = String.fromCharCode(slice[offset]);
      offset++;
      switch (tag) {
        case "b":
          val = slice.readInt8(offset);
          offset++;
          break;
        case "B":
          val = slice.readUInt8(offset);
          offset++;
          break;
        case "S":
          len = slice.readUInt32BE(offset);
          offset += 4;
          val = slice.toString("utf8", offset, offset + len);
          offset += len;
          break;
        case "I":
          val = slice.readInt32BE(offset);
          offset += 4;
          break;
        case "i":
          val = slice.readUInt32BE(offset);
          offset += 4;
          break;
        case "D":
          var places = slice[offset];
          offset++;
          var digits = slice.readUInt32BE(offset);
          offset += 4;
          val = { "!": "decimal", value: { places, digits } };
          break;
        case "T":
          val = ints.readUInt64BE(slice, offset);
          offset += 8;
          val = { "!": "timestamp", value: val };
          break;
        case "F":
          len = slice.readUInt32BE(offset);
          offset += 4;
          val = decodeFields(slice.subarray(offset, offset + len));
          offset += len;
          break;
        case "A":
          len = slice.readUInt32BE(offset);
          offset += 4;
          decodeArray(offset + len);
          break;
        case "d":
          val = slice.readDoubleBE(offset);
          offset += 8;
          break;
        case "f":
          val = slice.readFloatBE(offset);
          offset += 4;
          break;
        case "l":
          val = ints.readInt64BE(slice, offset);
          offset += 8;
          break;
        case "s":
          val = slice.readInt16BE(offset);
          offset += 2;
          break;
        case "u":
          val = slice.readUInt16BE(offset);
          offset += 2;
          break;
        case "t":
          val = slice[offset] != 0;
          offset++;
          break;
        case "V":
          val = null;
          break;
        case "x":
          len = slice.readUInt32BE(offset);
          offset += 4;
          val = slice.subarray(offset, offset + len);
          offset += len;
          break;
        default:
          throw new TypeError('Unexpected type tag "' + tag + '"');
      }
    }
    function decodeArray(until) {
      var vals = [];
      while (offset < until) {
        decodeFieldValue();
        vals.push(val);
      }
      val = vals;
    }
    while (offset < size) {
      len = slice.readUInt8(offset);
      offset++;
      key = slice.toString("utf8", offset, offset + len);
      offset += len;
      decodeFieldValue();
      fields[key] = val;
    }
    return fields;
  }
  codec.encodeTable = encodeTable;
  codec.decodeFields = decodeFields;
  return codec;
}
/** @preserve This file is generated by the script
 * ../bin/generate-defs.js, which is not in general included in a
 * distribution, but is available in the source repository e.g. at
 * https://github.com/squaremo/amqp.node/
 */
var hasRequiredDefs;
function requireDefs() {
  if (hasRequiredDefs) return defs;
  hasRequiredDefs = 1;
  function decodeBasicQos(buffer) {
    var val, offset = 0, fields = {
      prefetchSize: void 0,
      prefetchCount: void 0,
      global: void 0
    };
    val = buffer.readUInt32BE(offset);
    offset += 4;
    fields.prefetchSize = val;
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.prefetchCount = val;
    val = !!(1 & buffer[offset]);
    fields.global = val;
    return fields;
  }
  function encodeBasicQos(channel2, fields) {
    var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(19);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932170, 7);
    offset = 11;
    val = fields.prefetchSize;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'prefetchSize' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt32BE(val, offset);
    offset += 4;
    val = fields.prefetchCount;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'prefetchCount' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.global;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicQosOk(buffer) {
    return {};
  }
  function encodeBasicQosOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932171, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicConsume(buffer) {
    var val, len, offset = 0, fields = {
      ticket: void 0,
      queue: void 0,
      consumerTag: void 0,
      noLocal: void 0,
      noAck: void 0,
      exclusive: void 0,
      nowait: void 0,
      arguments: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.ticket = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.queue = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.consumerTag = val;
    val = !!(1 & buffer[offset]);
    fields.noLocal = val;
    val = !!(2 & buffer[offset]);
    fields.noAck = val;
    val = !!(4 & buffer[offset]);
    fields.exclusive = val;
    val = !!(8 & buffer[offset]);
    fields.nowait = val;
    offset++;
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = decodeFields(buffer.subarray(offset, offset + len));
    offset += len;
    fields.arguments = val;
    return fields;
  }
  function encodeBasicConsume(channel2, fields) {
    var len, offset = 0, val = null, bits = 0, varyingSize = 0, scratchOffset = 0;
    val = fields.queue;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'queue' is the wrong type; must be a string (up to 255 chars)");
    var queue_len = Buffer.byteLength(val, "utf8");
    varyingSize += queue_len;
    val = fields.consumerTag;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'consumerTag' is the wrong type; must be a string (up to 255 chars)");
    var consumerTag_len = Buffer.byteLength(val, "utf8");
    varyingSize += consumerTag_len;
    val = fields.arguments;
    if (void 0 === val) val = {};
    else if ("object" != typeof val) throw new TypeError("Field 'arguments' is the wrong type; must be an object");
    len = encodeTable(SCRATCH, val, scratchOffset);
    var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);
    scratchOffset += len;
    varyingSize += arguments_encoded.length;
    var buffer = Buffer.alloc(17 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932180, 7);
    offset = 11;
    val = fields.ticket;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'ticket' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.queue;
    void 0 === val && (val = "");
    buffer[offset] = queue_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += queue_len;
    val = fields.consumerTag;
    void 0 === val && (val = "");
    buffer[offset] = consumerTag_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += consumerTag_len;
    val = fields.noLocal;
    void 0 === val && (val = false);
    val && (bits += 1);
    val = fields.noAck;
    void 0 === val && (val = false);
    val && (bits += 2);
    val = fields.exclusive;
    void 0 === val && (val = false);
    val && (bits += 4);
    val = fields.nowait;
    void 0 === val && (val = false);
    val && (bits += 8);
    buffer[offset] = bits;
    offset++;
    bits = 0;
    offset += arguments_encoded.copy(buffer, offset);
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicConsumeOk(buffer) {
    var val, len, offset = 0, fields = {
      consumerTag: void 0
    };
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.consumerTag = val;
    return fields;
  }
  function encodeBasicConsumeOk(channel2, fields) {
    var offset = 0, val = null, varyingSize = 0;
    val = fields.consumerTag;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'consumerTag'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'consumerTag' is the wrong type; must be a string (up to 255 chars)");
    var consumerTag_len = Buffer.byteLength(val, "utf8");
    varyingSize += consumerTag_len;
    var buffer = Buffer.alloc(13 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932181, 7);
    offset = 11;
    val = fields.consumerTag;
    void 0 === val && (val = void 0);
    buffer[offset] = consumerTag_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += consumerTag_len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicCancel(buffer) {
    var val, len, offset = 0, fields = {
      consumerTag: void 0,
      nowait: void 0
    };
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.consumerTag = val;
    val = !!(1 & buffer[offset]);
    fields.nowait = val;
    return fields;
  }
  function encodeBasicCancel(channel2, fields) {
    var offset = 0, val = null, bits = 0, varyingSize = 0;
    val = fields.consumerTag;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'consumerTag'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'consumerTag' is the wrong type; must be a string (up to 255 chars)");
    var consumerTag_len = Buffer.byteLength(val, "utf8");
    varyingSize += consumerTag_len;
    var buffer = Buffer.alloc(14 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932190, 7);
    offset = 11;
    val = fields.consumerTag;
    void 0 === val && (val = void 0);
    buffer[offset] = consumerTag_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += consumerTag_len;
    val = fields.nowait;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicCancelOk(buffer) {
    var val, len, offset = 0, fields = {
      consumerTag: void 0
    };
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.consumerTag = val;
    return fields;
  }
  function encodeBasicCancelOk(channel2, fields) {
    var offset = 0, val = null, varyingSize = 0;
    val = fields.consumerTag;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'consumerTag'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'consumerTag' is the wrong type; must be a string (up to 255 chars)");
    var consumerTag_len = Buffer.byteLength(val, "utf8");
    varyingSize += consumerTag_len;
    var buffer = Buffer.alloc(13 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932191, 7);
    offset = 11;
    val = fields.consumerTag;
    void 0 === val && (val = void 0);
    buffer[offset] = consumerTag_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += consumerTag_len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicPublish(buffer) {
    var val, len, offset = 0, fields = {
      ticket: void 0,
      exchange: void 0,
      routingKey: void 0,
      mandatory: void 0,
      immediate: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.ticket = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.exchange = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.routingKey = val;
    val = !!(1 & buffer[offset]);
    fields.mandatory = val;
    val = !!(2 & buffer[offset]);
    fields.immediate = val;
    return fields;
  }
  function encodeBasicPublish(channel2, fields) {
    var offset = 0, val = null, bits = 0, varyingSize = 0;
    val = fields.exchange;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'exchange' is the wrong type; must be a string (up to 255 chars)");
    var exchange_len = Buffer.byteLength(val, "utf8");
    varyingSize += exchange_len;
    val = fields.routingKey;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'routingKey' is the wrong type; must be a string (up to 255 chars)");
    var routingKey_len = Buffer.byteLength(val, "utf8");
    varyingSize += routingKey_len;
    var buffer = Buffer.alloc(17 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932200, 7);
    offset = 11;
    val = fields.ticket;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'ticket' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.exchange;
    void 0 === val && (val = "");
    buffer[offset] = exchange_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += exchange_len;
    val = fields.routingKey;
    void 0 === val && (val = "");
    buffer[offset] = routingKey_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += routingKey_len;
    val = fields.mandatory;
    void 0 === val && (val = false);
    val && (bits += 1);
    val = fields.immediate;
    void 0 === val && (val = false);
    val && (bits += 2);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicReturn(buffer) {
    var val, len, offset = 0, fields = {
      replyCode: void 0,
      replyText: void 0,
      exchange: void 0,
      routingKey: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.replyCode = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.replyText = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.exchange = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.routingKey = val;
    return fields;
  }
  function encodeBasicReturn(channel2, fields) {
    var offset = 0, val = null, varyingSize = 0;
    val = fields.replyText;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'replyText' is the wrong type; must be a string (up to 255 chars)");
    var replyText_len = Buffer.byteLength(val, "utf8");
    varyingSize += replyText_len;
    val = fields.exchange;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'exchange'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'exchange' is the wrong type; must be a string (up to 255 chars)");
    var exchange_len = Buffer.byteLength(val, "utf8");
    varyingSize += exchange_len;
    val = fields.routingKey;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'routingKey'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'routingKey' is the wrong type; must be a string (up to 255 chars)");
    var routingKey_len = Buffer.byteLength(val, "utf8");
    varyingSize += routingKey_len;
    var buffer = Buffer.alloc(17 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932210, 7);
    offset = 11;
    val = fields.replyCode;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'replyCode'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'replyCode' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.replyText;
    void 0 === val && (val = "");
    buffer[offset] = replyText_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += replyText_len;
    val = fields.exchange;
    void 0 === val && (val = void 0);
    buffer[offset] = exchange_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += exchange_len;
    val = fields.routingKey;
    void 0 === val && (val = void 0);
    buffer[offset] = routingKey_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += routingKey_len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicDeliver(buffer) {
    var val, len, offset = 0, fields = {
      consumerTag: void 0,
      deliveryTag: void 0,
      redelivered: void 0,
      exchange: void 0,
      routingKey: void 0
    };
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.consumerTag = val;
    val = ints.readUInt64BE(buffer, offset);
    offset += 8;
    fields.deliveryTag = val;
    val = !!(1 & buffer[offset]);
    fields.redelivered = val;
    offset++;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.exchange = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.routingKey = val;
    return fields;
  }
  function encodeBasicDeliver(channel2, fields) {
    var offset = 0, val = null, bits = 0, varyingSize = 0;
    val = fields.consumerTag;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'consumerTag'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'consumerTag' is the wrong type; must be a string (up to 255 chars)");
    var consumerTag_len = Buffer.byteLength(val, "utf8");
    varyingSize += consumerTag_len;
    val = fields.exchange;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'exchange'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'exchange' is the wrong type; must be a string (up to 255 chars)");
    var exchange_len = Buffer.byteLength(val, "utf8");
    varyingSize += exchange_len;
    val = fields.routingKey;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'routingKey'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'routingKey' is the wrong type; must be a string (up to 255 chars)");
    var routingKey_len = Buffer.byteLength(val, "utf8");
    varyingSize += routingKey_len;
    var buffer = Buffer.alloc(24 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932220, 7);
    offset = 11;
    val = fields.consumerTag;
    void 0 === val && (val = void 0);
    buffer[offset] = consumerTag_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += consumerTag_len;
    val = fields.deliveryTag;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'deliveryTag'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'deliveryTag' is the wrong type; must be a number (but not NaN)");
    ints.writeUInt64BE(buffer, val, offset);
    offset += 8;
    val = fields.redelivered;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    bits = 0;
    val = fields.exchange;
    void 0 === val && (val = void 0);
    buffer[offset] = exchange_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += exchange_len;
    val = fields.routingKey;
    void 0 === val && (val = void 0);
    buffer[offset] = routingKey_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += routingKey_len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicGet(buffer) {
    var val, len, offset = 0, fields = {
      ticket: void 0,
      queue: void 0,
      noAck: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.ticket = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.queue = val;
    val = !!(1 & buffer[offset]);
    fields.noAck = val;
    return fields;
  }
  function encodeBasicGet(channel2, fields) {
    var offset = 0, val = null, bits = 0, varyingSize = 0;
    val = fields.queue;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'queue' is the wrong type; must be a string (up to 255 chars)");
    var queue_len = Buffer.byteLength(val, "utf8");
    varyingSize += queue_len;
    var buffer = Buffer.alloc(16 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932230, 7);
    offset = 11;
    val = fields.ticket;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'ticket' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.queue;
    void 0 === val && (val = "");
    buffer[offset] = queue_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += queue_len;
    val = fields.noAck;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicGetOk(buffer) {
    var val, len, offset = 0, fields = {
      deliveryTag: void 0,
      redelivered: void 0,
      exchange: void 0,
      routingKey: void 0,
      messageCount: void 0
    };
    val = ints.readUInt64BE(buffer, offset);
    offset += 8;
    fields.deliveryTag = val;
    val = !!(1 & buffer[offset]);
    fields.redelivered = val;
    offset++;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.exchange = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.routingKey = val;
    val = buffer.readUInt32BE(offset);
    offset += 4;
    fields.messageCount = val;
    return fields;
  }
  function encodeBasicGetOk(channel2, fields) {
    var offset = 0, val = null, bits = 0, varyingSize = 0;
    val = fields.exchange;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'exchange'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'exchange' is the wrong type; must be a string (up to 255 chars)");
    var exchange_len = Buffer.byteLength(val, "utf8");
    varyingSize += exchange_len;
    val = fields.routingKey;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'routingKey'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'routingKey' is the wrong type; must be a string (up to 255 chars)");
    var routingKey_len = Buffer.byteLength(val, "utf8");
    varyingSize += routingKey_len;
    var buffer = Buffer.alloc(27 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932231, 7);
    offset = 11;
    val = fields.deliveryTag;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'deliveryTag'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'deliveryTag' is the wrong type; must be a number (but not NaN)");
    ints.writeUInt64BE(buffer, val, offset);
    offset += 8;
    val = fields.redelivered;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    bits = 0;
    val = fields.exchange;
    void 0 === val && (val = void 0);
    buffer[offset] = exchange_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += exchange_len;
    val = fields.routingKey;
    void 0 === val && (val = void 0);
    buffer[offset] = routingKey_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += routingKey_len;
    val = fields.messageCount;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'messageCount'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'messageCount' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt32BE(val, offset);
    offset += 4;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicGetEmpty(buffer) {
    var val, len, offset = 0, fields = {
      clusterId: void 0
    };
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.clusterId = val;
    return fields;
  }
  function encodeBasicGetEmpty(channel2, fields) {
    var offset = 0, val = null, varyingSize = 0;
    val = fields.clusterId;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'clusterId' is the wrong type; must be a string (up to 255 chars)");
    var clusterId_len = Buffer.byteLength(val, "utf8");
    varyingSize += clusterId_len;
    var buffer = Buffer.alloc(13 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932232, 7);
    offset = 11;
    val = fields.clusterId;
    void 0 === val && (val = "");
    buffer[offset] = clusterId_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += clusterId_len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicAck(buffer) {
    var val, offset = 0, fields = {
      deliveryTag: void 0,
      multiple: void 0
    };
    val = ints.readUInt64BE(buffer, offset);
    offset += 8;
    fields.deliveryTag = val;
    val = !!(1 & buffer[offset]);
    fields.multiple = val;
    return fields;
  }
  function encodeBasicAck(channel2, fields) {
    var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(21);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932240, 7);
    offset = 11;
    val = fields.deliveryTag;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'deliveryTag' is the wrong type; must be a number (but not NaN)");
    ints.writeUInt64BE(buffer, val, offset);
    offset += 8;
    val = fields.multiple;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicReject(buffer) {
    var val, offset = 0, fields = {
      deliveryTag: void 0,
      requeue: void 0
    };
    val = ints.readUInt64BE(buffer, offset);
    offset += 8;
    fields.deliveryTag = val;
    val = !!(1 & buffer[offset]);
    fields.requeue = val;
    return fields;
  }
  function encodeBasicReject(channel2, fields) {
    var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(21);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932250, 7);
    offset = 11;
    val = fields.deliveryTag;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'deliveryTag'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'deliveryTag' is the wrong type; must be a number (but not NaN)");
    ints.writeUInt64BE(buffer, val, offset);
    offset += 8;
    val = fields.requeue;
    void 0 === val && (val = true);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicRecoverAsync(buffer) {
    var val, fields = {
      requeue: void 0
    };
    val = !!(1 & buffer[0]);
    fields.requeue = val;
    return fields;
  }
  function encodeBasicRecoverAsync(channel2, fields) {
    var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(13);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932260, 7);
    offset = 11;
    val = fields.requeue;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicRecover(buffer) {
    var val, fields = {
      requeue: void 0
    };
    val = !!(1 & buffer[0]);
    fields.requeue = val;
    return fields;
  }
  function encodeBasicRecover(channel2, fields) {
    var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(13);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932270, 7);
    offset = 11;
    val = fields.requeue;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicRecoverOk(buffer) {
    return {};
  }
  function encodeBasicRecoverOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932271, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeBasicNack(buffer) {
    var val, offset = 0, fields = {
      deliveryTag: void 0,
      multiple: void 0,
      requeue: void 0
    };
    val = ints.readUInt64BE(buffer, offset);
    offset += 8;
    fields.deliveryTag = val;
    val = !!(1 & buffer[offset]);
    fields.multiple = val;
    val = !!(2 & buffer[offset]);
    fields.requeue = val;
    return fields;
  }
  function encodeBasicNack(channel2, fields) {
    var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(21);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932280, 7);
    offset = 11;
    val = fields.deliveryTag;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'deliveryTag' is the wrong type; must be a number (but not NaN)");
    ints.writeUInt64BE(buffer, val, offset);
    offset += 8;
    val = fields.multiple;
    void 0 === val && (val = false);
    val && (bits += 1);
    val = fields.requeue;
    void 0 === val && (val = true);
    val && (bits += 2);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionStart(buffer) {
    var val, len, offset = 0, fields = {
      versionMajor: void 0,
      versionMinor: void 0,
      serverProperties: void 0,
      mechanisms: void 0,
      locales: void 0
    };
    val = buffer[offset];
    offset++;
    fields.versionMajor = val;
    val = buffer[offset];
    offset++;
    fields.versionMinor = val;
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = decodeFields(buffer.subarray(offset, offset + len));
    offset += len;
    fields.serverProperties = val;
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = buffer.subarray(offset, offset + len);
    offset += len;
    fields.mechanisms = val;
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = buffer.subarray(offset, offset + len);
    offset += len;
    fields.locales = val;
    return fields;
  }
  function encodeConnectionStart(channel2, fields) {
    var len, offset = 0, val = null, varyingSize = 0, scratchOffset = 0;
    val = fields.serverProperties;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'serverProperties'");
    if ("object" != typeof val) throw new TypeError("Field 'serverProperties' is the wrong type; must be an object");
    len = encodeTable(SCRATCH, val, scratchOffset);
    var serverProperties_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);
    scratchOffset += len;
    varyingSize += serverProperties_encoded.length;
    val = fields.mechanisms;
    if (void 0 === val) val = Buffer.from("PLAIN");
    else if (!Buffer.isBuffer(val)) throw new TypeError("Field 'mechanisms' is the wrong type; must be a Buffer");
    varyingSize += val.length;
    val = fields.locales;
    if (void 0 === val) val = Buffer.from("en_US");
    else if (!Buffer.isBuffer(val)) throw new TypeError("Field 'locales' is the wrong type; must be a Buffer");
    varyingSize += val.length;
    var buffer = Buffer.alloc(22 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655370, 7);
    offset = 11;
    val = fields.versionMajor;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'versionMajor' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt8(val, offset);
    offset++;
    val = fields.versionMinor;
    if (void 0 === val) val = 9;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'versionMinor' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt8(val, offset);
    offset++;
    offset += serverProperties_encoded.copy(buffer, offset);
    val = fields.mechanisms;
    void 0 === val && (val = Buffer.from("PLAIN"));
    len = val.length;
    buffer.writeUInt32BE(len, offset);
    offset += 4;
    val.copy(buffer, offset);
    offset += len;
    val = fields.locales;
    void 0 === val && (val = Buffer.from("en_US"));
    len = val.length;
    buffer.writeUInt32BE(len, offset);
    offset += 4;
    val.copy(buffer, offset);
    offset += len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionStartOk(buffer) {
    var val, len, offset = 0, fields = {
      clientProperties: void 0,
      mechanism: void 0,
      response: void 0,
      locale: void 0
    };
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = decodeFields(buffer.subarray(offset, offset + len));
    offset += len;
    fields.clientProperties = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.mechanism = val;
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = buffer.subarray(offset, offset + len);
    offset += len;
    fields.response = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.locale = val;
    return fields;
  }
  function encodeConnectionStartOk(channel2, fields) {
    var len, offset = 0, val = null, varyingSize = 0, scratchOffset = 0;
    val = fields.clientProperties;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'clientProperties'");
    if ("object" != typeof val) throw new TypeError("Field 'clientProperties' is the wrong type; must be an object");
    len = encodeTable(SCRATCH, val, scratchOffset);
    var clientProperties_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);
    scratchOffset += len;
    varyingSize += clientProperties_encoded.length;
    val = fields.mechanism;
    if (void 0 === val) val = "PLAIN";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'mechanism' is the wrong type; must be a string (up to 255 chars)");
    var mechanism_len = Buffer.byteLength(val, "utf8");
    varyingSize += mechanism_len;
    val = fields.response;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'response'");
    if (!Buffer.isBuffer(val)) throw new TypeError("Field 'response' is the wrong type; must be a Buffer");
    varyingSize += val.length;
    val = fields.locale;
    if (void 0 === val) val = "en_US";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'locale' is the wrong type; must be a string (up to 255 chars)");
    var locale_len = Buffer.byteLength(val, "utf8");
    varyingSize += locale_len;
    var buffer = Buffer.alloc(18 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655371, 7);
    offset = 11;
    offset += clientProperties_encoded.copy(buffer, offset);
    val = fields.mechanism;
    void 0 === val && (val = "PLAIN");
    buffer[offset] = mechanism_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += mechanism_len;
    val = fields.response;
    void 0 === val && (val = Buffer.from(void 0));
    len = val.length;
    buffer.writeUInt32BE(len, offset);
    offset += 4;
    val.copy(buffer, offset);
    offset += len;
    val = fields.locale;
    void 0 === val && (val = "en_US");
    buffer[offset] = locale_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += locale_len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionSecure(buffer) {
    var val, len, offset = 0, fields = {
      challenge: void 0
    };
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = buffer.subarray(offset, offset + len);
    offset += len;
    fields.challenge = val;
    return fields;
  }
  function encodeConnectionSecure(channel2, fields) {
    var len, offset = 0, val = null, varyingSize = 0;
    val = fields.challenge;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'challenge'");
    if (!Buffer.isBuffer(val)) throw new TypeError("Field 'challenge' is the wrong type; must be a Buffer");
    varyingSize += val.length;
    var buffer = Buffer.alloc(16 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655380, 7);
    offset = 11;
    val = fields.challenge;
    void 0 === val && (val = Buffer.from(void 0));
    len = val.length;
    buffer.writeUInt32BE(len, offset);
    offset += 4;
    val.copy(buffer, offset);
    offset += len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionSecureOk(buffer) {
    var val, len, offset = 0, fields = {
      response: void 0
    };
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = buffer.subarray(offset, offset + len);
    offset += len;
    fields.response = val;
    return fields;
  }
  function encodeConnectionSecureOk(channel2, fields) {
    var len, offset = 0, val = null, varyingSize = 0;
    val = fields.response;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'response'");
    if (!Buffer.isBuffer(val)) throw new TypeError("Field 'response' is the wrong type; must be a Buffer");
    varyingSize += val.length;
    var buffer = Buffer.alloc(16 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655381, 7);
    offset = 11;
    val = fields.response;
    void 0 === val && (val = Buffer.from(void 0));
    len = val.length;
    buffer.writeUInt32BE(len, offset);
    offset += 4;
    val.copy(buffer, offset);
    offset += len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionTune(buffer) {
    var val, offset = 0, fields = {
      channelMax: void 0,
      frameMax: void 0,
      heartbeat: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.channelMax = val;
    val = buffer.readUInt32BE(offset);
    offset += 4;
    fields.frameMax = val;
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.heartbeat = val;
    return fields;
  }
  function encodeConnectionTune(channel2, fields) {
    var offset = 0, val = null, buffer = Buffer.alloc(20);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655390, 7);
    offset = 11;
    val = fields.channelMax;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'channelMax' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.frameMax;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'frameMax' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt32BE(val, offset);
    offset += 4;
    val = fields.heartbeat;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'heartbeat' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionTuneOk(buffer) {
    var val, offset = 0, fields = {
      channelMax: void 0,
      frameMax: void 0,
      heartbeat: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.channelMax = val;
    val = buffer.readUInt32BE(offset);
    offset += 4;
    fields.frameMax = val;
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.heartbeat = val;
    return fields;
  }
  function encodeConnectionTuneOk(channel2, fields) {
    var offset = 0, val = null, buffer = Buffer.alloc(20);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655391, 7);
    offset = 11;
    val = fields.channelMax;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'channelMax' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.frameMax;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'frameMax' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt32BE(val, offset);
    offset += 4;
    val = fields.heartbeat;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'heartbeat' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionOpen(buffer) {
    var val, len, offset = 0, fields = {
      virtualHost: void 0,
      capabilities: void 0,
      insist: void 0
    };
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.virtualHost = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.capabilities = val;
    val = !!(1 & buffer[offset]);
    fields.insist = val;
    return fields;
  }
  function encodeConnectionOpen(channel2, fields) {
    var offset = 0, val = null, bits = 0, varyingSize = 0;
    val = fields.virtualHost;
    if (void 0 === val) val = "/";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'virtualHost' is the wrong type; must be a string (up to 255 chars)");
    var virtualHost_len = Buffer.byteLength(val, "utf8");
    varyingSize += virtualHost_len;
    val = fields.capabilities;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'capabilities' is the wrong type; must be a string (up to 255 chars)");
    var capabilities_len = Buffer.byteLength(val, "utf8");
    varyingSize += capabilities_len;
    var buffer = Buffer.alloc(15 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655400, 7);
    offset = 11;
    val = fields.virtualHost;
    void 0 === val && (val = "/");
    buffer[offset] = virtualHost_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += virtualHost_len;
    val = fields.capabilities;
    void 0 === val && (val = "");
    buffer[offset] = capabilities_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += capabilities_len;
    val = fields.insist;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionOpenOk(buffer) {
    var val, len, offset = 0, fields = {
      knownHosts: void 0
    };
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.knownHosts = val;
    return fields;
  }
  function encodeConnectionOpenOk(channel2, fields) {
    var offset = 0, val = null, varyingSize = 0;
    val = fields.knownHosts;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'knownHosts' is the wrong type; must be a string (up to 255 chars)");
    var knownHosts_len = Buffer.byteLength(val, "utf8");
    varyingSize += knownHosts_len;
    var buffer = Buffer.alloc(13 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655401, 7);
    offset = 11;
    val = fields.knownHosts;
    void 0 === val && (val = "");
    buffer[offset] = knownHosts_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += knownHosts_len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionClose(buffer) {
    var val, len, offset = 0, fields = {
      replyCode: void 0,
      replyText: void 0,
      classId: void 0,
      methodId: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.replyCode = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.replyText = val;
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.classId = val;
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.methodId = val;
    return fields;
  }
  function encodeConnectionClose(channel2, fields) {
    var offset = 0, val = null, varyingSize = 0;
    val = fields.replyText;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'replyText' is the wrong type; must be a string (up to 255 chars)");
    var replyText_len = Buffer.byteLength(val, "utf8");
    varyingSize += replyText_len;
    var buffer = Buffer.alloc(19 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655410, 7);
    offset = 11;
    val = fields.replyCode;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'replyCode'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'replyCode' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.replyText;
    void 0 === val && (val = "");
    buffer[offset] = replyText_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += replyText_len;
    val = fields.classId;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'classId'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'classId' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.methodId;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'methodId'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'methodId' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionCloseOk(buffer) {
    return {};
  }
  function encodeConnectionCloseOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655411, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionBlocked(buffer) {
    var val, len, offset = 0, fields = {
      reason: void 0
    };
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.reason = val;
    return fields;
  }
  function encodeConnectionBlocked(channel2, fields) {
    var offset = 0, val = null, varyingSize = 0;
    val = fields.reason;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'reason' is the wrong type; must be a string (up to 255 chars)");
    var reason_len = Buffer.byteLength(val, "utf8");
    varyingSize += reason_len;
    var buffer = Buffer.alloc(13 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655420, 7);
    offset = 11;
    val = fields.reason;
    void 0 === val && (val = "");
    buffer[offset] = reason_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += reason_len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionUnblocked(buffer) {
    return {};
  }
  function encodeConnectionUnblocked(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655421, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionUpdateSecret(buffer) {
    var val, len, offset = 0, fields = {
      newSecret: void 0,
      reason: void 0
    };
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = buffer.subarray(offset, offset + len);
    offset += len;
    fields.newSecret = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.reason = val;
    return fields;
  }
  function encodeConnectionUpdateSecret(channel2, fields) {
    var len, offset = 0, val = null, varyingSize = 0;
    val = fields.newSecret;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'newSecret'");
    if (!Buffer.isBuffer(val)) throw new TypeError("Field 'newSecret' is the wrong type; must be a Buffer");
    varyingSize += val.length;
    val = fields.reason;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'reason'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'reason' is the wrong type; must be a string (up to 255 chars)");
    var reason_len = Buffer.byteLength(val, "utf8");
    varyingSize += reason_len;
    var buffer = Buffer.alloc(17 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655430, 7);
    offset = 11;
    val = fields.newSecret;
    void 0 === val && (val = Buffer.from(void 0));
    len = val.length;
    buffer.writeUInt32BE(len, offset);
    offset += 4;
    val.copy(buffer, offset);
    offset += len;
    val = fields.reason;
    void 0 === val && (val = void 0);
    buffer[offset] = reason_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += reason_len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConnectionUpdateSecretOk(buffer) {
    return {};
  }
  function encodeConnectionUpdateSecretOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(655431, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeChannelOpen(buffer) {
    var val, len, offset = 0, fields = {
      outOfBand: void 0
    };
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.outOfBand = val;
    return fields;
  }
  function encodeChannelOpen(channel2, fields) {
    var offset = 0, val = null, varyingSize = 0;
    val = fields.outOfBand;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'outOfBand' is the wrong type; must be a string (up to 255 chars)");
    var outOfBand_len = Buffer.byteLength(val, "utf8");
    varyingSize += outOfBand_len;
    var buffer = Buffer.alloc(13 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(1310730, 7);
    offset = 11;
    val = fields.outOfBand;
    void 0 === val && (val = "");
    buffer[offset] = outOfBand_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += outOfBand_len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeChannelOpenOk(buffer) {
    var val, len, offset = 0, fields = {
      channelId: void 0
    };
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = buffer.subarray(offset, offset + len);
    offset += len;
    fields.channelId = val;
    return fields;
  }
  function encodeChannelOpenOk(channel2, fields) {
    var len, offset = 0, val = null, varyingSize = 0;
    val = fields.channelId;
    if (void 0 === val) val = Buffer.from("");
    else if (!Buffer.isBuffer(val)) throw new TypeError("Field 'channelId' is the wrong type; must be a Buffer");
    varyingSize += val.length;
    var buffer = Buffer.alloc(16 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(1310731, 7);
    offset = 11;
    val = fields.channelId;
    void 0 === val && (val = Buffer.from(""));
    len = val.length;
    buffer.writeUInt32BE(len, offset);
    offset += 4;
    val.copy(buffer, offset);
    offset += len;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeChannelFlow(buffer) {
    var val, fields = {
      active: void 0
    };
    val = !!(1 & buffer[0]);
    fields.active = val;
    return fields;
  }
  function encodeChannelFlow(channel2, fields) {
    var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(13);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(1310740, 7);
    offset = 11;
    val = fields.active;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'active'");
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeChannelFlowOk(buffer) {
    var val, fields = {
      active: void 0
    };
    val = !!(1 & buffer[0]);
    fields.active = val;
    return fields;
  }
  function encodeChannelFlowOk(channel2, fields) {
    var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(13);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(1310741, 7);
    offset = 11;
    val = fields.active;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'active'");
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeChannelClose(buffer) {
    var val, len, offset = 0, fields = {
      replyCode: void 0,
      replyText: void 0,
      classId: void 0,
      methodId: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.replyCode = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.replyText = val;
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.classId = val;
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.methodId = val;
    return fields;
  }
  function encodeChannelClose(channel2, fields) {
    var offset = 0, val = null, varyingSize = 0;
    val = fields.replyText;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'replyText' is the wrong type; must be a string (up to 255 chars)");
    var replyText_len = Buffer.byteLength(val, "utf8");
    varyingSize += replyText_len;
    var buffer = Buffer.alloc(19 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(1310760, 7);
    offset = 11;
    val = fields.replyCode;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'replyCode'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'replyCode' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.replyText;
    void 0 === val && (val = "");
    buffer[offset] = replyText_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += replyText_len;
    val = fields.classId;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'classId'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'classId' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.methodId;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'methodId'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'methodId' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeChannelCloseOk(buffer) {
    return {};
  }
  function encodeChannelCloseOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(1310761, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeAccessRequest(buffer) {
    var val, len, offset = 0, fields = {
      realm: void 0,
      exclusive: void 0,
      passive: void 0,
      active: void 0,
      write: void 0,
      read: void 0
    };
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.realm = val;
    val = !!(1 & buffer[offset]);
    fields.exclusive = val;
    val = !!(2 & buffer[offset]);
    fields.passive = val;
    val = !!(4 & buffer[offset]);
    fields.active = val;
    val = !!(8 & buffer[offset]);
    fields.write = val;
    val = !!(16 & buffer[offset]);
    fields.read = val;
    return fields;
  }
  function encodeAccessRequest(channel2, fields) {
    var offset = 0, val = null, bits = 0, varyingSize = 0;
    val = fields.realm;
    if (void 0 === val) val = "/data";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'realm' is the wrong type; must be a string (up to 255 chars)");
    var realm_len = Buffer.byteLength(val, "utf8");
    varyingSize += realm_len;
    var buffer = Buffer.alloc(14 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(1966090, 7);
    offset = 11;
    val = fields.realm;
    void 0 === val && (val = "/data");
    buffer[offset] = realm_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += realm_len;
    val = fields.exclusive;
    void 0 === val && (val = false);
    val && (bits += 1);
    val = fields.passive;
    void 0 === val && (val = true);
    val && (bits += 2);
    val = fields.active;
    void 0 === val && (val = true);
    val && (bits += 4);
    val = fields.write;
    void 0 === val && (val = true);
    val && (bits += 8);
    val = fields.read;
    void 0 === val && (val = true);
    val && (bits += 16);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeAccessRequestOk(buffer) {
    var val, offset = 0, fields = {
      ticket: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.ticket = val;
    return fields;
  }
  function encodeAccessRequestOk(channel2, fields) {
    var offset = 0, val = null, buffer = Buffer.alloc(14);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(1966091, 7);
    offset = 11;
    val = fields.ticket;
    if (void 0 === val) val = 1;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'ticket' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeExchangeDeclare(buffer) {
    var val, len, offset = 0, fields = {
      ticket: void 0,
      exchange: void 0,
      type: void 0,
      passive: void 0,
      durable: void 0,
      autoDelete: void 0,
      internal: void 0,
      nowait: void 0,
      arguments: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.ticket = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.exchange = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.type = val;
    val = !!(1 & buffer[offset]);
    fields.passive = val;
    val = !!(2 & buffer[offset]);
    fields.durable = val;
    val = !!(4 & buffer[offset]);
    fields.autoDelete = val;
    val = !!(8 & buffer[offset]);
    fields.internal = val;
    val = !!(16 & buffer[offset]);
    fields.nowait = val;
    offset++;
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = decodeFields(buffer.subarray(offset, offset + len));
    offset += len;
    fields.arguments = val;
    return fields;
  }
  function encodeExchangeDeclare(channel2, fields) {
    var len, offset = 0, val = null, bits = 0, varyingSize = 0, scratchOffset = 0;
    val = fields.exchange;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'exchange'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'exchange' is the wrong type; must be a string (up to 255 chars)");
    var exchange_len = Buffer.byteLength(val, "utf8");
    varyingSize += exchange_len;
    val = fields.type;
    if (void 0 === val) val = "direct";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'type' is the wrong type; must be a string (up to 255 chars)");
    var type_len = Buffer.byteLength(val, "utf8");
    varyingSize += type_len;
    val = fields.arguments;
    if (void 0 === val) val = {};
    else if ("object" != typeof val) throw new TypeError("Field 'arguments' is the wrong type; must be an object");
    len = encodeTable(SCRATCH, val, scratchOffset);
    var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);
    scratchOffset += len;
    varyingSize += arguments_encoded.length;
    var buffer = Buffer.alloc(17 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(2621450, 7);
    offset = 11;
    val = fields.ticket;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'ticket' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.exchange;
    void 0 === val && (val = void 0);
    buffer[offset] = exchange_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += exchange_len;
    val = fields.type;
    void 0 === val && (val = "direct");
    buffer[offset] = type_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += type_len;
    val = fields.passive;
    void 0 === val && (val = false);
    val && (bits += 1);
    val = fields.durable;
    void 0 === val && (val = false);
    val && (bits += 2);
    val = fields.autoDelete;
    void 0 === val && (val = false);
    val && (bits += 4);
    val = fields.internal;
    void 0 === val && (val = false);
    val && (bits += 8);
    val = fields.nowait;
    void 0 === val && (val = false);
    val && (bits += 16);
    buffer[offset] = bits;
    offset++;
    bits = 0;
    offset += arguments_encoded.copy(buffer, offset);
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeExchangeDeclareOk(buffer) {
    return {};
  }
  function encodeExchangeDeclareOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(2621451, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeExchangeDelete(buffer) {
    var val, len, offset = 0, fields = {
      ticket: void 0,
      exchange: void 0,
      ifUnused: void 0,
      nowait: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.ticket = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.exchange = val;
    val = !!(1 & buffer[offset]);
    fields.ifUnused = val;
    val = !!(2 & buffer[offset]);
    fields.nowait = val;
    return fields;
  }
  function encodeExchangeDelete(channel2, fields) {
    var offset = 0, val = null, bits = 0, varyingSize = 0;
    val = fields.exchange;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'exchange'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'exchange' is the wrong type; must be a string (up to 255 chars)");
    var exchange_len = Buffer.byteLength(val, "utf8");
    varyingSize += exchange_len;
    var buffer = Buffer.alloc(16 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(2621460, 7);
    offset = 11;
    val = fields.ticket;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'ticket' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.exchange;
    void 0 === val && (val = void 0);
    buffer[offset] = exchange_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += exchange_len;
    val = fields.ifUnused;
    void 0 === val && (val = false);
    val && (bits += 1);
    val = fields.nowait;
    void 0 === val && (val = false);
    val && (bits += 2);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeExchangeDeleteOk(buffer) {
    return {};
  }
  function encodeExchangeDeleteOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(2621461, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeExchangeBind(buffer) {
    var val, len, offset = 0, fields = {
      ticket: void 0,
      destination: void 0,
      source: void 0,
      routingKey: void 0,
      nowait: void 0,
      arguments: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.ticket = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.destination = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.source = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.routingKey = val;
    val = !!(1 & buffer[offset]);
    fields.nowait = val;
    offset++;
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = decodeFields(buffer.subarray(offset, offset + len));
    offset += len;
    fields.arguments = val;
    return fields;
  }
  function encodeExchangeBind(channel2, fields) {
    var len, offset = 0, val = null, bits = 0, varyingSize = 0, scratchOffset = 0;
    val = fields.destination;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'destination'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'destination' is the wrong type; must be a string (up to 255 chars)");
    var destination_len = Buffer.byteLength(val, "utf8");
    varyingSize += destination_len;
    val = fields.source;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'source'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'source' is the wrong type; must be a string (up to 255 chars)");
    var source_len = Buffer.byteLength(val, "utf8");
    varyingSize += source_len;
    val = fields.routingKey;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'routingKey' is the wrong type; must be a string (up to 255 chars)");
    var routingKey_len = Buffer.byteLength(val, "utf8");
    varyingSize += routingKey_len;
    val = fields.arguments;
    if (void 0 === val) val = {};
    else if ("object" != typeof val) throw new TypeError("Field 'arguments' is the wrong type; must be an object");
    len = encodeTable(SCRATCH, val, scratchOffset);
    var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);
    scratchOffset += len;
    varyingSize += arguments_encoded.length;
    var buffer = Buffer.alloc(18 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(2621470, 7);
    offset = 11;
    val = fields.ticket;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'ticket' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.destination;
    void 0 === val && (val = void 0);
    buffer[offset] = destination_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += destination_len;
    val = fields.source;
    void 0 === val && (val = void 0);
    buffer[offset] = source_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += source_len;
    val = fields.routingKey;
    void 0 === val && (val = "");
    buffer[offset] = routingKey_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += routingKey_len;
    val = fields.nowait;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    bits = 0;
    offset += arguments_encoded.copy(buffer, offset);
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeExchangeBindOk(buffer) {
    return {};
  }
  function encodeExchangeBindOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(2621471, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeExchangeUnbind(buffer) {
    var val, len, offset = 0, fields = {
      ticket: void 0,
      destination: void 0,
      source: void 0,
      routingKey: void 0,
      nowait: void 0,
      arguments: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.ticket = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.destination = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.source = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.routingKey = val;
    val = !!(1 & buffer[offset]);
    fields.nowait = val;
    offset++;
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = decodeFields(buffer.subarray(offset, offset + len));
    offset += len;
    fields.arguments = val;
    return fields;
  }
  function encodeExchangeUnbind(channel2, fields) {
    var len, offset = 0, val = null, bits = 0, varyingSize = 0, scratchOffset = 0;
    val = fields.destination;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'destination'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'destination' is the wrong type; must be a string (up to 255 chars)");
    var destination_len = Buffer.byteLength(val, "utf8");
    varyingSize += destination_len;
    val = fields.source;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'source'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'source' is the wrong type; must be a string (up to 255 chars)");
    var source_len = Buffer.byteLength(val, "utf8");
    varyingSize += source_len;
    val = fields.routingKey;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'routingKey' is the wrong type; must be a string (up to 255 chars)");
    var routingKey_len = Buffer.byteLength(val, "utf8");
    varyingSize += routingKey_len;
    val = fields.arguments;
    if (void 0 === val) val = {};
    else if ("object" != typeof val) throw new TypeError("Field 'arguments' is the wrong type; must be an object");
    len = encodeTable(SCRATCH, val, scratchOffset);
    var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);
    scratchOffset += len;
    varyingSize += arguments_encoded.length;
    var buffer = Buffer.alloc(18 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(2621480, 7);
    offset = 11;
    val = fields.ticket;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'ticket' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.destination;
    void 0 === val && (val = void 0);
    buffer[offset] = destination_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += destination_len;
    val = fields.source;
    void 0 === val && (val = void 0);
    buffer[offset] = source_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += source_len;
    val = fields.routingKey;
    void 0 === val && (val = "");
    buffer[offset] = routingKey_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += routingKey_len;
    val = fields.nowait;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    bits = 0;
    offset += arguments_encoded.copy(buffer, offset);
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeExchangeUnbindOk(buffer) {
    return {};
  }
  function encodeExchangeUnbindOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(2621491, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeQueueDeclare(buffer) {
    var val, len, offset = 0, fields = {
      ticket: void 0,
      queue: void 0,
      passive: void 0,
      durable: void 0,
      exclusive: void 0,
      autoDelete: void 0,
      nowait: void 0,
      arguments: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.ticket = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.queue = val;
    val = !!(1 & buffer[offset]);
    fields.passive = val;
    val = !!(2 & buffer[offset]);
    fields.durable = val;
    val = !!(4 & buffer[offset]);
    fields.exclusive = val;
    val = !!(8 & buffer[offset]);
    fields.autoDelete = val;
    val = !!(16 & buffer[offset]);
    fields.nowait = val;
    offset++;
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = decodeFields(buffer.subarray(offset, offset + len));
    offset += len;
    fields.arguments = val;
    return fields;
  }
  function encodeQueueDeclare(channel2, fields) {
    var len, offset = 0, val = null, bits = 0, varyingSize = 0, scratchOffset = 0;
    val = fields.queue;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'queue' is the wrong type; must be a string (up to 255 chars)");
    var queue_len = Buffer.byteLength(val, "utf8");
    varyingSize += queue_len;
    val = fields.arguments;
    if (void 0 === val) val = {};
    else if ("object" != typeof val) throw new TypeError("Field 'arguments' is the wrong type; must be an object");
    len = encodeTable(SCRATCH, val, scratchOffset);
    var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);
    scratchOffset += len;
    varyingSize += arguments_encoded.length;
    var buffer = Buffer.alloc(16 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3276810, 7);
    offset = 11;
    val = fields.ticket;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'ticket' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.queue;
    void 0 === val && (val = "");
    buffer[offset] = queue_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += queue_len;
    val = fields.passive;
    void 0 === val && (val = false);
    val && (bits += 1);
    val = fields.durable;
    void 0 === val && (val = false);
    val && (bits += 2);
    val = fields.exclusive;
    void 0 === val && (val = false);
    val && (bits += 4);
    val = fields.autoDelete;
    void 0 === val && (val = false);
    val && (bits += 8);
    val = fields.nowait;
    void 0 === val && (val = false);
    val && (bits += 16);
    buffer[offset] = bits;
    offset++;
    bits = 0;
    offset += arguments_encoded.copy(buffer, offset);
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeQueueDeclareOk(buffer) {
    var val, len, offset = 0, fields = {
      queue: void 0,
      messageCount: void 0,
      consumerCount: void 0
    };
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.queue = val;
    val = buffer.readUInt32BE(offset);
    offset += 4;
    fields.messageCount = val;
    val = buffer.readUInt32BE(offset);
    offset += 4;
    fields.consumerCount = val;
    return fields;
  }
  function encodeQueueDeclareOk(channel2, fields) {
    var offset = 0, val = null, varyingSize = 0;
    val = fields.queue;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'queue'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'queue' is the wrong type; must be a string (up to 255 chars)");
    var queue_len = Buffer.byteLength(val, "utf8");
    varyingSize += queue_len;
    var buffer = Buffer.alloc(21 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3276811, 7);
    offset = 11;
    val = fields.queue;
    void 0 === val && (val = void 0);
    buffer[offset] = queue_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += queue_len;
    val = fields.messageCount;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'messageCount'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'messageCount' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt32BE(val, offset);
    offset += 4;
    val = fields.consumerCount;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'consumerCount'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'consumerCount' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt32BE(val, offset);
    offset += 4;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeQueueBind(buffer) {
    var val, len, offset = 0, fields = {
      ticket: void 0,
      queue: void 0,
      exchange: void 0,
      routingKey: void 0,
      nowait: void 0,
      arguments: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.ticket = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.queue = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.exchange = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.routingKey = val;
    val = !!(1 & buffer[offset]);
    fields.nowait = val;
    offset++;
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = decodeFields(buffer.subarray(offset, offset + len));
    offset += len;
    fields.arguments = val;
    return fields;
  }
  function encodeQueueBind(channel2, fields) {
    var len, offset = 0, val = null, bits = 0, varyingSize = 0, scratchOffset = 0;
    val = fields.queue;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'queue' is the wrong type; must be a string (up to 255 chars)");
    var queue_len = Buffer.byteLength(val, "utf8");
    varyingSize += queue_len;
    val = fields.exchange;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'exchange'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'exchange' is the wrong type; must be a string (up to 255 chars)");
    var exchange_len = Buffer.byteLength(val, "utf8");
    varyingSize += exchange_len;
    val = fields.routingKey;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'routingKey' is the wrong type; must be a string (up to 255 chars)");
    var routingKey_len = Buffer.byteLength(val, "utf8");
    varyingSize += routingKey_len;
    val = fields.arguments;
    if (void 0 === val) val = {};
    else if ("object" != typeof val) throw new TypeError("Field 'arguments' is the wrong type; must be an object");
    len = encodeTable(SCRATCH, val, scratchOffset);
    var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);
    scratchOffset += len;
    varyingSize += arguments_encoded.length;
    var buffer = Buffer.alloc(18 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3276820, 7);
    offset = 11;
    val = fields.ticket;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'ticket' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.queue;
    void 0 === val && (val = "");
    buffer[offset] = queue_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += queue_len;
    val = fields.exchange;
    void 0 === val && (val = void 0);
    buffer[offset] = exchange_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += exchange_len;
    val = fields.routingKey;
    void 0 === val && (val = "");
    buffer[offset] = routingKey_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += routingKey_len;
    val = fields.nowait;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    bits = 0;
    offset += arguments_encoded.copy(buffer, offset);
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeQueueBindOk(buffer) {
    return {};
  }
  function encodeQueueBindOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3276821, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeQueuePurge(buffer) {
    var val, len, offset = 0, fields = {
      ticket: void 0,
      queue: void 0,
      nowait: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.ticket = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.queue = val;
    val = !!(1 & buffer[offset]);
    fields.nowait = val;
    return fields;
  }
  function encodeQueuePurge(channel2, fields) {
    var offset = 0, val = null, bits = 0, varyingSize = 0;
    val = fields.queue;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'queue' is the wrong type; must be a string (up to 255 chars)");
    var queue_len = Buffer.byteLength(val, "utf8");
    varyingSize += queue_len;
    var buffer = Buffer.alloc(16 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3276830, 7);
    offset = 11;
    val = fields.ticket;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'ticket' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.queue;
    void 0 === val && (val = "");
    buffer[offset] = queue_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += queue_len;
    val = fields.nowait;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeQueuePurgeOk(buffer) {
    var val, offset = 0, fields = {
      messageCount: void 0
    };
    val = buffer.readUInt32BE(offset);
    offset += 4;
    fields.messageCount = val;
    return fields;
  }
  function encodeQueuePurgeOk(channel2, fields) {
    var offset = 0, val = null, buffer = Buffer.alloc(16);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3276831, 7);
    offset = 11;
    val = fields.messageCount;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'messageCount'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'messageCount' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt32BE(val, offset);
    offset += 4;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeQueueDelete(buffer) {
    var val, len, offset = 0, fields = {
      ticket: void 0,
      queue: void 0,
      ifUnused: void 0,
      ifEmpty: void 0,
      nowait: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.ticket = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.queue = val;
    val = !!(1 & buffer[offset]);
    fields.ifUnused = val;
    val = !!(2 & buffer[offset]);
    fields.ifEmpty = val;
    val = !!(4 & buffer[offset]);
    fields.nowait = val;
    return fields;
  }
  function encodeQueueDelete(channel2, fields) {
    var offset = 0, val = null, bits = 0, varyingSize = 0;
    val = fields.queue;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'queue' is the wrong type; must be a string (up to 255 chars)");
    var queue_len = Buffer.byteLength(val, "utf8");
    varyingSize += queue_len;
    var buffer = Buffer.alloc(16 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3276840, 7);
    offset = 11;
    val = fields.ticket;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'ticket' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.queue;
    void 0 === val && (val = "");
    buffer[offset] = queue_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += queue_len;
    val = fields.ifUnused;
    void 0 === val && (val = false);
    val && (bits += 1);
    val = fields.ifEmpty;
    void 0 === val && (val = false);
    val && (bits += 2);
    val = fields.nowait;
    void 0 === val && (val = false);
    val && (bits += 4);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeQueueDeleteOk(buffer) {
    var val, offset = 0, fields = {
      messageCount: void 0
    };
    val = buffer.readUInt32BE(offset);
    offset += 4;
    fields.messageCount = val;
    return fields;
  }
  function encodeQueueDeleteOk(channel2, fields) {
    var offset = 0, val = null, buffer = Buffer.alloc(16);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3276841, 7);
    offset = 11;
    val = fields.messageCount;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'messageCount'");
    if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'messageCount' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt32BE(val, offset);
    offset += 4;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeQueueUnbind(buffer) {
    var val, len, offset = 0, fields = {
      ticket: void 0,
      queue: void 0,
      exchange: void 0,
      routingKey: void 0,
      arguments: void 0
    };
    val = buffer.readUInt16BE(offset);
    offset += 2;
    fields.ticket = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.queue = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.exchange = val;
    len = buffer.readUInt8(offset);
    offset++;
    val = buffer.toString("utf8", offset, offset + len);
    offset += len;
    fields.routingKey = val;
    len = buffer.readUInt32BE(offset);
    offset += 4;
    val = decodeFields(buffer.subarray(offset, offset + len));
    offset += len;
    fields.arguments = val;
    return fields;
  }
  function encodeQueueUnbind(channel2, fields) {
    var len, offset = 0, val = null, varyingSize = 0, scratchOffset = 0;
    val = fields.queue;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'queue' is the wrong type; must be a string (up to 255 chars)");
    var queue_len = Buffer.byteLength(val, "utf8");
    varyingSize += queue_len;
    val = fields.exchange;
    if (void 0 === val) throw new Error("Missing value for mandatory field 'exchange'");
    if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'exchange' is the wrong type; must be a string (up to 255 chars)");
    var exchange_len = Buffer.byteLength(val, "utf8");
    varyingSize += exchange_len;
    val = fields.routingKey;
    if (void 0 === val) val = "";
    else if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'routingKey' is the wrong type; must be a string (up to 255 chars)");
    var routingKey_len = Buffer.byteLength(val, "utf8");
    varyingSize += routingKey_len;
    val = fields.arguments;
    if (void 0 === val) val = {};
    else if ("object" != typeof val) throw new TypeError("Field 'arguments' is the wrong type; must be an object");
    len = encodeTable(SCRATCH, val, scratchOffset);
    var arguments_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);
    scratchOffset += len;
    varyingSize += arguments_encoded.length;
    var buffer = Buffer.alloc(17 + varyingSize);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3276850, 7);
    offset = 11;
    val = fields.ticket;
    if (void 0 === val) val = 0;
    else if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'ticket' is the wrong type; must be a number (but not NaN)");
    buffer.writeUInt16BE(val, offset);
    offset += 2;
    val = fields.queue;
    void 0 === val && (val = "");
    buffer[offset] = queue_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += queue_len;
    val = fields.exchange;
    void 0 === val && (val = void 0);
    buffer[offset] = exchange_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += exchange_len;
    val = fields.routingKey;
    void 0 === val && (val = "");
    buffer[offset] = routingKey_len;
    offset++;
    buffer.write(val, offset, "utf8");
    offset += routingKey_len;
    offset += arguments_encoded.copy(buffer, offset);
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeQueueUnbindOk(buffer) {
    return {};
  }
  function encodeQueueUnbindOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3276851, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeTxSelect(buffer) {
    return {};
  }
  function encodeTxSelect(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(5898250, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeTxSelectOk(buffer) {
    return {};
  }
  function encodeTxSelectOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(5898251, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeTxCommit(buffer) {
    return {};
  }
  function encodeTxCommit(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(5898260, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeTxCommitOk(buffer) {
    return {};
  }
  function encodeTxCommitOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(5898261, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeTxRollback(buffer) {
    return {};
  }
  function encodeTxRollback(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(5898270, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeTxRollbackOk(buffer) {
    return {};
  }
  function encodeTxRollbackOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(5898271, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConfirmSelect(buffer) {
    var val, fields = {
      nowait: void 0
    };
    val = !!(1 & buffer[0]);
    fields.nowait = val;
    return fields;
  }
  function encodeConfirmSelect(channel2, fields) {
    var offset = 0, val = null, bits = 0, buffer = Buffer.alloc(13);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(5570570, 7);
    offset = 11;
    val = fields.nowait;
    void 0 === val && (val = false);
    val && (bits += 1);
    buffer[offset] = bits;
    offset++;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function decodeConfirmSelectOk(buffer) {
    return {};
  }
  function encodeConfirmSelectOk(channel2, fields) {
    var offset = 0, buffer = Buffer.alloc(12);
    buffer[0] = 1;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(5570571, 7);
    offset = 11;
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    return buffer;
  }
  function encodeBasicProperties(channel2, size, fields) {
    var val, len, offset = 0, flags = 0, scratchOffset = 0, varyingSize = 0;
    val = fields.contentType;
    if (void 0 != val) {
      if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'contentType' is the wrong type; must be a string (up to 255 chars)");
      var contentType_len = Buffer.byteLength(val, "utf8");
      varyingSize += 1;
      varyingSize += contentType_len;
    }
    val = fields.contentEncoding;
    if (void 0 != val) {
      if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'contentEncoding' is the wrong type; must be a string (up to 255 chars)");
      var contentEncoding_len = Buffer.byteLength(val, "utf8");
      varyingSize += 1;
      varyingSize += contentEncoding_len;
    }
    val = fields.headers;
    if (void 0 != val) {
      if ("object" != typeof val) throw new TypeError("Field 'headers' is the wrong type; must be an object");
      len = encodeTable(SCRATCH, val, scratchOffset);
      var headers_encoded = SCRATCH.slice(scratchOffset, scratchOffset + len);
      scratchOffset += len;
      varyingSize += headers_encoded.length;
    }
    val = fields.deliveryMode;
    if (void 0 != val) {
      if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'deliveryMode' is the wrong type; must be a number (but not NaN)");
      varyingSize += 1;
    }
    val = fields.priority;
    if (void 0 != val) {
      if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'priority' is the wrong type; must be a number (but not NaN)");
      varyingSize += 1;
    }
    val = fields.correlationId;
    if (void 0 != val) {
      if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'correlationId' is the wrong type; must be a string (up to 255 chars)");
      var correlationId_len = Buffer.byteLength(val, "utf8");
      varyingSize += 1;
      varyingSize += correlationId_len;
    }
    val = fields.replyTo;
    if (void 0 != val) {
      if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'replyTo' is the wrong type; must be a string (up to 255 chars)");
      var replyTo_len = Buffer.byteLength(val, "utf8");
      varyingSize += 1;
      varyingSize += replyTo_len;
    }
    val = fields.expiration;
    if (void 0 != val) {
      if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'expiration' is the wrong type; must be a string (up to 255 chars)");
      var expiration_len = Buffer.byteLength(val, "utf8");
      varyingSize += 1;
      varyingSize += expiration_len;
    }
    val = fields.messageId;
    if (void 0 != val) {
      if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'messageId' is the wrong type; must be a string (up to 255 chars)");
      var messageId_len = Buffer.byteLength(val, "utf8");
      varyingSize += 1;
      varyingSize += messageId_len;
    }
    val = fields.timestamp;
    if (void 0 != val) {
      if ("number" != typeof val || isNaN(val)) throw new TypeError("Field 'timestamp' is the wrong type; must be a number (but not NaN)");
      varyingSize += 8;
    }
    val = fields.type;
    if (void 0 != val) {
      if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'type' is the wrong type; must be a string (up to 255 chars)");
      var type_len = Buffer.byteLength(val, "utf8");
      varyingSize += 1;
      varyingSize += type_len;
    }
    val = fields.userId;
    if (void 0 != val) {
      if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'userId' is the wrong type; must be a string (up to 255 chars)");
      var userId_len = Buffer.byteLength(val, "utf8");
      varyingSize += 1;
      varyingSize += userId_len;
    }
    val = fields.appId;
    if (void 0 != val) {
      if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'appId' is the wrong type; must be a string (up to 255 chars)");
      var appId_len = Buffer.byteLength(val, "utf8");
      varyingSize += 1;
      varyingSize += appId_len;
    }
    val = fields.clusterId;
    if (void 0 != val) {
      if (!("string" == typeof val && Buffer.byteLength(val) < 256)) throw new TypeError("Field 'clusterId' is the wrong type; must be a string (up to 255 chars)");
      var clusterId_len = Buffer.byteLength(val, "utf8");
      varyingSize += 1;
      varyingSize += clusterId_len;
    }
    var buffer = Buffer.alloc(22 + varyingSize);
    buffer[0] = 2;
    buffer.writeUInt16BE(channel2, 1);
    buffer.writeUInt32BE(3932160, 7);
    ints.writeUInt64BE(buffer, size, 11);
    flags = 0;
    offset = 21;
    val = fields.contentType;
    if (void 0 != val) {
      flags += 32768;
      buffer[offset] = contentType_len;
      offset++;
      buffer.write(val, offset, "utf8");
      offset += contentType_len;
    }
    val = fields.contentEncoding;
    if (void 0 != val) {
      flags += 16384;
      buffer[offset] = contentEncoding_len;
      offset++;
      buffer.write(val, offset, "utf8");
      offset += contentEncoding_len;
    }
    val = fields.headers;
    if (void 0 != val) {
      flags += 8192;
      offset += headers_encoded.copy(buffer, offset);
    }
    val = fields.deliveryMode;
    if (void 0 != val) {
      flags += 4096;
      buffer.writeUInt8(val, offset);
      offset++;
    }
    val = fields.priority;
    if (void 0 != val) {
      flags += 2048;
      buffer.writeUInt8(val, offset);
      offset++;
    }
    val = fields.correlationId;
    if (void 0 != val) {
      flags += 1024;
      buffer[offset] = correlationId_len;
      offset++;
      buffer.write(val, offset, "utf8");
      offset += correlationId_len;
    }
    val = fields.replyTo;
    if (void 0 != val) {
      flags += 512;
      buffer[offset] = replyTo_len;
      offset++;
      buffer.write(val, offset, "utf8");
      offset += replyTo_len;
    }
    val = fields.expiration;
    if (void 0 != val) {
      flags += 256;
      buffer[offset] = expiration_len;
      offset++;
      buffer.write(val, offset, "utf8");
      offset += expiration_len;
    }
    val = fields.messageId;
    if (void 0 != val) {
      flags += 128;
      buffer[offset] = messageId_len;
      offset++;
      buffer.write(val, offset, "utf8");
      offset += messageId_len;
    }
    val = fields.timestamp;
    if (void 0 != val) {
      flags += 64;
      ints.writeUInt64BE(buffer, val, offset);
      offset += 8;
    }
    val = fields.type;
    if (void 0 != val) {
      flags += 32;
      buffer[offset] = type_len;
      offset++;
      buffer.write(val, offset, "utf8");
      offset += type_len;
    }
    val = fields.userId;
    if (void 0 != val) {
      flags += 16;
      buffer[offset] = userId_len;
      offset++;
      buffer.write(val, offset, "utf8");
      offset += userId_len;
    }
    val = fields.appId;
    if (void 0 != val) {
      flags += 8;
      buffer[offset] = appId_len;
      offset++;
      buffer.write(val, offset, "utf8");
      offset += appId_len;
    }
    val = fields.clusterId;
    if (void 0 != val) {
      flags += 4;
      buffer[offset] = clusterId_len;
      offset++;
      buffer.write(val, offset, "utf8");
      offset += clusterId_len;
    }
    buffer[offset] = 206;
    buffer.writeUInt32BE(offset - 7, 3);
    buffer.writeUInt16BE(flags, 19);
    return buffer.subarray(0, offset + 1);
  }
  function decodeBasicProperties(buffer) {
    var flags, val, len, offset = 2;
    flags = buffer.readUInt16BE(0);
    if (0 === flags) return {};
    var fields = {
      contentType: void 0,
      contentEncoding: void 0,
      headers: void 0,
      deliveryMode: void 0,
      priority: void 0,
      correlationId: void 0,
      replyTo: void 0,
      expiration: void 0,
      messageId: void 0,
      timestamp: void 0,
      type: void 0,
      userId: void 0,
      appId: void 0,
      clusterId: void 0
    };
    if (32768 & flags) {
      len = buffer.readUInt8(offset);
      offset++;
      val = buffer.toString("utf8", offset, offset + len);
      offset += len;
      fields.contentType = val;
    }
    if (16384 & flags) {
      len = buffer.readUInt8(offset);
      offset++;
      val = buffer.toString("utf8", offset, offset + len);
      offset += len;
      fields.contentEncoding = val;
    }
    if (8192 & flags) {
      len = buffer.readUInt32BE(offset);
      offset += 4;
      val = decodeFields(buffer.subarray(offset, offset + len));
      offset += len;
      fields.headers = val;
    }
    if (4096 & flags) {
      val = buffer[offset];
      offset++;
      fields.deliveryMode = val;
    }
    if (2048 & flags) {
      val = buffer[offset];
      offset++;
      fields.priority = val;
    }
    if (1024 & flags) {
      len = buffer.readUInt8(offset);
      offset++;
      val = buffer.toString("utf8", offset, offset + len);
      offset += len;
      fields.correlationId = val;
    }
    if (512 & flags) {
      len = buffer.readUInt8(offset);
      offset++;
      val = buffer.toString("utf8", offset, offset + len);
      offset += len;
      fields.replyTo = val;
    }
    if (256 & flags) {
      len = buffer.readUInt8(offset);
      offset++;
      val = buffer.toString("utf8", offset, offset + len);
      offset += len;
      fields.expiration = val;
    }
    if (128 & flags) {
      len = buffer.readUInt8(offset);
      offset++;
      val = buffer.toString("utf8", offset, offset + len);
      offset += len;
      fields.messageId = val;
    }
    if (64 & flags) {
      val = ints.readUInt64BE(buffer, offset);
      offset += 8;
      fields.timestamp = val;
    }
    if (32 & flags) {
      len = buffer.readUInt8(offset);
      offset++;
      val = buffer.toString("utf8", offset, offset + len);
      offset += len;
      fields.type = val;
    }
    if (16 & flags) {
      len = buffer.readUInt8(offset);
      offset++;
      val = buffer.toString("utf8", offset, offset + len);
      offset += len;
      fields.userId = val;
    }
    if (8 & flags) {
      len = buffer.readUInt8(offset);
      offset++;
      val = buffer.toString("utf8", offset, offset + len);
      offset += len;
      fields.appId = val;
    }
    if (4 & flags) {
      len = buffer.readUInt8(offset);
      offset++;
      val = buffer.toString("utf8", offset, offset + len);
      offset += len;
      fields.clusterId = val;
    }
    return fields;
  }
  var codec2 = requireCodec(), ints = requireBufferMoreInts(), encodeTable = codec2.encodeTable, decodeFields = codec2.decodeFields, SCRATCH = Buffer.alloc(65536);
  defs.constants = {
    FRAME_METHOD: 1,
    FRAME_HEADER: 2,
    FRAME_BODY: 3,
    FRAME_HEARTBEAT: 8,
    FRAME_MIN_SIZE: 4096,
    FRAME_END: 206,
    REPLY_SUCCESS: 200,
    CONTENT_TOO_LARGE: 311,
    NO_ROUTE: 312,
    NO_CONSUMERS: 313,
    ACCESS_REFUSED: 403,
    NOT_FOUND: 404,
    RESOURCE_LOCKED: 405,
    PRECONDITION_FAILED: 406,
    CONNECTION_FORCED: 320,
    INVALID_PATH: 402,
    FRAME_ERROR: 501,
    SYNTAX_ERROR: 502,
    COMMAND_INVALID: 503,
    CHANNEL_ERROR: 504,
    UNEXPECTED_FRAME: 505,
    RESOURCE_ERROR: 506,
    NOT_ALLOWED: 530,
    NOT_IMPLEMENTED: 540,
    INTERNAL_ERROR: 541
  };
  defs.constant_strs = {
    "1": "FRAME-METHOD",
    "2": "FRAME-HEADER",
    "3": "FRAME-BODY",
    "8": "FRAME-HEARTBEAT",
    "200": "REPLY-SUCCESS",
    "206": "FRAME-END",
    "311": "CONTENT-TOO-LARGE",
    "312": "NO-ROUTE",
    "313": "NO-CONSUMERS",
    "320": "CONNECTION-FORCED",
    "402": "INVALID-PATH",
    "403": "ACCESS-REFUSED",
    "404": "NOT-FOUND",
    "405": "RESOURCE-LOCKED",
    "406": "PRECONDITION-FAILED",
    "501": "FRAME-ERROR",
    "502": "SYNTAX-ERROR",
    "503": "COMMAND-INVALID",
    "504": "CHANNEL-ERROR",
    "505": "UNEXPECTED-FRAME",
    "506": "RESOURCE-ERROR",
    "530": "NOT-ALLOWED",
    "540": "NOT-IMPLEMENTED",
    "541": "INTERNAL-ERROR",
    "4096": "FRAME-MIN-SIZE"
  };
  defs.FRAME_OVERHEAD = 8;
  defs.decode = function(id, buf) {
    switch (id) {
      case 3932170:
        return decodeBasicQos(buf);
      case 3932171:
        return decodeBasicQosOk();
      case 3932180:
        return decodeBasicConsume(buf);
      case 3932181:
        return decodeBasicConsumeOk(buf);
      case 3932190:
        return decodeBasicCancel(buf);
      case 3932191:
        return decodeBasicCancelOk(buf);
      case 3932200:
        return decodeBasicPublish(buf);
      case 3932210:
        return decodeBasicReturn(buf);
      case 3932220:
        return decodeBasicDeliver(buf);
      case 3932230:
        return decodeBasicGet(buf);
      case 3932231:
        return decodeBasicGetOk(buf);
      case 3932232:
        return decodeBasicGetEmpty(buf);
      case 3932240:
        return decodeBasicAck(buf);
      case 3932250:
        return decodeBasicReject(buf);
      case 3932260:
        return decodeBasicRecoverAsync(buf);
      case 3932270:
        return decodeBasicRecover(buf);
      case 3932271:
        return decodeBasicRecoverOk();
      case 3932280:
        return decodeBasicNack(buf);
      case 655370:
        return decodeConnectionStart(buf);
      case 655371:
        return decodeConnectionStartOk(buf);
      case 655380:
        return decodeConnectionSecure(buf);
      case 655381:
        return decodeConnectionSecureOk(buf);
      case 655390:
        return decodeConnectionTune(buf);
      case 655391:
        return decodeConnectionTuneOk(buf);
      case 655400:
        return decodeConnectionOpen(buf);
      case 655401:
        return decodeConnectionOpenOk(buf);
      case 655410:
        return decodeConnectionClose(buf);
      case 655411:
        return decodeConnectionCloseOk();
      case 655420:
        return decodeConnectionBlocked(buf);
      case 655421:
        return decodeConnectionUnblocked();
      case 655430:
        return decodeConnectionUpdateSecret(buf);
      case 655431:
        return decodeConnectionUpdateSecretOk();
      case 1310730:
        return decodeChannelOpen(buf);
      case 1310731:
        return decodeChannelOpenOk(buf);
      case 1310740:
        return decodeChannelFlow(buf);
      case 1310741:
        return decodeChannelFlowOk(buf);
      case 1310760:
        return decodeChannelClose(buf);
      case 1310761:
        return decodeChannelCloseOk();
      case 1966090:
        return decodeAccessRequest(buf);
      case 1966091:
        return decodeAccessRequestOk(buf);
      case 2621450:
        return decodeExchangeDeclare(buf);
      case 2621451:
        return decodeExchangeDeclareOk();
      case 2621460:
        return decodeExchangeDelete(buf);
      case 2621461:
        return decodeExchangeDeleteOk();
      case 2621470:
        return decodeExchangeBind(buf);
      case 2621471:
        return decodeExchangeBindOk();
      case 2621480:
        return decodeExchangeUnbind(buf);
      case 2621491:
        return decodeExchangeUnbindOk();
      case 3276810:
        return decodeQueueDeclare(buf);
      case 3276811:
        return decodeQueueDeclareOk(buf);
      case 3276820:
        return decodeQueueBind(buf);
      case 3276821:
        return decodeQueueBindOk();
      case 3276830:
        return decodeQueuePurge(buf);
      case 3276831:
        return decodeQueuePurgeOk(buf);
      case 3276840:
        return decodeQueueDelete(buf);
      case 3276841:
        return decodeQueueDeleteOk(buf);
      case 3276850:
        return decodeQueueUnbind(buf);
      case 3276851:
        return decodeQueueUnbindOk();
      case 5898250:
        return decodeTxSelect();
      case 5898251:
        return decodeTxSelectOk();
      case 5898260:
        return decodeTxCommit();
      case 5898261:
        return decodeTxCommitOk();
      case 5898270:
        return decodeTxRollback();
      case 5898271:
        return decodeTxRollbackOk();
      case 5570570:
        return decodeConfirmSelect(buf);
      case 5570571:
        return decodeConfirmSelectOk();
      case 60:
        return decodeBasicProperties(buf);
      default:
        throw new Error("Unknown class/method ID");
    }
  };
  defs.encodeMethod = function(id, channel2, fields) {
    switch (id) {
      case 3932170:
        return encodeBasicQos(channel2, fields);
      case 3932171:
        return encodeBasicQosOk(channel2);
      case 3932180:
        return encodeBasicConsume(channel2, fields);
      case 3932181:
        return encodeBasicConsumeOk(channel2, fields);
      case 3932190:
        return encodeBasicCancel(channel2, fields);
      case 3932191:
        return encodeBasicCancelOk(channel2, fields);
      case 3932200:
        return encodeBasicPublish(channel2, fields);
      case 3932210:
        return encodeBasicReturn(channel2, fields);
      case 3932220:
        return encodeBasicDeliver(channel2, fields);
      case 3932230:
        return encodeBasicGet(channel2, fields);
      case 3932231:
        return encodeBasicGetOk(channel2, fields);
      case 3932232:
        return encodeBasicGetEmpty(channel2, fields);
      case 3932240:
        return encodeBasicAck(channel2, fields);
      case 3932250:
        return encodeBasicReject(channel2, fields);
      case 3932260:
        return encodeBasicRecoverAsync(channel2, fields);
      case 3932270:
        return encodeBasicRecover(channel2, fields);
      case 3932271:
        return encodeBasicRecoverOk(channel2);
      case 3932280:
        return encodeBasicNack(channel2, fields);
      case 655370:
        return encodeConnectionStart(channel2, fields);
      case 655371:
        return encodeConnectionStartOk(channel2, fields);
      case 655380:
        return encodeConnectionSecure(channel2, fields);
      case 655381:
        return encodeConnectionSecureOk(channel2, fields);
      case 655390:
        return encodeConnectionTune(channel2, fields);
      case 655391:
        return encodeConnectionTuneOk(channel2, fields);
      case 655400:
        return encodeConnectionOpen(channel2, fields);
      case 655401:
        return encodeConnectionOpenOk(channel2, fields);
      case 655410:
        return encodeConnectionClose(channel2, fields);
      case 655411:
        return encodeConnectionCloseOk(channel2);
      case 655420:
        return encodeConnectionBlocked(channel2, fields);
      case 655421:
        return encodeConnectionUnblocked(channel2);
      case 655430:
        return encodeConnectionUpdateSecret(channel2, fields);
      case 655431:
        return encodeConnectionUpdateSecretOk(channel2);
      case 1310730:
        return encodeChannelOpen(channel2, fields);
      case 1310731:
        return encodeChannelOpenOk(channel2, fields);
      case 1310740:
        return encodeChannelFlow(channel2, fields);
      case 1310741:
        return encodeChannelFlowOk(channel2, fields);
      case 1310760:
        return encodeChannelClose(channel2, fields);
      case 1310761:
        return encodeChannelCloseOk(channel2);
      case 1966090:
        return encodeAccessRequest(channel2, fields);
      case 1966091:
        return encodeAccessRequestOk(channel2, fields);
      case 2621450:
        return encodeExchangeDeclare(channel2, fields);
      case 2621451:
        return encodeExchangeDeclareOk(channel2);
      case 2621460:
        return encodeExchangeDelete(channel2, fields);
      case 2621461:
        return encodeExchangeDeleteOk(channel2);
      case 2621470:
        return encodeExchangeBind(channel2, fields);
      case 2621471:
        return encodeExchangeBindOk(channel2);
      case 2621480:
        return encodeExchangeUnbind(channel2, fields);
      case 2621491:
        return encodeExchangeUnbindOk(channel2);
      case 3276810:
        return encodeQueueDeclare(channel2, fields);
      case 3276811:
        return encodeQueueDeclareOk(channel2, fields);
      case 3276820:
        return encodeQueueBind(channel2, fields);
      case 3276821:
        return encodeQueueBindOk(channel2);
      case 3276830:
        return encodeQueuePurge(channel2, fields);
      case 3276831:
        return encodeQueuePurgeOk(channel2, fields);
      case 3276840:
        return encodeQueueDelete(channel2, fields);
      case 3276841:
        return encodeQueueDeleteOk(channel2, fields);
      case 3276850:
        return encodeQueueUnbind(channel2, fields);
      case 3276851:
        return encodeQueueUnbindOk(channel2);
      case 5898250:
        return encodeTxSelect(channel2);
      case 5898251:
        return encodeTxSelectOk(channel2);
      case 5898260:
        return encodeTxCommit(channel2);
      case 5898261:
        return encodeTxCommitOk(channel2);
      case 5898270:
        return encodeTxRollback(channel2);
      case 5898271:
        return encodeTxRollbackOk(channel2);
      case 5570570:
        return encodeConfirmSelect(channel2, fields);
      case 5570571:
        return encodeConfirmSelectOk(channel2);
      default:
        throw new Error("Unknown class/method ID");
    }
  };
  defs.encodeProperties = function(id, channel2, size, fields) {
    switch (id) {
      case 60:
        return encodeBasicProperties(channel2, size, fields);
      default:
        throw new Error("Unknown class/properties ID");
    }
  };
  defs.info = function(id) {
    switch (id) {
      case 3932170:
        return methodInfoBasicQos;
      case 3932171:
        return methodInfoBasicQosOk;
      case 3932180:
        return methodInfoBasicConsume;
      case 3932181:
        return methodInfoBasicConsumeOk;
      case 3932190:
        return methodInfoBasicCancel;
      case 3932191:
        return methodInfoBasicCancelOk;
      case 3932200:
        return methodInfoBasicPublish;
      case 3932210:
        return methodInfoBasicReturn;
      case 3932220:
        return methodInfoBasicDeliver;
      case 3932230:
        return methodInfoBasicGet;
      case 3932231:
        return methodInfoBasicGetOk;
      case 3932232:
        return methodInfoBasicGetEmpty;
      case 3932240:
        return methodInfoBasicAck;
      case 3932250:
        return methodInfoBasicReject;
      case 3932260:
        return methodInfoBasicRecoverAsync;
      case 3932270:
        return methodInfoBasicRecover;
      case 3932271:
        return methodInfoBasicRecoverOk;
      case 3932280:
        return methodInfoBasicNack;
      case 655370:
        return methodInfoConnectionStart;
      case 655371:
        return methodInfoConnectionStartOk;
      case 655380:
        return methodInfoConnectionSecure;
      case 655381:
        return methodInfoConnectionSecureOk;
      case 655390:
        return methodInfoConnectionTune;
      case 655391:
        return methodInfoConnectionTuneOk;
      case 655400:
        return methodInfoConnectionOpen;
      case 655401:
        return methodInfoConnectionOpenOk;
      case 655410:
        return methodInfoConnectionClose;
      case 655411:
        return methodInfoConnectionCloseOk;
      case 655420:
        return methodInfoConnectionBlocked;
      case 655421:
        return methodInfoConnectionUnblocked;
      case 655430:
        return methodInfoConnectionUpdateSecret;
      case 655431:
        return methodInfoConnectionUpdateSecretOk;
      case 1310730:
        return methodInfoChannelOpen;
      case 1310731:
        return methodInfoChannelOpenOk;
      case 1310740:
        return methodInfoChannelFlow;
      case 1310741:
        return methodInfoChannelFlowOk;
      case 1310760:
        return methodInfoChannelClose;
      case 1310761:
        return methodInfoChannelCloseOk;
      case 1966090:
        return methodInfoAccessRequest;
      case 1966091:
        return methodInfoAccessRequestOk;
      case 2621450:
        return methodInfoExchangeDeclare;
      case 2621451:
        return methodInfoExchangeDeclareOk;
      case 2621460:
        return methodInfoExchangeDelete;
      case 2621461:
        return methodInfoExchangeDeleteOk;
      case 2621470:
        return methodInfoExchangeBind;
      case 2621471:
        return methodInfoExchangeBindOk;
      case 2621480:
        return methodInfoExchangeUnbind;
      case 2621491:
        return methodInfoExchangeUnbindOk;
      case 3276810:
        return methodInfoQueueDeclare;
      case 3276811:
        return methodInfoQueueDeclareOk;
      case 3276820:
        return methodInfoQueueBind;
      case 3276821:
        return methodInfoQueueBindOk;
      case 3276830:
        return methodInfoQueuePurge;
      case 3276831:
        return methodInfoQueuePurgeOk;
      case 3276840:
        return methodInfoQueueDelete;
      case 3276841:
        return methodInfoQueueDeleteOk;
      case 3276850:
        return methodInfoQueueUnbind;
      case 3276851:
        return methodInfoQueueUnbindOk;
      case 5898250:
        return methodInfoTxSelect;
      case 5898251:
        return methodInfoTxSelectOk;
      case 5898260:
        return methodInfoTxCommit;
      case 5898261:
        return methodInfoTxCommitOk;
      case 5898270:
        return methodInfoTxRollback;
      case 5898271:
        return methodInfoTxRollbackOk;
      case 5570570:
        return methodInfoConfirmSelect;
      case 5570571:
        return methodInfoConfirmSelectOk;
      case 60:
        return propertiesInfoBasicProperties;
      default:
        throw new Error("Unknown class/method ID");
    }
  };
  defs.BasicQos = 3932170;
  var methodInfoBasicQos = defs.methodInfoBasicQos = {
    id: 3932170,
    classId: 60,
    methodId: 10,
    name: "BasicQos",
    args: [{
      type: "long",
      name: "prefetchSize",
      default: 0
    }, {
      type: "short",
      name: "prefetchCount",
      default: 0
    }, {
      type: "bit",
      name: "global",
      default: false
    }]
  };
  defs.BasicQosOk = 3932171;
  var methodInfoBasicQosOk = defs.methodInfoBasicQosOk = {
    id: 3932171,
    classId: 60,
    methodId: 11,
    name: "BasicQosOk",
    args: []
  };
  defs.BasicConsume = 3932180;
  var methodInfoBasicConsume = defs.methodInfoBasicConsume = {
    id: 3932180,
    classId: 60,
    methodId: 20,
    name: "BasicConsume",
    args: [{
      type: "short",
      name: "ticket",
      default: 0
    }, {
      type: "shortstr",
      name: "queue",
      default: ""
    }, {
      type: "shortstr",
      name: "consumerTag",
      default: ""
    }, {
      type: "bit",
      name: "noLocal",
      default: false
    }, {
      type: "bit",
      name: "noAck",
      default: false
    }, {
      type: "bit",
      name: "exclusive",
      default: false
    }, {
      type: "bit",
      name: "nowait",
      default: false
    }, {
      type: "table",
      name: "arguments",
      default: {}
    }]
  };
  defs.BasicConsumeOk = 3932181;
  var methodInfoBasicConsumeOk = defs.methodInfoBasicConsumeOk = {
    id: 3932181,
    classId: 60,
    methodId: 21,
    name: "BasicConsumeOk",
    args: [{
      type: "shortstr",
      name: "consumerTag"
    }]
  };
  defs.BasicCancel = 3932190;
  var methodInfoBasicCancel = defs.methodInfoBasicCancel = {
    id: 3932190,
    classId: 60,
    methodId: 30,
    name: "BasicCancel",
    args: [{
      type: "shortstr",
      name: "consumerTag"
    }, {
      type: "bit",
      name: "nowait",
      default: false
    }]
  };
  defs.BasicCancelOk = 3932191;
  var methodInfoBasicCancelOk = defs.methodInfoBasicCancelOk = {
    id: 3932191,
    classId: 60,
    methodId: 31,
    name: "BasicCancelOk",
    args: [{
      type: "shortstr",
      name: "consumerTag"
    }]
  };
  defs.BasicPublish = 3932200;
  var methodInfoBasicPublish = defs.methodInfoBasicPublish = {
    id: 3932200,
    classId: 60,
    methodId: 40,
    name: "BasicPublish",
    args: [{
      type: "short",
      name: "ticket",
      default: 0
    }, {
      type: "shortstr",
      name: "exchange",
      default: ""
    }, {
      type: "shortstr",
      name: "routingKey",
      default: ""
    }, {
      type: "bit",
      name: "mandatory",
      default: false
    }, {
      type: "bit",
      name: "immediate",
      default: false
    }]
  };
  defs.BasicReturn = 3932210;
  var methodInfoBasicReturn = defs.methodInfoBasicReturn = {
    id: 3932210,
    classId: 60,
    methodId: 50,
    name: "BasicReturn",
    args: [{
      type: "short",
      name: "replyCode"
    }, {
      type: "shortstr",
      name: "replyText",
      default: ""
    }, {
      type: "shortstr",
      name: "exchange"
    }, {
      type: "shortstr",
      name: "routingKey"
    }]
  };
  defs.BasicDeliver = 3932220;
  var methodInfoBasicDeliver = defs.methodInfoBasicDeliver = {
    id: 3932220,
    classId: 60,
    methodId: 60,
    name: "BasicDeliver",
    args: [{
      type: "shortstr",
      name: "consumerTag"
    }, {
      type: "longlong",
      name: "deliveryTag"
    }, {
      type: "bit",
      name: "redelivered",
      default: false
    }, {
      type: "shortstr",
      name: "exchange"
    }, {
      type: "shortstr",
      name: "routingKey"
    }]
  };
  defs.BasicGet = 3932230;
  var methodInfoBasicGet = defs.methodInfoBasicGet = {
    id: 3932230,
    classId: 60,
    methodId: 70,
    name: "BasicGet",
    args: [{
      type: "short",
      name: "ticket",
      default: 0
    }, {
      type: "shortstr",
      name: "queue",
      default: ""
    }, {
      type: "bit",
      name: "noAck",
      default: false
    }]
  };
  defs.BasicGetOk = 3932231;
  var methodInfoBasicGetOk = defs.methodInfoBasicGetOk = {
    id: 3932231,
    classId: 60,
    methodId: 71,
    name: "BasicGetOk",
    args: [{
      type: "longlong",
      name: "deliveryTag"
    }, {
      type: "bit",
      name: "redelivered",
      default: false
    }, {
      type: "shortstr",
      name: "exchange"
    }, {
      type: "shortstr",
      name: "routingKey"
    }, {
      type: "long",
      name: "messageCount"
    }]
  };
  defs.BasicGetEmpty = 3932232;
  var methodInfoBasicGetEmpty = defs.methodInfoBasicGetEmpty = {
    id: 3932232,
    classId: 60,
    methodId: 72,
    name: "BasicGetEmpty",
    args: [{
      type: "shortstr",
      name: "clusterId",
      default: ""
    }]
  };
  defs.BasicAck = 3932240;
  var methodInfoBasicAck = defs.methodInfoBasicAck = {
    id: 3932240,
    classId: 60,
    methodId: 80,
    name: "BasicAck",
    args: [{
      type: "longlong",
      name: "deliveryTag",
      default: 0
    }, {
      type: "bit",
      name: "multiple",
      default: false
    }]
  };
  defs.BasicReject = 3932250;
  var methodInfoBasicReject = defs.methodInfoBasicReject = {
    id: 3932250,
    classId: 60,
    methodId: 90,
    name: "BasicReject",
    args: [{
      type: "longlong",
      name: "deliveryTag"
    }, {
      type: "bit",
      name: "requeue",
      default: true
    }]
  };
  defs.BasicRecoverAsync = 3932260;
  var methodInfoBasicRecoverAsync = defs.methodInfoBasicRecoverAsync = {
    id: 3932260,
    classId: 60,
    methodId: 100,
    name: "BasicRecoverAsync",
    args: [{
      type: "bit",
      name: "requeue",
      default: false
    }]
  };
  defs.BasicRecover = 3932270;
  var methodInfoBasicRecover = defs.methodInfoBasicRecover = {
    id: 3932270,
    classId: 60,
    methodId: 110,
    name: "BasicRecover",
    args: [{
      type: "bit",
      name: "requeue",
      default: false
    }]
  };
  defs.BasicRecoverOk = 3932271;
  var methodInfoBasicRecoverOk = defs.methodInfoBasicRecoverOk = {
    id: 3932271,
    classId: 60,
    methodId: 111,
    name: "BasicRecoverOk",
    args: []
  };
  defs.BasicNack = 3932280;
  var methodInfoBasicNack = defs.methodInfoBasicNack = {
    id: 3932280,
    classId: 60,
    methodId: 120,
    name: "BasicNack",
    args: [{
      type: "longlong",
      name: "deliveryTag",
      default: 0
    }, {
      type: "bit",
      name: "multiple",
      default: false
    }, {
      type: "bit",
      name: "requeue",
      default: true
    }]
  };
  defs.ConnectionStart = 655370;
  var methodInfoConnectionStart = defs.methodInfoConnectionStart = {
    id: 655370,
    classId: 10,
    methodId: 10,
    name: "ConnectionStart",
    args: [{
      type: "octet",
      name: "versionMajor",
      default: 0
    }, {
      type: "octet",
      name: "versionMinor",
      default: 9
    }, {
      type: "table",
      name: "serverProperties"
    }, {
      type: "longstr",
      name: "mechanisms",
      default: "PLAIN"
    }, {
      type: "longstr",
      name: "locales",
      default: "en_US"
    }]
  };
  defs.ConnectionStartOk = 655371;
  var methodInfoConnectionStartOk = defs.methodInfoConnectionStartOk = {
    id: 655371,
    classId: 10,
    methodId: 11,
    name: "ConnectionStartOk",
    args: [{
      type: "table",
      name: "clientProperties"
    }, {
      type: "shortstr",
      name: "mechanism",
      default: "PLAIN"
    }, {
      type: "longstr",
      name: "response"
    }, {
      type: "shortstr",
      name: "locale",
      default: "en_US"
    }]
  };
  defs.ConnectionSecure = 655380;
  var methodInfoConnectionSecure = defs.methodInfoConnectionSecure = {
    id: 655380,
    classId: 10,
    methodId: 20,
    name: "ConnectionSecure",
    args: [{
      type: "longstr",
      name: "challenge"
    }]
  };
  defs.ConnectionSecureOk = 655381;
  var methodInfoConnectionSecureOk = defs.methodInfoConnectionSecureOk = {
    id: 655381,
    classId: 10,
    methodId: 21,
    name: "ConnectionSecureOk",
    args: [{
      type: "longstr",
      name: "response"
    }]
  };
  defs.ConnectionTune = 655390;
  var methodInfoConnectionTune = defs.methodInfoConnectionTune = {
    id: 655390,
    classId: 10,
    methodId: 30,
    name: "ConnectionTune",
    args: [{
      type: "short",
      name: "channelMax",
      default: 0
    }, {
      type: "long",
      name: "frameMax",
      default: 0
    }, {
      type: "short",
      name: "heartbeat",
      default: 0
    }]
  };
  defs.ConnectionTuneOk = 655391;
  var methodInfoConnectionTuneOk = defs.methodInfoConnectionTuneOk = {
    id: 655391,
    classId: 10,
    methodId: 31,
    name: "ConnectionTuneOk",
    args: [{
      type: "short",
      name: "channelMax",
      default: 0
    }, {
      type: "long",
      name: "frameMax",
      default: 0
    }, {
      type: "short",
      name: "heartbeat",
      default: 0
    }]
  };
  defs.ConnectionOpen = 655400;
  var methodInfoConnectionOpen = defs.methodInfoConnectionOpen = {
    id: 655400,
    classId: 10,
    methodId: 40,
    name: "ConnectionOpen",
    args: [{
      type: "shortstr",
      name: "virtualHost",
      default: "/"
    }, {
      type: "shortstr",
      name: "capabilities",
      default: ""
    }, {
      type: "bit",
      name: "insist",
      default: false
    }]
  };
  defs.ConnectionOpenOk = 655401;
  var methodInfoConnectionOpenOk = defs.methodInfoConnectionOpenOk = {
    id: 655401,
    classId: 10,
    methodId: 41,
    name: "ConnectionOpenOk",
    args: [{
      type: "shortstr",
      name: "knownHosts",
      default: ""
    }]
  };
  defs.ConnectionClose = 655410;
  var methodInfoConnectionClose = defs.methodInfoConnectionClose = {
    id: 655410,
    classId: 10,
    methodId: 50,
    name: "ConnectionClose",
    args: [{
      type: "short",
      name: "replyCode"
    }, {
      type: "shortstr",
      name: "replyText",
      default: ""
    }, {
      type: "short",
      name: "classId"
    }, {
      type: "short",
      name: "methodId"
    }]
  };
  defs.ConnectionCloseOk = 655411;
  var methodInfoConnectionCloseOk = defs.methodInfoConnectionCloseOk = {
    id: 655411,
    classId: 10,
    methodId: 51,
    name: "ConnectionCloseOk",
    args: []
  };
  defs.ConnectionBlocked = 655420;
  var methodInfoConnectionBlocked = defs.methodInfoConnectionBlocked = {
    id: 655420,
    classId: 10,
    methodId: 60,
    name: "ConnectionBlocked",
    args: [{
      type: "shortstr",
      name: "reason",
      default: ""
    }]
  };
  defs.ConnectionUnblocked = 655421;
  var methodInfoConnectionUnblocked = defs.methodInfoConnectionUnblocked = {
    id: 655421,
    classId: 10,
    methodId: 61,
    name: "ConnectionUnblocked",
    args: []
  };
  defs.ConnectionUpdateSecret = 655430;
  var methodInfoConnectionUpdateSecret = defs.methodInfoConnectionUpdateSecret = {
    id: 655430,
    classId: 10,
    methodId: 70,
    name: "ConnectionUpdateSecret",
    args: [{
      type: "longstr",
      name: "newSecret"
    }, {
      type: "shortstr",
      name: "reason"
    }]
  };
  defs.ConnectionUpdateSecretOk = 655431;
  var methodInfoConnectionUpdateSecretOk = defs.methodInfoConnectionUpdateSecretOk = {
    id: 655431,
    classId: 10,
    methodId: 71,
    name: "ConnectionUpdateSecretOk",
    args: []
  };
  defs.ChannelOpen = 1310730;
  var methodInfoChannelOpen = defs.methodInfoChannelOpen = {
    id: 1310730,
    classId: 20,
    methodId: 10,
    name: "ChannelOpen",
    args: [{
      type: "shortstr",
      name: "outOfBand",
      default: ""
    }]
  };
  defs.ChannelOpenOk = 1310731;
  var methodInfoChannelOpenOk = defs.methodInfoChannelOpenOk = {
    id: 1310731,
    classId: 20,
    methodId: 11,
    name: "ChannelOpenOk",
    args: [{
      type: "longstr",
      name: "channelId",
      default: ""
    }]
  };
  defs.ChannelFlow = 1310740;
  var methodInfoChannelFlow = defs.methodInfoChannelFlow = {
    id: 1310740,
    classId: 20,
    methodId: 20,
    name: "ChannelFlow",
    args: [{
      type: "bit",
      name: "active"
    }]
  };
  defs.ChannelFlowOk = 1310741;
  var methodInfoChannelFlowOk = defs.methodInfoChannelFlowOk = {
    id: 1310741,
    classId: 20,
    methodId: 21,
    name: "ChannelFlowOk",
    args: [{
      type: "bit",
      name: "active"
    }]
  };
  defs.ChannelClose = 1310760;
  var methodInfoChannelClose = defs.methodInfoChannelClose = {
    id: 1310760,
    classId: 20,
    methodId: 40,
    name: "ChannelClose",
    args: [{
      type: "short",
      name: "replyCode"
    }, {
      type: "shortstr",
      name: "replyText",
      default: ""
    }, {
      type: "short",
      name: "classId"
    }, {
      type: "short",
      name: "methodId"
    }]
  };
  defs.ChannelCloseOk = 1310761;
  var methodInfoChannelCloseOk = defs.methodInfoChannelCloseOk = {
    id: 1310761,
    classId: 20,
    methodId: 41,
    name: "ChannelCloseOk",
    args: []
  };
  defs.AccessRequest = 1966090;
  var methodInfoAccessRequest = defs.methodInfoAccessRequest = {
    id: 1966090,
    classId: 30,
    methodId: 10,
    name: "AccessRequest",
    args: [{
      type: "shortstr",
      name: "realm",
      default: "/data"
    }, {
      type: "bit",
      name: "exclusive",
      default: false
    }, {
      type: "bit",
      name: "passive",
      default: true
    }, {
      type: "bit",
      name: "active",
      default: true
    }, {
      type: "bit",
      name: "write",
      default: true
    }, {
      type: "bit",
      name: "read",
      default: true
    }]
  };
  defs.AccessRequestOk = 1966091;
  var methodInfoAccessRequestOk = defs.methodInfoAccessRequestOk = {
    id: 1966091,
    classId: 30,
    methodId: 11,
    name: "AccessRequestOk",
    args: [{
      type: "short",
      name: "ticket",
      default: 1
    }]
  };
  defs.ExchangeDeclare = 2621450;
  var methodInfoExchangeDeclare = defs.methodInfoExchangeDeclare = {
    id: 2621450,
    classId: 40,
    methodId: 10,
    name: "ExchangeDeclare",
    args: [{
      type: "short",
      name: "ticket",
      default: 0
    }, {
      type: "shortstr",
      name: "exchange"
    }, {
      type: "shortstr",
      name: "type",
      default: "direct"
    }, {
      type: "bit",
      name: "passive",
      default: false
    }, {
      type: "bit",
      name: "durable",
      default: false
    }, {
      type: "bit",
      name: "autoDelete",
      default: false
    }, {
      type: "bit",
      name: "internal",
      default: false
    }, {
      type: "bit",
      name: "nowait",
      default: false
    }, {
      type: "table",
      name: "arguments",
      default: {}
    }]
  };
  defs.ExchangeDeclareOk = 2621451;
  var methodInfoExchangeDeclareOk = defs.methodInfoExchangeDeclareOk = {
    id: 2621451,
    classId: 40,
    methodId: 11,
    name: "ExchangeDeclareOk",
    args: []
  };
  defs.ExchangeDelete = 2621460;
  var methodInfoExchangeDelete = defs.methodInfoExchangeDelete = {
    id: 2621460,
    classId: 40,
    methodId: 20,
    name: "ExchangeDelete",
    args: [{
      type: "short",
      name: "ticket",
      default: 0
    }, {
      type: "shortstr",
      name: "exchange"
    }, {
      type: "bit",
      name: "ifUnused",
      default: false
    }, {
      type: "bit",
      name: "nowait",
      default: false
    }]
  };
  defs.ExchangeDeleteOk = 2621461;
  var methodInfoExchangeDeleteOk = defs.methodInfoExchangeDeleteOk = {
    id: 2621461,
    classId: 40,
    methodId: 21,
    name: "ExchangeDeleteOk",
    args: []
  };
  defs.ExchangeBind = 2621470;
  var methodInfoExchangeBind = defs.methodInfoExchangeBind = {
    id: 2621470,
    classId: 40,
    methodId: 30,
    name: "ExchangeBind",
    args: [{
      type: "short",
      name: "ticket",
      default: 0
    }, {
      type: "shortstr",
      name: "destination"
    }, {
      type: "shortstr",
      name: "source"
    }, {
      type: "shortstr",
      name: "routingKey",
      default: ""
    }, {
      type: "bit",
      name: "nowait",
      default: false
    }, {
      type: "table",
      name: "arguments",
      default: {}
    }]
  };
  defs.ExchangeBindOk = 2621471;
  var methodInfoExchangeBindOk = defs.methodInfoExchangeBindOk = {
    id: 2621471,
    classId: 40,
    methodId: 31,
    name: "ExchangeBindOk",
    args: []
  };
  defs.ExchangeUnbind = 2621480;
  var methodInfoExchangeUnbind = defs.methodInfoExchangeUnbind = {
    id: 2621480,
    classId: 40,
    methodId: 40,
    name: "ExchangeUnbind",
    args: [{
      type: "short",
      name: "ticket",
      default: 0
    }, {
      type: "shortstr",
      name: "destination"
    }, {
      type: "shortstr",
      name: "source"
    }, {
      type: "shortstr",
      name: "routingKey",
      default: ""
    }, {
      type: "bit",
      name: "nowait",
      default: false
    }, {
      type: "table",
      name: "arguments",
      default: {}
    }]
  };
  defs.ExchangeUnbindOk = 2621491;
  var methodInfoExchangeUnbindOk = defs.methodInfoExchangeUnbindOk = {
    id: 2621491,
    classId: 40,
    methodId: 51,
    name: "ExchangeUnbindOk",
    args: []
  };
  defs.QueueDeclare = 3276810;
  var methodInfoQueueDeclare = defs.methodInfoQueueDeclare = {
    id: 3276810,
    classId: 50,
    methodId: 10,
    name: "QueueDeclare",
    args: [{
      type: "short",
      name: "ticket",
      default: 0
    }, {
      type: "shortstr",
      name: "queue",
      default: ""
    }, {
      type: "bit",
      name: "passive",
      default: false
    }, {
      type: "bit",
      name: "durable",
      default: false
    }, {
      type: "bit",
      name: "exclusive",
      default: false
    }, {
      type: "bit",
      name: "autoDelete",
      default: false
    }, {
      type: "bit",
      name: "nowait",
      default: false
    }, {
      type: "table",
      name: "arguments",
      default: {}
    }]
  };
  defs.QueueDeclareOk = 3276811;
  var methodInfoQueueDeclareOk = defs.methodInfoQueueDeclareOk = {
    id: 3276811,
    classId: 50,
    methodId: 11,
    name: "QueueDeclareOk",
    args: [{
      type: "shortstr",
      name: "queue"
    }, {
      type: "long",
      name: "messageCount"
    }, {
      type: "long",
      name: "consumerCount"
    }]
  };
  defs.QueueBind = 3276820;
  var methodInfoQueueBind = defs.methodInfoQueueBind = {
    id: 3276820,
    classId: 50,
    methodId: 20,
    name: "QueueBind",
    args: [{
      type: "short",
      name: "ticket",
      default: 0
    }, {
      type: "shortstr",
      name: "queue",
      default: ""
    }, {
      type: "shortstr",
      name: "exchange"
    }, {
      type: "shortstr",
      name: "routingKey",
      default: ""
    }, {
      type: "bit",
      name: "nowait",
      default: false
    }, {
      type: "table",
      name: "arguments",
      default: {}
    }]
  };
  defs.QueueBindOk = 3276821;
  var methodInfoQueueBindOk = defs.methodInfoQueueBindOk = {
    id: 3276821,
    classId: 50,
    methodId: 21,
    name: "QueueBindOk",
    args: []
  };
  defs.QueuePurge = 3276830;
  var methodInfoQueuePurge = defs.methodInfoQueuePurge = {
    id: 3276830,
    classId: 50,
    methodId: 30,
    name: "QueuePurge",
    args: [{
      type: "short",
      name: "ticket",
      default: 0
    }, {
      type: "shortstr",
      name: "queue",
      default: ""
    }, {
      type: "bit",
      name: "nowait",
      default: false
    }]
  };
  defs.QueuePurgeOk = 3276831;
  var methodInfoQueuePurgeOk = defs.methodInfoQueuePurgeOk = {
    id: 3276831,
    classId: 50,
    methodId: 31,
    name: "QueuePurgeOk",
    args: [{
      type: "long",
      name: "messageCount"
    }]
  };
  defs.QueueDelete = 3276840;
  var methodInfoQueueDelete = defs.methodInfoQueueDelete = {
    id: 3276840,
    classId: 50,
    methodId: 40,
    name: "QueueDelete",
    args: [{
      type: "short",
      name: "ticket",
      default: 0
    }, {
      type: "shortstr",
      name: "queue",
      default: ""
    }, {
      type: "bit",
      name: "ifUnused",
      default: false
    }, {
      type: "bit",
      name: "ifEmpty",
      default: false
    }, {
      type: "bit",
      name: "nowait",
      default: false
    }]
  };
  defs.QueueDeleteOk = 3276841;
  var methodInfoQueueDeleteOk = defs.methodInfoQueueDeleteOk = {
    id: 3276841,
    classId: 50,
    methodId: 41,
    name: "QueueDeleteOk",
    args: [{
      type: "long",
      name: "messageCount"
    }]
  };
  defs.QueueUnbind = 3276850;
  var methodInfoQueueUnbind = defs.methodInfoQueueUnbind = {
    id: 3276850,
    classId: 50,
    methodId: 50,
    name: "QueueUnbind",
    args: [{
      type: "short",
      name: "ticket",
      default: 0
    }, {
      type: "shortstr",
      name: "queue",
      default: ""
    }, {
      type: "shortstr",
      name: "exchange"
    }, {
      type: "shortstr",
      name: "routingKey",
      default: ""
    }, {
      type: "table",
      name: "arguments",
      default: {}
    }]
  };
  defs.QueueUnbindOk = 3276851;
  var methodInfoQueueUnbindOk = defs.methodInfoQueueUnbindOk = {
    id: 3276851,
    classId: 50,
    methodId: 51,
    name: "QueueUnbindOk",
    args: []
  };
  defs.TxSelect = 5898250;
  var methodInfoTxSelect = defs.methodInfoTxSelect = {
    id: 5898250,
    classId: 90,
    methodId: 10,
    name: "TxSelect",
    args: []
  };
  defs.TxSelectOk = 5898251;
  var methodInfoTxSelectOk = defs.methodInfoTxSelectOk = {
    id: 5898251,
    classId: 90,
    methodId: 11,
    name: "TxSelectOk",
    args: []
  };
  defs.TxCommit = 5898260;
  var methodInfoTxCommit = defs.methodInfoTxCommit = {
    id: 5898260,
    classId: 90,
    methodId: 20,
    name: "TxCommit",
    args: []
  };
  defs.TxCommitOk = 5898261;
  var methodInfoTxCommitOk = defs.methodInfoTxCommitOk = {
    id: 5898261,
    classId: 90,
    methodId: 21,
    name: "TxCommitOk",
    args: []
  };
  defs.TxRollback = 5898270;
  var methodInfoTxRollback = defs.methodInfoTxRollback = {
    id: 5898270,
    classId: 90,
    methodId: 30,
    name: "TxRollback",
    args: []
  };
  defs.TxRollbackOk = 5898271;
  var methodInfoTxRollbackOk = defs.methodInfoTxRollbackOk = {
    id: 5898271,
    classId: 90,
    methodId: 31,
    name: "TxRollbackOk",
    args: []
  };
  defs.ConfirmSelect = 5570570;
  var methodInfoConfirmSelect = defs.methodInfoConfirmSelect = {
    id: 5570570,
    classId: 85,
    methodId: 10,
    name: "ConfirmSelect",
    args: [{
      type: "bit",
      name: "nowait",
      default: false
    }]
  };
  defs.ConfirmSelectOk = 5570571;
  var methodInfoConfirmSelectOk = defs.methodInfoConfirmSelectOk = {
    id: 5570571,
    classId: 85,
    methodId: 11,
    name: "ConfirmSelectOk",
    args: []
  };
  defs.BasicProperties = 60;
  var propertiesInfoBasicProperties = defs.propertiesInfoBasicProperties = {
    id: 60,
    name: "BasicProperties",
    args: [{
      type: "shortstr",
      name: "contentType"
    }, {
      type: "shortstr",
      name: "contentEncoding"
    }, {
      type: "table",
      name: "headers"
    }, {
      type: "octet",
      name: "deliveryMode"
    }, {
      type: "octet",
      name: "priority"
    }, {
      type: "shortstr",
      name: "correlationId"
    }, {
      type: "shortstr",
      name: "replyTo"
    }, {
      type: "shortstr",
      name: "expiration"
    }, {
      type: "shortstr",
      name: "messageId"
    }, {
      type: "timestamp",
      name: "timestamp"
    }, {
      type: "shortstr",
      name: "type"
    }, {
      type: "shortstr",
      name: "userId"
    }, {
      type: "shortstr",
      name: "appId"
    }, {
      type: "shortstr",
      name: "clusterId"
    }]
  };
  return defs;
}
var frame = {};
var hasRequiredFrame;
function requireFrame() {
  if (hasRequiredFrame) return frame;
  hasRequiredFrame = 1;
  const ints = requireBufferMoreInts();
  var defs2 = requireDefs();
  var constants = defs2.constants;
  var decode = defs2.decode;
  frame.PROTOCOL_HEADER = "AMQP" + String.fromCharCode(0, 0, 9, 1);
  var FRAME_METHOD = constants.FRAME_METHOD, FRAME_HEARTBEAT = constants.FRAME_HEARTBEAT, FRAME_HEADER = constants.FRAME_HEADER, FRAME_BODY = constants.FRAME_BODY, FRAME_END = constants.FRAME_END;
  const TYPE_BYTES = 1;
  const CHANNEL_BYTES = 2;
  const SIZE_BYTES = 4;
  const FRAME_HEADER_BYTES = TYPE_BYTES + CHANNEL_BYTES + SIZE_BYTES;
  const FRAME_END_BYTES = 1;
  function readInt64BE(buffer, offset) {
    if (typeof Buffer.prototype.readBigInt64BE === "function") {
      return Number(buffer.readBigInt64BE(offset));
    }
    return ints.readInt64BE(buffer, offset);
  }
  frame.makeBodyFrame = function(channel2, payload) {
    const frameSize = FRAME_HEADER_BYTES + payload.length + FRAME_END_BYTES;
    const frame2 = Buffer.alloc(frameSize);
    let offset = 0;
    offset = frame2.writeUInt8(FRAME_BODY, offset);
    offset = frame2.writeUInt16BE(channel2, offset);
    offset = frame2.writeInt32BE(payload.length, offset);
    payload.copy(frame2, offset);
    offset += payload.length;
    frame2.writeUInt8(FRAME_END, offset);
    return frame2;
  };
  function parseFrame(bin) {
    if (bin.length < FRAME_HEADER_BYTES) {
      return false;
    }
    const type = bin.readUInt8(0);
    const channel2 = bin.readUInt16BE(1);
    const size = bin.readUInt32BE(3);
    const totalSize = FRAME_HEADER_BYTES + size + FRAME_END_BYTES;
    if (bin.length < totalSize) {
      return false;
    }
    const frameEnd = bin.readUInt8(FRAME_HEADER_BYTES + size);
    if (frameEnd !== FRAME_END) {
      throw new Error("Invalid frame");
    }
    return {
      type,
      channel: channel2,
      size,
      payload: bin.subarray(FRAME_HEADER_BYTES, FRAME_HEADER_BYTES + size),
      rest: bin.subarray(totalSize)
    };
  }
  frame.parseFrame = parseFrame;
  var HEARTBEAT = { channel: 0 };
  frame.decodeFrame = (frame2) => {
    const payload = frame2.payload;
    const channel2 = frame2.channel;
    switch (frame2.type) {
      case FRAME_METHOD: {
        const id = payload.readUInt32BE(0);
        const args = payload.subarray(4);
        const fields = decode(id, args);
        return { id, channel: channel2, fields };
      }
      case FRAME_HEADER: {
        const id = payload.readUInt16BE(0);
        const size = readInt64BE(payload, 4);
        const flagsAndfields = payload.subarray(12);
        const fields = decode(id, flagsAndfields);
        return { id, channel: channel2, size, fields };
      }
      case FRAME_BODY:
        return { channel: channel2, content: payload };
      case FRAME_HEARTBEAT:
        return HEARTBEAT;
      default:
        throw new Error("Unknown frame type " + frame2.type);
    }
  };
  frame.HEARTBEAT_BUF = Buffer.from([
    constants.FRAME_HEARTBEAT,
    0,
    0,
    0,
    0,
    // size = 0
    0,
    0,
    // channel = 0
    constants.FRAME_END
  ]);
  frame.HEARTBEAT = HEARTBEAT;
  return frame;
}
var mux = {};
var hasRequiredMux;
function requireMux() {
  if (hasRequiredMux) return mux;
  hasRequiredMux = 1;
  var assert = require$$0;
  var schedule = typeof setImmediate === "function" ? setImmediate : process.nextTick;
  class Mux {
    constructor(downstream) {
      this.newStreams = [];
      this.oldStreams = [];
      this.blocked = false;
      this.scheduledRead = false;
      this.out = downstream;
      var self2 = this;
      downstream.on("drain", function() {
        self2.blocked = false;
        self2._readIncoming();
      });
    }
    // There are 2 states we can be in:
    // - waiting for outbound capacity, which will be signalled by a
    // - 'drain' event on the downstream; or,
    // - no packets to send, waiting for an inbound buffer to have
    //   packets, which will be signalled by a 'readable' event
    // If we write all packets available whenever there is outbound
    // capacity, we will either run out of outbound capacity (`#write`
    // returns false), or run out of packets (all calls to an
    // `inbound.read()` have returned null).
    _readIncoming() {
      if (this.blocked) return;
      var accepting = true;
      var out = this.out;
      function roundrobin(streams) {
        var s;
        while (accepting && (s = streams.shift())) {
          var chunk = s.read();
          if (chunk !== null) {
            accepting = out.write(chunk);
            streams.push(s);
          }
        }
      }
      roundrobin(this.newStreams);
      if (accepting) {
        assert.equal(0, this.newStreams.length);
        roundrobin(this.oldStreams);
      } else {
        assert(this.newStreams.length > 0, "Expect some new streams to remain");
        Array.prototype.push.apply(this.oldStreams, this.newStreams);
        this.newStreams = [];
      }
      this.blocked = !accepting;
    }
    _scheduleRead() {
      var self2 = this;
      if (!self2.scheduledRead) {
        schedule(function() {
          self2.scheduledRead = false;
          self2._readIncoming();
        });
        self2.scheduledRead = true;
      }
    }
    pipeFrom(readable) {
      var self2 = this;
      function enqueue() {
        self2.newStreams.push(readable);
        self2._scheduleRead();
      }
      function cleanup() {
        readable.removeListener("readable", enqueue);
        readable.removeListener("error", cleanup);
        readable.removeListener("end", cleanup);
        readable.removeListener("unpipeFrom", cleanupIfMe);
      }
      function cleanupIfMe(dest) {
        if (dest === self2) cleanup();
      }
      readable.on("unpipeFrom", cleanupIfMe);
      readable.on("end", cleanup);
      readable.on("error", cleanup);
      readable.on("readable", enqueue);
    }
    unpipeFrom(readable) {
      readable.emit("unpipeFrom", this);
    }
  }
  mux.Mux = Mux;
  return mux;
}
var heartbeat = { exports: {} };
var hasRequiredHeartbeat;
function requireHeartbeat() {
  if (hasRequiredHeartbeat) return heartbeat.exports;
  hasRequiredHeartbeat = 1;
  (function(module) {
    var EventEmitter = require$$0$1;
    module.exports.UNITS_TO_MS = 1e3;
    class Heart extends EventEmitter {
      constructor(interval, checkSend, checkRecv) {
        super();
        this.interval = interval;
        var intervalMs = interval * module.exports.UNITS_TO_MS;
        var beat = this.emit.bind(this, "beat");
        var timeout = this.emit.bind(this, "timeout");
        this.sendTimer = setInterval(
          this.runHeartbeat.bind(this, checkSend, beat),
          intervalMs / 2
        );
        var recvMissed = 0;
        function missedTwo() {
          if (!checkRecv())
            return ++recvMissed < 2;
          else {
            recvMissed = 0;
            return true;
          }
        }
        this.recvTimer = setInterval(
          this.runHeartbeat.bind(this, missedTwo, timeout),
          intervalMs
        );
      }
      clear() {
        clearInterval(this.sendTimer);
        clearInterval(this.recvTimer);
      }
      runHeartbeat(check, fail) {
        if (!check())
          fail();
      }
    }
    module.exports.Heart = Heart;
  })(heartbeat);
  return heartbeat.exports;
}
var format = {};
var hasRequiredFormat;
function requireFormat() {
  if (hasRequiredFormat) return format;
  hasRequiredFormat = 1;
  var defs2 = requireDefs();
  var format$1 = require$$1.format;
  var HEARTBEAT = requireFrame().HEARTBEAT;
  format.closeMessage = function(close) {
    var code = close.fields.replyCode;
    return format$1(
      '%d (%s) with message "%s"',
      code,
      defs2.constant_strs[code],
      close.fields.replyText
    );
  };
  format.methodName = function(id) {
    return defs2.info(id).name;
  };
  format.inspect = function(frame2, showFields) {
    if (frame2 === HEARTBEAT) {
      return "<Heartbeat>";
    } else if (!frame2.id) {
      return format$1(
        "<Content channel:%d size:%d>",
        frame2.channel,
        frame2.size
      );
    } else {
      var info = defs2.info(frame2.id);
      return format$1(
        "<%s channel:%d%s>",
        info.name,
        frame2.channel,
        showFields ? " " + JSON.stringify(frame2.fields, void 0, 2) : ""
      );
    }
  };
  return format;
}
var bitset = {};
var hasRequiredBitset;
function requireBitset() {
  if (hasRequiredBitset) return bitset;
  hasRequiredBitset = 1;
  class BitSet {
    /**
     * @param {number} [size]
     */
    constructor(size) {
      if (size) {
        const numWords = Math.ceil(size / 32);
        this.words = new Array(numWords);
      } else {
        this.words = [];
      }
      this.wordsInUse = 0;
    }
    /**
     * @param {number} numWords
     */
    ensureSize(numWords) {
      const wordsPresent = this.words.length;
      if (wordsPresent < numWords) {
        this.words = this.words.concat(new Array(numWords - wordsPresent));
      }
    }
    /**
     * @param {number} bitIndex
     */
    set(bitIndex) {
      const w = wordIndex(bitIndex);
      if (w >= this.wordsInUse) {
        this.ensureSize(w + 1);
        this.wordsInUse = w + 1;
      }
      const bit = 1 << bitIndex;
      this.words[w] |= bit;
    }
    /**
     * @param {number} bitIndex
     */
    clear(bitIndex) {
      const w = wordIndex(bitIndex);
      if (w >= this.wordsInUse) return;
      const mask = ~(1 << bitIndex);
      this.words[w] &= mask;
    }
    /**
     * @param {number} bitIndex
     */
    get(bitIndex) {
      const w = wordIndex(bitIndex);
      if (w >= this.wordsInUse) return false;
      const bit = 1 << bitIndex;
      return !!(this.words[w] & bit);
    }
    /**
     * Give the next bit that is set on or after fromIndex, or -1 if no such bit
     *
     * @param {number} fromIndex
     */
    nextSetBit(fromIndex) {
      let w = wordIndex(fromIndex);
      if (w >= this.wordsInUse) return -1;
      let word = this.words[w] & 4294967295 << fromIndex;
      while (true) {
        if (word) return w * 32 + trailingZeros(word);
        w++;
        if (w === this.wordsInUse) return -1;
        word = this.words[w];
      }
    }
    /**
     * @param {number} fromIndex
     */
    nextClearBit(fromIndex) {
      let w = wordIndex(fromIndex);
      if (w >= this.wordsInUse) return fromIndex;
      let word = ~this.words[w] & 4294967295 << fromIndex;
      while (true) {
        if (word) return w * 32 + trailingZeros(word);
        w++;
        if (w == this.wordsInUse) return w * 32;
        word = ~this.words[w];
      }
    }
  }
  function wordIndex(bitIndex) {
    return Math.floor(bitIndex / 32);
  }
  function trailingZeros(i) {
    if (i === 0) return 32;
    let y, n = 31;
    y = i << 16;
    if (y != 0) {
      n = n - 16;
      i = y;
    }
    y = i << 8;
    if (y != 0) {
      n = n - 8;
      i = y;
    }
    y = i << 4;
    if (y != 0) {
      n = n - 4;
      i = y;
    }
    y = i << 2;
    if (y != 0) {
      n = n - 2;
      i = y;
    }
    return n - (i << 1 >>> 31);
  }
  bitset.BitSet = BitSet;
  return bitset;
}
var error = {};
var hasRequiredError;
function requireError() {
  if (hasRequiredError) return error;
  hasRequiredError = 1;
  var inherits = require$$1.inherits;
  function trimStack(stack, num) {
    return stack && stack.split("\n").slice(num).join("\n");
  }
  function IllegalOperationError(msg, stack) {
    var tmp = new Error();
    this.message = msg;
    this.stack = this.toString() + "\n" + trimStack(tmp.stack, 2);
    this.stackAtStateChange = stack;
  }
  inherits(IllegalOperationError, Error);
  IllegalOperationError.prototype.name = "IllegalOperationError";
  function stackCapture(reason) {
    var e = new Error();
    return "Stack capture: " + reason + "\n" + trimStack(e.stack, 2);
  }
  error.IllegalOperationError = IllegalOperationError;
  error.stackCapture = stackCapture;
  return error;
}
var hasRequiredConnection;
function requireConnection() {
  if (hasRequiredConnection) return connection;
  hasRequiredConnection = 1;
  var defs2 = requireDefs();
  var constants = defs2.constants;
  var frame2 = requireFrame();
  var HEARTBEAT = frame2.HEARTBEAT;
  var Mux = requireMux().Mux;
  var Duplex = require$$3.Duplex;
  var EventEmitter = require$$0$1;
  var Heart = requireHeartbeat().Heart;
  var methodName = requireFormat().methodName;
  var closeMsg = requireFormat().closeMessage;
  var inspect = requireFormat().inspect;
  var BitSet = requireBitset().BitSet;
  var fmt = require$$1.format;
  var PassThrough = require$$3.PassThrough;
  var IllegalOperationError = requireError().IllegalOperationError;
  var stackCapture = requireError().stackCapture;
  var DEFAULT_WRITE_HWM = 1024;
  var SINGLE_CHUNK_THRESHOLD = 2048;
  class Connection extends EventEmitter {
    constructor(underlying) {
      super();
      var stream = this.stream = wrapStream(underlying);
      this.muxer = new Mux(stream);
      this.rest = Buffer.alloc(0);
      this.frameMax = constants.FRAME_MIN_SIZE;
      this.sentSinceLastCheck = false;
      this.recvSinceLastCheck = false;
      this.expectSocketClose = false;
      this.freeChannels = new BitSet();
      this.channels = [{
        channel: { accept: channel0(this) },
        buffer: underlying
      }];
    }
    // This changed between versions, as did the codec, methods, etc. AMQP
    // 0-9-1 is fairly similar to 0.8, but better, and nothing implements
    // 0.8 that doesn't implement 0-9-1. In other words, it doesn't make
    // much sense to generalise here.
    sendProtocolHeader() {
      this.sendBytes(frame2.PROTOCOL_HEADER);
    }
    /*
    	    The frighteningly complicated opening protocol (spec section 2.2.4):
    
    	       Client -> Server
    
    	         protocol header ->
    	           <- start
    	         start-ok ->
    	       .. next two zero or more times ..
    	           <- secure
    	         secure-ok ->
    	           <- tune
    	         tune-ok ->
    	         open ->
    	           <- open-ok
    
    	  If I'm only supporting SASL's PLAIN mechanism (which I am for the time
    	  being), it gets a bit easier since the server won't in general send
    	  back a `secure`, it'll just send `tune` after the `start-ok`.
    	  (SASL PLAIN: http://tools.ietf.org/html/rfc4616)
    
    	  */
    open(allFields, openCallback0) {
      var self2 = this;
      var openCallback = openCallback0 || function() {
      };
      var tunedOptions = Object.create(allFields);
      function wait(k) {
        self2.step(function(err, frame3) {
          if (err !== null)
            bail(err);
          else if (frame3.channel !== 0) {
            bail(new Error(
              fmt(
                "Frame on channel != 0 during handshake: %s",
                inspect(frame3, false)
              )
            ));
          } else
            k(frame3);
        });
      }
      function expect(Method, k) {
        wait(function(frame3) {
          if (frame3.id === Method)
            k(frame3);
          else {
            bail(new Error(
              fmt(
                "Expected %s; got %s",
                methodName(Method),
                inspect(frame3, false)
              )
            ));
          }
        });
      }
      function bail(err) {
        openCallback(err);
      }
      function send(Method) {
        self2.sendMethod(0, Method, tunedOptions);
      }
      function negotiate(server, desired) {
        if (server === 0 || desired === 0) {
          return Math.max(server, desired);
        } else {
          return Math.min(server, desired);
        }
      }
      function onStart(start) {
        var mechanisms = start.fields.mechanisms.toString().split(" ");
        if (mechanisms.indexOf(allFields.mechanism) < 0) {
          bail(new Error(fmt(
            "SASL mechanism %s is not provided by the server",
            allFields.mechanism
          )));
          return;
        }
        self2.serverProperties = start.fields.serverProperties;
        try {
          send(defs2.ConnectionStartOk);
        } catch (err) {
          bail(err);
          return;
        }
        wait(afterStartOk);
      }
      function afterStartOk(reply) {
        switch (reply.id) {
          case defs2.ConnectionSecure:
            bail(new Error(
              "Wasn't expecting to have to go through secure"
            ));
            break;
          case defs2.ConnectionClose:
            bail(new Error(fmt(
              "Handshake terminated by server: %s",
              closeMsg(reply)
            )));
            break;
          case defs2.ConnectionTune:
            var fields = reply.fields;
            tunedOptions.frameMax = negotiate(fields.frameMax, allFields.frameMax);
            tunedOptions.channelMax = negotiate(fields.channelMax, allFields.channelMax);
            tunedOptions.heartbeat = negotiate(fields.heartbeat, allFields.heartbeat);
            try {
              send(defs2.ConnectionTuneOk);
              send(defs2.ConnectionOpen);
            } catch (err) {
              bail(err);
              return;
            }
            expect(defs2.ConnectionOpenOk, onOpenOk);
            break;
          default:
            bail(new Error(
              fmt(
                "Expected connection.secure, connection.close, or connection.tune during handshake; got %s",
                inspect(reply, false)
              )
            ));
            break;
        }
      }
      function onOpenOk(openOk) {
        self2.channelMax = tunedOptions.channelMax || 65535;
        self2.frameMax = tunedOptions.frameMax || 4294967295;
        self2.heartbeat = tunedOptions.heartbeat;
        self2.heartbeater = self2.startHeartbeater();
        self2.accept = mainAccept;
        succeed(openOk);
      }
      function endWhileOpening(err) {
        bail(err || new Error("Socket closed abruptly during opening handshake"));
      }
      this.stream.on("end", endWhileOpening);
      this.stream.on("error", endWhileOpening);
      function succeed(ok) {
        self2.stream.removeListener("end", endWhileOpening);
        self2.stream.removeListener("error", endWhileOpening);
        self2.stream.on("error", self2.onSocketError.bind(self2));
        self2.stream.on("end", self2.onSocketError.bind(
          self2,
          new Error("Unexpected close")
        ));
        self2.on("frameError", self2.onSocketError.bind(self2));
        self2.acceptLoop();
        openCallback(null, ok);
      }
      this.sendProtocolHeader();
      expect(defs2.ConnectionStart, onStart);
    }
    // Closing things: AMQP has a closing handshake that applies to
    // closing both connects and channels. As the initiating party, I send
    // Close, then ignore all frames until I see either CloseOK --
    // which signifies that the other party has seen the Close and shut
    // the connection or channel down, so it's fine to free resources; or
    // Close, which means the other party also wanted to close the
    // whatever, and I should send CloseOk so it can free resources,
    // then go back to waiting for the CloseOk. If I receive a Close
    // out of the blue, I should throw away any unsent frames (they will
    // be ignored anyway) and send CloseOk, then clean up resources. In
    // general, Close out of the blue signals an error (or a forced
    // closure, which may as well be an error).
    //
    //  RUNNING [1] --- send Close ---> Closing [2] ---> recv Close --+
    //     |                               |                         [3]
    //     |                               +------ send CloseOk ------+
    //  recv Close                   recv CloseOk
    //     |                               |
    //     V                               V
    //  Ended [4] ---- send CloseOk ---> Closed [5]
    //
    // [1] All frames accepted; getting a Close frame from the server
    // moves to Ended; client may initiate a close by sending Close
    // itself.
    // [2] Client has initiated a close; only CloseOk or (simulataneously
    // sent) Close is accepted.
    // [3] Simultaneous close
    // [4] Server won't send any more frames; accept no more frames, send
    // CloseOk.
    // [5] Fully closed, client will send no more, server will send no
    // more. Signal 'close' or 'error'.
    //
    // There are two signalling mechanisms used in the API. The first is
    // that calling `close` will return a promise, that will either
    // resolve once the connection or channel is cleanly shut down, or
    // will reject if the shutdown times out.
    //
    // The second is the 'close' and 'error' events. These are
    // emitted as above. The events will fire *before* promises are
    // resolved.
    // Close the connection without even giving a reason. Typical.
    close(closeCallback) {
      var k = closeCallback && function() {
        closeCallback(null);
      };
      this.closeBecause("Cheers, thanks", constants.REPLY_SUCCESS, k);
    }
    // Close with a reason and a 'code'. I'm pretty sure RabbitMQ totally
    // ignores these; maybe it logs them. The continuation will be invoked
    // when the CloseOk has been received, and before the 'close' event.
    closeBecause(reason, code, k) {
      this.sendMethod(0, defs2.ConnectionClose, {
        replyText: reason,
        replyCode: code,
        methodId: 0,
        classId: 0
      });
      var s = stackCapture("closeBecause called: " + reason);
      this.toClosing(s, k);
    }
    closeWithError(reason, code, error2) {
      this.emit("error", error2);
      this.closeBecause(reason, code);
    }
    onSocketError(err) {
      if (!this.expectSocketClose) {
        this.expectSocketClose = true;
        this.emit("error", err);
        var s = stackCapture("Socket error");
        this.toClosed(s, err);
      }
    }
    // A close has been initiated. Repeat: a close has been initiated.
    // This means we should not send more frames, anyway they will be
    // ignored. We also have to shut down all the channels.
    toClosing(capturedStack, k) {
      var send = this.sendMethod.bind(this);
      this.accept = function(f) {
        if (f.id === defs2.ConnectionCloseOk) {
          if (k)
            k();
          var s = stackCapture("ConnectionCloseOk received");
          this.toClosed(s, void 0);
        } else if (f.id === defs2.ConnectionClose) {
          send(0, defs2.ConnectionCloseOk, {});
        }
      };
      invalidateSend(this, "Connection closing", capturedStack);
    }
    _closeChannels(capturedStack) {
      for (var i = 1; i < this.channels.length; i++) {
        var ch = this.channels[i];
        if (ch !== null) {
          ch.channel.toClosed(capturedStack);
        }
      }
    }
    // A close has been confirmed. Cease all communication.
    toClosed(capturedStack, maybeErr) {
      this._closeChannels(capturedStack);
      var info = fmt(
        "Connection closed (%s)",
        maybeErr ? maybeErr.toString() : "by client"
      );
      invalidateSend(this, info, capturedStack);
      this.accept = invalidOp(info, capturedStack);
      this.close = function(cb) {
        cb && cb(new IllegalOperationError(info, capturedStack));
      };
      if (this.heartbeater)
        this.heartbeater.clear();
      this.expectSocketClose = true;
      this.stream.end();
      this.emit("close", maybeErr);
    }
    _updateSecret(newSecret, reason, cb) {
      this.sendMethod(0, defs2.ConnectionUpdateSecret, {
        newSecret,
        reason
      });
      this.once("update-secret-ok", cb);
    }
    // ===
    startHeartbeater() {
      if (this.heartbeat === 0)
        return null;
      else {
        var self2 = this;
        var hb = new Heart(
          this.heartbeat,
          this.checkSend.bind(this),
          this.checkRecv.bind(this)
        );
        hb.on("timeout", function() {
          var hberr = new Error("Heartbeat timeout");
          self2.emit("error", hberr);
          var s = stackCapture("Heartbeat timeout");
          self2.toClosed(s, hberr);
        });
        hb.on("beat", function() {
          self2.sendHeartbeat();
        });
        return hb;
      }
    }
    // I use an array to keep track of the channels, rather than an
    // object. The channel identifiers are numbers, and allocated by the
    // connection. If I try to allocate low numbers when they are
    // available (which I do, by looking from the start of the bitset),
    // this ought to keep the array small, and out of 'sparse array
    // storage'. I also set entries to null, rather than deleting them, in
    // the expectation that the next channel allocation will fill the slot
    // again rather than growing the array. See
    // http://www.html5rocks.com/en/tutorials/speed/v8/
    freshChannel(channel2, options) {
      var next = this.freeChannels.nextClearBit(1);
      if (next < 0 || next > this.channelMax)
        throw new Error("No channels left to allocate");
      this.freeChannels.set(next);
      var hwm = options && options.highWaterMark || DEFAULT_WRITE_HWM;
      var writeBuffer = new PassThrough({
        objectMode: true,
        highWaterMark: hwm
      });
      this.channels[next] = { channel: channel2, buffer: writeBuffer };
      writeBuffer.on("drain", function() {
        channel2.onBufferDrain();
      });
      this.muxer.pipeFrom(writeBuffer);
      return next;
    }
    releaseChannel(channel2) {
      this.freeChannels.clear(channel2);
      var buffer = this.channels[channel2].buffer;
      buffer.end();
      this.channels[channel2] = null;
    }
    acceptLoop() {
      var self2 = this;
      function go() {
        try {
          var f;
          while (f = self2.recvFrame())
            self2.accept(f);
        } catch (e) {
          self2.emit("frameError", e);
        }
      }
      self2.stream.on("readable", go);
      go();
    }
    step(cb) {
      var self2 = this;
      function recv() {
        var f;
        try {
          f = self2.recvFrame();
        } catch (e) {
          cb(e, null);
          return;
        }
        if (f)
          cb(null, f);
        else
          self2.stream.once("readable", recv);
      }
      recv();
    }
    checkSend() {
      var check = this.sentSinceLastCheck;
      this.sentSinceLastCheck = false;
      return check;
    }
    checkRecv() {
      var check = this.recvSinceLastCheck;
      this.recvSinceLastCheck = false;
      return check;
    }
    sendBytes(bytes) {
      this.sentSinceLastCheck = true;
      this.stream.write(bytes);
    }
    sendHeartbeat() {
      return this.sendBytes(frame2.HEARTBEAT_BUF);
    }
    sendMethod(channel2, Method, fields) {
      var frame3 = encodeMethod(Method, channel2, fields);
      this.sentSinceLastCheck = true;
      var buffer = this.channels[channel2].buffer;
      return buffer.write(frame3);
    }
    sendMessage(channel2, Method, fields, Properties, props, content) {
      if (!Buffer.isBuffer(content))
        throw new TypeError("content is not a buffer");
      var mframe = encodeMethod(Method, channel2, fields);
      var pframe = encodeProperties(
        Properties,
        channel2,
        content.length,
        props
      );
      var buffer = this.channels[channel2].buffer;
      this.sentSinceLastCheck = true;
      var methodHeaderLen = mframe.length + pframe.length;
      var bodyLen = content.length > 0 ? content.length + FRAME_OVERHEAD : 0;
      var allLen = methodHeaderLen + bodyLen;
      if (allLen < SINGLE_CHUNK_THRESHOLD) {
        var all = Buffer.allocUnsafe(allLen);
        var offset = mframe.copy(all, 0);
        offset += pframe.copy(all, offset);
        if (bodyLen > 0)
          makeBodyFrame(channel2, content).copy(all, offset);
        return buffer.write(all);
      } else {
        if (methodHeaderLen < SINGLE_CHUNK_THRESHOLD) {
          var both = Buffer.allocUnsafe(methodHeaderLen);
          var offset = mframe.copy(both, 0);
          pframe.copy(both, offset);
          buffer.write(both);
        } else {
          buffer.write(mframe);
          buffer.write(pframe);
        }
        return this.sendContent(channel2, content);
      }
    }
    sendContent(channel2, body) {
      if (!Buffer.isBuffer(body)) {
        throw new TypeError(fmt("Expected buffer; got %s", body));
      }
      var writeResult = true;
      var buffer = this.channels[channel2].buffer;
      var maxBody = this.frameMax - FRAME_OVERHEAD;
      for (var offset = 0; offset < body.length; offset += maxBody) {
        var end = offset + maxBody;
        var slice = end > body.length ? body.subarray(offset) : body.subarray(offset, end);
        var bodyFrame = makeBodyFrame(channel2, slice);
        writeResult = buffer.write(bodyFrame);
      }
      this.sentSinceLastCheck = true;
      return writeResult;
    }
    recvFrame() {
      var frame3 = parseFrame(this.rest);
      if (!frame3) {
        var incoming = this.stream.read();
        if (incoming === null) {
          return false;
        } else {
          this.recvSinceLastCheck = true;
          this.rest = Buffer.concat([this.rest, incoming]);
          return this.recvFrame();
        }
      } else {
        this.rest = frame3.rest;
        return decodeFrame(frame3);
      }
    }
  }
  function mainAccept(frame3) {
    var rec = this.channels[frame3.channel];
    if (rec) {
      return rec.channel.accept(frame3);
    } else
      this.closeWithError(
        fmt("Frame on unknown channel %d", frame3.channel),
        constants.CHANNEL_ERROR,
        new Error(fmt(
          "Frame on unknown channel: %s",
          inspect(frame3, false)
        ))
      );
  }
  function channel0(connection2) {
    return function(f) {
      if (f === HEARTBEAT) ;
      else if (f.id === defs2.ConnectionClose) {
        connection2.sendMethod(0, defs2.ConnectionCloseOk, {});
        var emsg = fmt("Connection closed: %s", closeMsg(f));
        var s = stackCapture(emsg);
        var e = new Error(emsg);
        e.code = f.fields.replyCode;
        if (isFatalError(e)) {
          connection2.emit("error", e);
        }
        connection2.toClosed(s, e);
      } else if (f.id === defs2.ConnectionBlocked) {
        connection2.emit("blocked", f.fields.reason);
      } else if (f.id === defs2.ConnectionUnblocked) {
        connection2.emit("unblocked");
      } else if (f.id === defs2.ConnectionUpdateSecretOk) {
        connection2.emit("update-secret-ok");
      } else {
        connection2.closeWithError(
          fmt("Unexpected frame on channel 0"),
          constants.UNEXPECTED_FRAME,
          new Error(fmt(
            "Unexpected frame on channel 0: %s",
            inspect(f, false)
          ))
        );
      }
    };
  }
  function invalidOp(msg, stack) {
    return function() {
      throw new IllegalOperationError(msg, stack);
    };
  }
  function invalidateSend(conn, msg, stack) {
    conn.sendMethod = conn.sendContent = conn.sendMessage = invalidOp(msg, stack);
  }
  var encodeMethod = defs2.encodeMethod;
  var encodeProperties = defs2.encodeProperties;
  var FRAME_OVERHEAD = defs2.FRAME_OVERHEAD;
  var makeBodyFrame = frame2.makeBodyFrame;
  var parseFrame = frame2.parseFrame;
  var decodeFrame = frame2.decodeFrame;
  function wrapStream(s) {
    if (s instanceof Duplex) return s;
    else {
      var ws = new Duplex();
      ws.wrap(s);
      ws._write = function(chunk, encoding, callback) {
        return s.write(chunk, encoding, callback);
      };
      return ws;
    }
  }
  function isFatalError(error2) {
    switch (error2 && error2.code) {
      case defs2.constants.CONNECTION_FORCED:
      case defs2.constants.REPLY_SUCCESS:
        return false;
      default:
        return true;
    }
  }
  connection.Connection = Connection;
  connection.isFatalError = isFatalError;
  return connection;
}
var credentials = {};
var hasRequiredCredentials;
function requireCredentials() {
  if (hasRequiredCredentials) return credentials;
  hasRequiredCredentials = 1;
  var codec2 = requireCodec();
  credentials.plain = function(user, passwd) {
    return {
      mechanism: "PLAIN",
      response: function() {
        return Buffer.from(["", user, passwd].join(String.fromCharCode(0)));
      },
      username: user,
      password: passwd
    };
  };
  credentials.amqplain = function(user, passwd) {
    return {
      mechanism: "AMQPLAIN",
      response: function() {
        const buffer = Buffer.alloc(16384);
        const size = codec2.encodeTable(buffer, { LOGIN: user, PASSWORD: passwd }, 0);
        return buffer.subarray(4, size);
      },
      username: user,
      password: passwd
    };
  };
  credentials.external = function() {
    return {
      mechanism: "EXTERNAL",
      response: function() {
        return Buffer.from("");
      }
    };
  };
  return credentials;
}
const version = "0.10.8";
const require$$5 = {
  version
};
var hasRequiredConnect;
function requireConnect() {
  if (hasRequiredConnect) return connect;
  hasRequiredConnect = 1;
  var URL = requireUrlParse();
  var QS = require$$1$1;
  var Connection = requireConnection().Connection;
  var fmt = require$$1.format;
  var credentials2 = requireCredentials();
  function copyInto(obj, target) {
    var keys = Object.keys(obj);
    var i = keys.length;
    while (i--) {
      var k = keys[i];
      target[k] = obj[k];
    }
    return target;
  }
  function clone(obj) {
    return copyInto(obj, {});
  }
  var CLIENT_PROPERTIES = {
    "product": "amqplib",
    "version": require$$5.version,
    "platform": fmt("Node.JS %s", process.version),
    "information": "https://amqp-node.github.io/amqplib/",
    "capabilities": {
      "publisher_confirms": true,
      "exchange_exchange_bindings": true,
      "basic.nack": true,
      "consumer_cancel_notify": true,
      "connection.blocked": true,
      "authentication_failure_close": true
    }
  };
  function openFrames(vhost, query, credentials3, extraClientProperties) {
    if (!vhost)
      vhost = "/";
    else
      vhost = QS.unescape(vhost);
    var query = query || {};
    function intOrDefault(val, def) {
      return val === void 0 ? def : parseInt(val);
    }
    var clientProperties = Object.create(CLIENT_PROPERTIES);
    return {
      // start-ok
      "clientProperties": copyInto(extraClientProperties, clientProperties),
      "mechanism": credentials3.mechanism,
      "response": credentials3.response(),
      "locale": query.locale || "en_US",
      // tune-ok
      "channelMax": intOrDefault(query.channelMax, 0),
      "frameMax": intOrDefault(query.frameMax, 131072),
      "heartbeat": intOrDefault(query.heartbeat, 0),
      // open
      "virtualHost": vhost,
      "capabilities": "",
      "insist": 0
    };
  }
  function credentialsFromUrl(parts) {
    var user = "guest", passwd = "guest";
    if (parts.username != "" || parts.password != "") {
      user = parts.username ? unescape(parts.username) : "";
      passwd = parts.password ? unescape(parts.password) : "";
    }
    return credentials2.plain(user, passwd);
  }
  function connect$1(url2, socketOptions, openCallback) {
    var sockopts = clone(socketOptions || {});
    url2 = url2 || "amqp://localhost";
    var noDelay = !!sockopts.noDelay;
    var timeout = sockopts.timeout;
    var keepAlive = !!sockopts.keepAlive;
    var keepAliveDelay = sockopts.keepAliveDelay || 0;
    var extraClientProperties = sockopts.clientProperties || {};
    var protocol, fields;
    if (typeof url2 === "object") {
      protocol = (url2.protocol || "amqp") + ":";
      sockopts.host = url2.hostname;
      sockopts.servername = sockopts.servername || url2.hostname;
      sockopts.port = url2.port || (protocol === "amqp:" ? 5672 : 5671);
      var user, pass;
      if (url2.username == void 0 && url2.password == void 0) {
        user = "guest";
        pass = "guest";
      } else {
        user = url2.username || "";
        pass = url2.password || "";
      }
      var config = {
        locale: url2.locale,
        channelMax: url2.channelMax,
        frameMax: url2.frameMax,
        heartbeat: url2.heartbeat
      };
      fields = openFrames(url2.vhost, config, sockopts.credentials || credentials2.plain(user, pass), extraClientProperties);
    } else {
      var parts = URL(url2, true);
      protocol = parts.protocol;
      sockopts.host = parts.hostname;
      sockopts.servername = sockopts.servername || parts.hostname;
      sockopts.port = parseInt(parts.port) || (protocol === "amqp:" ? 5672 : 5671);
      var vhost = parts.pathname ? parts.pathname.substr(1) : null;
      fields = openFrames(vhost, parts.query, sockopts.credentials || credentialsFromUrl(parts), extraClientProperties);
    }
    var sockok = false;
    var sock;
    function onConnect() {
      sockok = true;
      sock.setNoDelay(noDelay);
      if (keepAlive) sock.setKeepAlive(keepAlive, keepAliveDelay);
      var c = new Connection(sock);
      c.open(fields, function(err, ok) {
        if (timeout) sock.setTimeout(0);
        if (err === null) {
          openCallback(null, c);
        } else {
          sock.end();
          sock.destroy();
          openCallback(err);
        }
      });
    }
    if (protocol === "amqp:") {
      sock = require$$6.connect(sockopts, onConnect);
    } else if (protocol === "amqps:") {
      sock = require$$7.connect(sockopts, onConnect);
    } else {
      throw new Error("Expected amqp: or amqps: as the protocol; got " + protocol);
    }
    if (timeout) {
      sock.setTimeout(timeout, function() {
        sock.end();
        sock.destroy();
        openCallback(new Error("connect ETIMEDOUT"));
      });
    }
    sock.once("error", function(err) {
      if (!sockok) openCallback(err);
    });
  }
  connect.connect = connect$1;
  connect.credentialsFromUrl = credentialsFromUrl;
  return connect;
}
var channel_model = {};
var channel = {};
var hasRequiredChannel;
function requireChannel() {
  if (hasRequiredChannel) return channel;
  hasRequiredChannel = 1;
  var defs2 = requireDefs();
  var closeMsg = requireFormat().closeMessage;
  var inspect = requireFormat().inspect;
  var methodName = requireFormat().methodName;
  var assert = require$$0;
  var EventEmitter = require$$0$1;
  var fmt = require$$1.format;
  var IllegalOperationError = requireError().IllegalOperationError;
  var stackCapture = requireError().stackCapture;
  class Channel extends EventEmitter {
    constructor(connection2) {
      super();
      this.connection = connection2;
      this.reply = null;
      this.pending = [];
      this.lwm = 1;
      this.unconfirmed = [];
      this.on("ack", this.handleConfirm.bind(this, function(cb) {
        if (cb)
          cb(null);
      }));
      this.on("nack", this.handleConfirm.bind(this, function(cb) {
        if (cb)
          cb(new Error("message nacked"));
      }));
      this.on("close", function() {
        var cb;
        while (cb = this.unconfirmed.shift()) {
          if (cb)
            cb(new Error("channel closed"));
        }
      });
      this.handleMessage = acceptDeliveryOrReturn;
    }
    setOptions(options) {
      this.options = options;
    }
    allocate() {
      this.ch = this.connection.freshChannel(this, this.options);
      return this;
    }
    // Incoming frames are either notifications of e.g., message delivery,
    // or replies to something we've sent. In general I deal with the
    // former by emitting an event, and with the latter by keeping a track
    // of what's expecting a reply.
    //
    // The AMQP specification implies that RPCs can't be pipelined; that
    // is, you can have only one outstanding RPC on a channel at a
    // time. Certainly that's what RabbitMQ and its clients assume. For
    // this reason, I buffer RPCs if the channel is already waiting for a
    // reply.
    // Just send the damn frame.
    sendImmediately(method, fields) {
      return this.connection.sendMethod(this.ch, method, fields);
    }
    // Invariant: !this.reply -> pending.length == 0. That is, whenever we
    // clear a reply, we must send another RPC (and thereby fill
    // this.reply) if there is one waiting. The invariant relevant here
    // and in `accept`.
    sendOrEnqueue(method, fields, reply) {
      if (!this.reply) {
        assert(this.pending.length === 0);
        this.reply = reply;
        this.sendImmediately(method, fields);
      } else {
        this.pending.push({
          method,
          fields,
          reply
        });
      }
    }
    sendMessage(fields, properties, content) {
      return this.connection.sendMessage(
        this.ch,
        defs2.BasicPublish,
        fields,
        defs2.BasicProperties,
        properties,
        content
      );
    }
    // Internal, synchronously resolved RPC; the return value is resolved
    // with the whole frame.
    _rpc(method, fields, expect, cb) {
      var self2 = this;
      function reply(err, f) {
        if (err === null) {
          if (f.id === expect) {
            return cb(null, f);
          } else {
            var expectedName = methodName(expect);
            var e = new Error(fmt(
              "Expected %s; got %s",
              expectedName,
              inspect(f, false)
            ));
            self2.closeWithError(
              f.id,
              fmt(
                "Expected %s; got %s",
                expectedName,
                methodName(f.id)
              ),
              defs2.constants.UNEXPECTED_FRAME,
              e
            );
            return cb(e);
          }
        } else if (err instanceof Error)
          return cb(err);
        else {
          var closeReason = (err.fields.classId << 16) + err.fields.methodId;
          var e = method === closeReason ? fmt(
            "Operation failed: %s; %s",
            methodName(method),
            closeMsg(err)
          ) : fmt("Channel closed by server: %s", closeMsg(err));
          var closeFrameError = new Error(e);
          closeFrameError.code = err.fields.replyCode;
          closeFrameError.classId = err.fields.classId;
          closeFrameError.methodId = err.fields.methodId;
          return cb(closeFrameError);
        }
      }
      this.sendOrEnqueue(method, fields, reply);
    }
    // Move to entirely closed state.
    toClosed(capturedStack) {
      this._rejectPending();
      invalidateSend(this, "Channel closed", capturedStack);
      this.accept = invalidOp("Channel closed", capturedStack);
      this.connection.releaseChannel(this.ch);
      this.emit("close");
    }
    // Stop being able to send and receive methods and content. Used when
    // we close the channel. Invokes the continuation once the server has
    // acknowledged the close, but before the channel is moved to the
    // closed state.
    toClosing(capturedStack, k) {
      var send = this.sendImmediately.bind(this);
      invalidateSend(this, "Channel closing", capturedStack);
      this.accept = function(f) {
        if (f.id === defs2.ChannelCloseOk) {
          if (k)
            k();
          var s = stackCapture("ChannelCloseOk frame received");
          this.toClosed(s);
        } else if (f.id === defs2.ChannelClose) {
          send(defs2.ChannelCloseOk, {});
        }
      };
    }
    _rejectPending() {
      function rej(r) {
        r(new Error("Channel ended, no reply will be forthcoming"));
      }
      if (this.reply !== null)
        rej(this.reply);
      this.reply = null;
      var discard;
      while (discard = this.pending.shift())
        rej(discard.reply);
      this.pending = null;
    }
    closeBecause(reason, code, k) {
      this.sendImmediately(defs2.ChannelClose, {
        replyText: reason,
        replyCode: code,
        methodId: 0,
        classId: 0
      });
      var s = stackCapture("closeBecause called: " + reason);
      this.toClosing(s, k);
    }
    // If we close because there's been an error, we need to distinguish
    // between what we tell the server (`reason`) and what we report as
    // the cause in the client (`error`).
    closeWithError(id, reason, code, error2) {
      var self2 = this;
      this.closeBecause(reason, code, function() {
        error2.code = code;
        if (id) {
          error2.classId = defs2.info(id).classId;
          error2.methodId = defs2.info(id).methodId;
        }
        self2.emit("error", error2);
      });
    }
    // A trampolining state machine for message frames on a channel. A
    // message arrives in at least two frames: first, a method announcing
    // the message (either a BasicDeliver or BasicGetOk); then, a message
    // header with the message properties; then, zero or more content
    // frames.
    // Keep the try/catch localised, in an attempt to avoid disabling
    // optimisation
    acceptMessageFrame(f) {
      try {
        this.handleMessage = this.handleMessage(f);
      } catch (msg) {
        if (typeof msg === "string") {
          this.closeWithError(
            f.id,
            msg,
            defs2.constants.UNEXPECTED_FRAME,
            new Error(msg)
          );
        } else if (msg instanceof Error) {
          this.closeWithError(
            f.id,
            "Error while processing message",
            defs2.constants.INTERNAL_ERROR,
            msg
          );
        } else {
          this.closeWithError(
            f.id,
            "Internal error while processing message",
            defs2.constants.INTERNAL_ERROR,
            new Error(msg.toString())
          );
        }
      }
    }
    handleConfirm(handle, f) {
      var tag = f.deliveryTag;
      var multi = f.multiple;
      if (multi) {
        var confirmed = this.unconfirmed.splice(0, tag - this.lwm + 1);
        this.lwm = tag + 1;
        confirmed.forEach(handle);
      } else {
        var c;
        if (tag === this.lwm) {
          c = this.unconfirmed.shift();
          this.lwm++;
          while (this.unconfirmed[0] === null) {
            this.unconfirmed.shift();
            this.lwm++;
          }
        } else {
          c = this.unconfirmed[tag - this.lwm];
          this.unconfirmed[tag - this.lwm] = null;
        }
        handle(c);
      }
    }
    pushConfirmCallback(cb) {
      this.unconfirmed.push(cb || false);
    }
    onBufferDrain() {
      this.emit("drain");
    }
    accept(f) {
      switch (f.id) {
        // Message frames
        case void 0:
        // content frame!
        case defs2.BasicDeliver:
        case defs2.BasicReturn:
        case defs2.BasicProperties:
          return this.acceptMessageFrame(f);
        // confirmations, need to do confirm.select first
        case defs2.BasicAck:
          return this.emit("ack", f.fields);
        case defs2.BasicNack:
          return this.emit("nack", f.fields);
        case defs2.BasicCancel:
          return this.emit("cancel", f.fields);
        case defs2.ChannelClose:
          if (this.reply) {
            var reply = this.reply;
            this.reply = null;
            reply(f);
          }
          var emsg = "Channel closed by server: " + closeMsg(f);
          this.sendImmediately(defs2.ChannelCloseOk, {});
          var error2 = new Error(emsg);
          error2.code = f.fields.replyCode;
          error2.classId = f.fields.classId;
          error2.methodId = f.fields.methodId;
          this.emit("error", error2);
          var s = stackCapture(emsg);
          this.toClosed(s);
          return;
        case defs2.BasicFlow:
          return this.closeWithError(
            f.id,
            "Flow not implemented",
            defs2.constants.NOT_IMPLEMENTED,
            new Error("Flow not implemented")
          );
        default:
          var reply = this.reply;
          this.reply = null;
          if (this.pending.length > 0) {
            var send = this.pending.shift();
            this.reply = send.reply;
            this.sendImmediately(send.method, send.fields);
          }
          return reply(null, f);
      }
    }
  }
  function invalidOp(msg, stack) {
    return function() {
      throw new IllegalOperationError(msg, stack);
    };
  }
  function invalidateSend(ch, msg, stack) {
    ch.sendImmediately = ch.sendOrEnqueue = ch.sendMessage = invalidOp(msg, stack);
  }
  function acceptDeliveryOrReturn(f) {
    var event;
    if (f.id === defs2.BasicDeliver) event = "delivery";
    else if (f.id === defs2.BasicReturn) event = "return";
    else throw fmt(
      "Expected BasicDeliver or BasicReturn; got %s",
      inspect(f)
    );
    var self2 = this;
    var fields = f.fields;
    return acceptMessage(function(message) {
      message.fields = fields;
      self2.emit(event, message);
    });
  }
  function acceptMessage(continuation) {
    var totalSize = 0, remaining = 0;
    var buffers = null;
    var message = {
      fields: null,
      properties: null,
      content: null
    };
    return headers;
    function headers(f) {
      if (f.id === defs2.BasicProperties) {
        message.properties = f.fields;
        totalSize = remaining = f.size;
        if (totalSize === 0) {
          message.content = Buffer.alloc(0);
          continuation(message);
          return acceptDeliveryOrReturn;
        } else {
          return content;
        }
      } else {
        throw "Expected headers frame after delivery";
      }
    }
    function content(f) {
      if (f.content) {
        var size = f.content.length;
        remaining -= size;
        if (remaining === 0) {
          if (buffers !== null) {
            buffers.push(f.content);
            message.content = Buffer.concat(buffers);
          } else {
            message.content = f.content;
          }
          continuation(message);
          return acceptDeliveryOrReturn;
        } else if (remaining < 0) {
          throw fmt(
            "Too much content sent! Expected %d bytes",
            totalSize
          );
        } else {
          if (buffers !== null)
            buffers.push(f.content);
          else
            buffers = [f.content];
          return content;
        }
      } else throw "Expected content frame after headers";
    }
  }
  class BaseChannel extends Channel {
    constructor(connection2) {
      super(connection2);
      this.consumers = /* @__PURE__ */ new Map();
    }
    // Not sure I like the ff, it's going to be changing hidden classes
    // all over the place. On the other hand, whaddya do.
    registerConsumer(tag, callback) {
      this.consumers.set(tag, callback);
    }
    unregisterConsumer(tag) {
      this.consumers.delete(tag);
    }
    dispatchMessage(fields, message) {
      var consumerTag = fields.consumerTag;
      var consumer = this.consumers.get(consumerTag);
      if (consumer) {
        return consumer(message);
      } else {
        throw new Error("Unknown consumer: " + consumerTag);
      }
    }
    handleDelivery(message) {
      return this.dispatchMessage(message.fields, message);
    }
    handleCancel(fields) {
      var result = this.dispatchMessage(fields, null);
      this.unregisterConsumer(fields.consumerTag);
      return result;
    }
  }
  channel.acceptMessage = acceptMessage;
  channel.BaseChannel = BaseChannel;
  channel.Channel = Channel;
  return channel;
}
var api_args;
var hasRequiredApi_args;
function requireApi_args() {
  if (hasRequiredApi_args) return api_args;
  hasRequiredApi_args = 1;
  function setIfDefined(obj, prop, value) {
    if (value != void 0) obj[prop] = value;
  }
  var EMPTY_OPTIONS = Object.freeze({});
  var Args = {};
  Args.assertQueue = function(queue, options) {
    queue = queue || "";
    options = options || EMPTY_OPTIONS;
    var argt = Object.create(options.arguments || null);
    setIfDefined(argt, "x-expires", options.expires);
    setIfDefined(argt, "x-message-ttl", options.messageTtl);
    setIfDefined(
      argt,
      "x-dead-letter-exchange",
      options.deadLetterExchange
    );
    setIfDefined(
      argt,
      "x-dead-letter-routing-key",
      options.deadLetterRoutingKey
    );
    setIfDefined(argt, "x-max-length", options.maxLength);
    setIfDefined(argt, "x-max-priority", options.maxPriority);
    setIfDefined(argt, "x-overflow", options.overflow);
    setIfDefined(argt, "x-queue-mode", options.queueMode);
    return {
      queue,
      exclusive: !!options.exclusive,
      durable: options.durable === void 0 ? true : options.durable,
      autoDelete: !!options.autoDelete,
      arguments: argt,
      passive: false,
      // deprecated but we have to include it
      ticket: 0,
      nowait: false
    };
  };
  Args.checkQueue = function(queue) {
    return {
      queue,
      passive: true,
      // switch to "completely different" mode
      nowait: false,
      durable: true,
      autoDelete: false,
      exclusive: false,
      // ignored
      ticket: 0
    };
  };
  Args.deleteQueue = function(queue, options) {
    options = options || EMPTY_OPTIONS;
    return {
      queue,
      ifUnused: !!options.ifUnused,
      ifEmpty: !!options.ifEmpty,
      ticket: 0,
      nowait: false
    };
  };
  Args.purgeQueue = function(queue) {
    return {
      queue,
      ticket: 0,
      nowait: false
    };
  };
  Args.bindQueue = function(queue, source, pattern, argt) {
    return {
      queue,
      exchange: source,
      routingKey: pattern,
      arguments: argt,
      ticket: 0,
      nowait: false
    };
  };
  Args.unbindQueue = function(queue, source, pattern, argt) {
    return {
      queue,
      exchange: source,
      routingKey: pattern,
      arguments: argt,
      ticket: 0,
      nowait: false
    };
  };
  Args.assertExchange = function(exchange, type, options) {
    options = options || EMPTY_OPTIONS;
    var argt = Object.create(options.arguments || null);
    setIfDefined(argt, "alternate-exchange", options.alternateExchange);
    return {
      exchange,
      ticket: 0,
      type,
      passive: false,
      durable: options.durable === void 0 ? true : options.durable,
      autoDelete: !!options.autoDelete,
      internal: !!options.internal,
      nowait: false,
      arguments: argt
    };
  };
  Args.checkExchange = function(exchange) {
    return {
      exchange,
      passive: true,
      // switch to 'may as well be another method' mode
      nowait: false,
      // ff are ignored
      durable: true,
      internal: false,
      type: "",
      autoDelete: false,
      ticket: 0
    };
  };
  Args.deleteExchange = function(exchange, options) {
    options = options || EMPTY_OPTIONS;
    return {
      exchange,
      ifUnused: !!options.ifUnused,
      ticket: 0,
      nowait: false
    };
  };
  Args.bindExchange = function(dest, source, pattern, argt) {
    return {
      source,
      destination: dest,
      routingKey: pattern,
      arguments: argt,
      ticket: 0,
      nowait: false
    };
  };
  Args.unbindExchange = function(dest, source, pattern, argt) {
    return {
      source,
      destination: dest,
      routingKey: pattern,
      arguments: argt,
      ticket: 0,
      nowait: false
    };
  };
  Args.publish = function(exchange, routingKey, options) {
    options = options || EMPTY_OPTIONS;
    function convertCC(cc) {
      if (cc === void 0) {
        return void 0;
      } else if (Array.isArray(cc)) {
        return cc.map(String);
      } else return [String(cc)];
    }
    var headers = Object.create(options.headers || null);
    setIfDefined(headers, "CC", convertCC(options.CC));
    setIfDefined(headers, "BCC", convertCC(options.BCC));
    var deliveryMode;
    if (options.persistent !== void 0)
      deliveryMode = options.persistent ? 2 : 1;
    else if (typeof options.deliveryMode === "number")
      deliveryMode = options.deliveryMode;
    else if (options.deliveryMode)
      deliveryMode = 2;
    var expiration = options.expiration;
    if (expiration !== void 0) expiration = expiration.toString();
    return {
      // method fields
      exchange,
      routingKey,
      mandatory: !!options.mandatory,
      immediate: false,
      // RabbitMQ doesn't implement this any more
      ticket: void 0,
      // properties
      contentType: options.contentType,
      contentEncoding: options.contentEncoding,
      headers,
      deliveryMode,
      priority: options.priority,
      correlationId: options.correlationId,
      replyTo: options.replyTo,
      expiration,
      messageId: options.messageId,
      timestamp: options.timestamp,
      type: options.type,
      userId: options.userId,
      appId: options.appId,
      clusterId: void 0
    };
  };
  Args.consume = function(queue, options) {
    options = options || EMPTY_OPTIONS;
    var argt = Object.create(options.arguments || null);
    setIfDefined(argt, "x-priority", options.priority);
    return {
      ticket: 0,
      queue,
      consumerTag: options.consumerTag || "",
      noLocal: !!options.noLocal,
      noAck: !!options.noAck,
      exclusive: !!options.exclusive,
      nowait: false,
      arguments: argt
    };
  };
  Args.cancel = function(consumerTag) {
    return {
      consumerTag,
      nowait: false
    };
  };
  Args.get = function(queue, options) {
    options = options || EMPTY_OPTIONS;
    return {
      ticket: 0,
      queue,
      noAck: !!options.noAck
    };
  };
  Args.ack = function(tag, allUpTo) {
    return {
      deliveryTag: tag,
      multiple: !!allUpTo
    };
  };
  Args.nack = function(tag, allUpTo, requeue) {
    return {
      deliveryTag: tag,
      multiple: !!allUpTo,
      requeue: requeue === void 0 ? true : requeue
    };
  };
  Args.reject = function(tag, requeue) {
    return {
      deliveryTag: tag,
      requeue: requeue === void 0 ? true : requeue
    };
  };
  Args.prefetch = function(count, global2) {
    return {
      prefetchCount: count || 0,
      prefetchSize: 0,
      global: !!global2
    };
  };
  Args.recover = function() {
    return { requeue: true };
  };
  api_args = Object.freeze(Args);
  return api_args;
}
var hasRequiredChannel_model;
function requireChannel_model() {
  if (hasRequiredChannel_model) return channel_model;
  hasRequiredChannel_model = 1;
  const EventEmitter = require$$0$1;
  const promisify = require$$1.promisify;
  const defs2 = requireDefs();
  const { BaseChannel } = requireChannel();
  const { acceptMessage } = requireChannel();
  const Args = requireApi_args();
  const { inspect } = requireFormat();
  class ChannelModel extends EventEmitter {
    constructor(connection2) {
      super();
      this.connection = connection2;
      ["error", "close", "blocked", "unblocked"].forEach((ev) => {
        connection2.on(ev, this.emit.bind(this, ev));
      });
    }
    close() {
      return promisify(this.connection.close.bind(this.connection))();
    }
    updateSecret(newSecret, reason) {
      return promisify(this.connection._updateSecret.bind(this.connection))(newSecret, reason);
    }
    async createChannel(options) {
      const channel2 = new Channel(this.connection);
      channel2.setOptions(options);
      await channel2.open();
      return channel2;
    }
    async createConfirmChannel(options) {
      const channel2 = new ConfirmChannel(this.connection);
      channel2.setOptions(options);
      await channel2.open();
      await channel2.rpc(defs2.ConfirmSelect, { nowait: false }, defs2.ConfirmSelectOk);
      return channel2;
    }
  }
  class Channel extends BaseChannel {
    constructor(connection2) {
      super(connection2);
      this.on("delivery", this.handleDelivery.bind(this));
      this.on("cancel", this.handleCancel.bind(this));
    }
    // An RPC that returns a 'proper' promise, which resolves to just the
    // response's fields; this is intended to be suitable for implementing
    // API procedures.
    async rpc(method, fields, expect) {
      const f = await promisify((cb) => {
        return this._rpc(method, fields, expect, cb);
      })();
      return f.fields;
    }
    // Do the remarkably simple channel open handshake
    async open() {
      const ch = await this.allocate.bind(this)();
      return ch.rpc(
        defs2.ChannelOpen,
        { outOfBand: "" },
        defs2.ChannelOpenOk
      );
    }
    close() {
      return promisify((cb) => {
        return this.closeBecause(
          "Goodbye",
          defs2.constants.REPLY_SUCCESS,
          cb
        );
      })();
    }
    // === Public API, declaring queues and stuff ===
    assertQueue(queue, options) {
      return this.rpc(
        defs2.QueueDeclare,
        Args.assertQueue(queue, options),
        defs2.QueueDeclareOk
      );
    }
    checkQueue(queue) {
      return this.rpc(
        defs2.QueueDeclare,
        Args.checkQueue(queue),
        defs2.QueueDeclareOk
      );
    }
    deleteQueue(queue, options) {
      return this.rpc(
        defs2.QueueDelete,
        Args.deleteQueue(queue, options),
        defs2.QueueDeleteOk
      );
    }
    purgeQueue(queue) {
      return this.rpc(
        defs2.QueuePurge,
        Args.purgeQueue(queue),
        defs2.QueuePurgeOk
      );
    }
    bindQueue(queue, source, pattern, argt) {
      return this.rpc(
        defs2.QueueBind,
        Args.bindQueue(queue, source, pattern, argt),
        defs2.QueueBindOk
      );
    }
    unbindQueue(queue, source, pattern, argt) {
      return this.rpc(
        defs2.QueueUnbind,
        Args.unbindQueue(queue, source, pattern, argt),
        defs2.QueueUnbindOk
      );
    }
    assertExchange(exchange, type, options) {
      return this.rpc(
        defs2.ExchangeDeclare,
        Args.assertExchange(exchange, type, options),
        defs2.ExchangeDeclareOk
      ).then((_ok) => {
        return { exchange };
      });
    }
    checkExchange(exchange) {
      return this.rpc(
        defs2.ExchangeDeclare,
        Args.checkExchange(exchange),
        defs2.ExchangeDeclareOk
      );
    }
    deleteExchange(name, options) {
      return this.rpc(
        defs2.ExchangeDelete,
        Args.deleteExchange(name, options),
        defs2.ExchangeDeleteOk
      );
    }
    bindExchange(dest, source, pattern, argt) {
      return this.rpc(
        defs2.ExchangeBind,
        Args.bindExchange(dest, source, pattern, argt),
        defs2.ExchangeBindOk
      );
    }
    unbindExchange(dest, source, pattern, argt) {
      return this.rpc(
        defs2.ExchangeUnbind,
        Args.unbindExchange(dest, source, pattern, argt),
        defs2.ExchangeUnbindOk
      );
    }
    // Working with messages
    publish(exchange, routingKey, content, options) {
      const fieldsAndProps = Args.publish(exchange, routingKey, options);
      return this.sendMessage(fieldsAndProps, fieldsAndProps, content);
    }
    sendToQueue(queue, content, options) {
      return this.publish("", queue, content, options);
    }
    consume(queue, callback, options) {
      const fields = Args.consume(queue, options);
      return new Promise((resolve, reject) => {
        this._rpc(defs2.BasicConsume, fields, defs2.BasicConsumeOk, (err, ok) => {
          if (err) return reject(err);
          this.registerConsumer(ok.fields.consumerTag, callback);
          resolve(ok.fields);
        });
      });
    }
    async cancel(consumerTag) {
      await promisify((cb) => {
        this._rpc(
          defs2.BasicCancel,
          Args.cancel(consumerTag),
          defs2.BasicCancelOk,
          cb
        );
      })().then((ok) => {
        this.unregisterConsumer(consumerTag);
        return ok.fields;
      });
    }
    get(queue, options) {
      const fields = Args.get(queue, options);
      return new Promise((resolve, reject) => {
        this.sendOrEnqueue(defs2.BasicGet, fields, (err, f) => {
          if (err) return reject(err);
          if (f.id === defs2.BasicGetEmpty) {
            return resolve(false);
          } else if (f.id === defs2.BasicGetOk) {
            const fields2 = f.fields;
            this.handleMessage = acceptMessage((m) => {
              m.fields = fields2;
              resolve(m);
            });
          } else {
            reject(new Error(`Unexpected response to BasicGet: ${inspect(f)}`));
          }
        });
      });
    }
    ack(message, allUpTo) {
      this.sendImmediately(
        defs2.BasicAck,
        Args.ack(message.fields.deliveryTag, allUpTo)
      );
    }
    ackAll() {
      this.sendImmediately(defs2.BasicAck, Args.ack(0, true));
    }
    nack(message, allUpTo, requeue) {
      this.sendImmediately(
        defs2.BasicNack,
        Args.nack(message.fields.deliveryTag, allUpTo, requeue)
      );
    }
    nackAll(requeue) {
      this.sendImmediately(
        defs2.BasicNack,
        Args.nack(0, true, requeue)
      );
    }
    // `Basic.Nack` is not available in older RabbitMQ versions (or in the
    // AMQP specification), so you have to use the one-at-a-time
    // `Basic.Reject`. This is otherwise synonymous with
    // `#nack(message, false, requeue)`.
    reject(message, requeue) {
      this.sendImmediately(
        defs2.BasicReject,
        Args.reject(message.fields.deliveryTag, requeue)
      );
    }
    recover() {
      return this.rpc(
        defs2.BasicRecover,
        Args.recover(),
        defs2.BasicRecoverOk
      );
    }
    qos(count, global2) {
      return this.rpc(
        defs2.BasicQos,
        Args.prefetch(count, global2),
        defs2.BasicQosOk
      );
    }
  }
  Channel.prototype.prefetch = Channel.prototype.qos;
  class ConfirmChannel extends Channel {
    publish(exchange, routingKey, content, options, cb) {
      this.pushConfirmCallback(cb);
      return super.publish(exchange, routingKey, content, options);
    }
    sendToQueue(queue, content, options, cb) {
      return this.publish("", queue, content, options, cb);
    }
    waitForConfirms() {
      const awaiting = [];
      const unconfirmed = this.unconfirmed;
      unconfirmed.forEach((val, index) => {
        if (val !== null) {
          const confirmed = new Promise((resolve, reject) => {
            unconfirmed[index] = (err) => {
              if (val) val(err);
              if (err === null) resolve();
              else reject(err);
            };
          });
          awaiting.push(confirmed);
        }
      });
      if (!this.pending) {
        var cb;
        while (cb = this.unconfirmed.shift()) {
          if (cb) cb(new Error("channel closed"));
        }
      }
      return Promise.all(awaiting);
    }
  }
  channel_model.ConfirmChannel = ConfirmChannel;
  channel_model.Channel = Channel;
  channel_model.ChannelModel = ChannelModel;
  return channel_model;
}
var hasRequiredChannel_api;
function requireChannel_api() {
  if (hasRequiredChannel_api) return channel_api;
  hasRequiredChannel_api = 1;
  var raw_connect = requireConnect().connect;
  var ChannelModel = requireChannel_model().ChannelModel;
  var promisify = require$$1.promisify;
  function connect2(url2, connOptions) {
    return promisify(function(cb) {
      return raw_connect(url2, connOptions, cb);
    })().then(function(conn) {
      return new ChannelModel(conn);
    });
  }
  channel_api.connect = connect2;
  channel_api.credentials = requireCredentials();
  channel_api.IllegalOperationError = requireError().IllegalOperationError;
  return channel_api;
}
var channel_apiExports = requireChannel_api();
const amqp = /* @__PURE__ */ getDefaultExportFromCjs(channel_apiExports);
const __filename$1 = fileURLToPath(import.meta.url);
const __dirname$1 = path.dirname(__filename$1);
class NodeModelConfigManager {
  constructor() {
    this.modelConfigs = /* @__PURE__ */ new Map();
    this.modelDisplayNameMap = /* @__PURE__ */ new Map();
    this.initialized = false;
    this.configDir = this.getConfigDirectory();
  }
  /**
   * 获取配置文件目录路径
   */
  getConfigDirectory() {
    const possiblePaths = [
      // 开发环境路径
      path.join(__dirname$1, "../../src/config/modelParams"),
      // 生产环境路径（相对于打包后的位置）
      path.join(__dirname$1, "../../../src/config/modelParams"),
      path.join(__dirname$1, "../../config/modelParams"),
      // 如果在resources目录中
      path.join(__dirname$1, "../config/modelParams")
    ];
    for (const configPath of possiblePaths) {
      if (fs.existsSync(configPath)) {
        console.log(`Found model config directory at: ${configPath}`);
        return configPath;
      }
    }
    const defaultPath = possiblePaths[0];
    console.warn(`Model config directory not found, using default: ${defaultPath}`);
    return defaultPath;
  }
  static getInstance() {
    if (!NodeModelConfigManager.instance) {
      NodeModelConfigManager.instance = new NodeModelConfigManager();
    }
    return NodeModelConfigManager.instance;
  }
  /**
   * 初始化模型配置
   */
  async initialize() {
    if (this.initialized) return;
    try {
      await this.loadAllModelConfigs();
      this.buildDisplayNameMap();
      this.initialized = true;
      console.log("Node model config manager initialized successfully");
    } catch (error2) {
      console.error("Failed to initialize node model config manager:", error2);
      throw error2;
    }
  }
  /**
   * 加载所有模型配置文件
   */
  async loadAllModelConfigs() {
    try {
      if (!fs.existsSync(this.configDir)) {
        console.warn(`Model config directory not found: ${this.configDir}`);
        return;
      }
      const files = fs.readdirSync(this.configDir);
      const jsonFiles = files.filter((file) => file.endsWith(".json"));
      for (const fileName of jsonFiles) {
        try {
          const filePath = path.join(this.configDir, fileName);
          const fileContent = fs.readFileSync(filePath, "utf-8");
          const config = JSON.parse(fileContent);
          const configId = fileName.replace(".json", "");
          if (!config.category) {
            config.category = this.inferCategory(configId);
          }
          const metadata = {
            id: configId,
            fileName,
            config
          };
          this.modelConfigs.set(configId, metadata);
          console.log(`Loaded model config: ${configId}`);
        } catch (error2) {
          console.warn(`Failed to load model config: ${fileName}`, error2);
        }
      }
    } catch (error2) {
      console.error("Error loading model configs:", error2);
      throw error2;
    }
  }
  /**
   * 根据模型ID推断分类
   */
  inferCategory(modelId) {
    const treeModels = ["DecisionTreeRegressor", "RandomForestRegressor", "GradientBoostingRegressor", "XGBoost"];
    const linearModels = ["LinearRegression", "Ridge", "Lasso", "ElasticNet"];
    if (treeModels.includes(modelId)) return "tree";
    if (linearModels.includes(modelId)) return "linear";
    return "ml";
  }
  /**
   * 构建显示名称映射
   */
  buildDisplayNameMap() {
    this.modelDisplayNameMap.clear();
    this.modelConfigs.forEach((metadata, id) => {
      this.modelDisplayNameMap.set(id, metadata.config.displayName);
    });
  }
  /**
   * 获取所有模型配置
   */
  getAllModels() {
    return Array.from(this.modelConfigs.values());
  }
  /**
   * 根据ID获取模型配置
   */
  getModelById(id) {
    return this.modelConfigs.get(id);
  }
  /**
   * 获取模型显示名称
   */
  getModelDisplayName(modelId) {
    if (!this.initialized || this.modelDisplayNameMap.size === 0) {
      return this.getDefaultDisplayName(modelId);
    }
    return this.modelDisplayNameMap.get(modelId) || this.getDefaultDisplayName(modelId);
  }
  /**
   * 获取默认显示名称（当配置文件不可用时的降级方案）
   */
  getDefaultDisplayName(modelId) {
    const defaultMap = {
      "DecisionTreeRegressor": "决策树",
      "RandomForestRegressor": "随机森林",
      "XGBoost": "XGBoost",
      "GradientBoostingRegressor": "梯度提升回归",
      "SVR": "支持向量机",
      "MLPRegressor": "人工神经网络",
      "LinearRegression": "多元线性回归",
      "Ridge": "岭回归",
      "Lasso": "Lasso回归",
      "ElasticNet": "弹性网络回归"
    };
    return defaultMap[modelId] || modelId;
  }
  /**
   * 获取所有模型的显示名称映射
   */
  getModelDisplayNameMap() {
    const map = {};
    this.modelDisplayNameMap.forEach((displayName, id) => {
      map[id] = displayName;
    });
    return map;
  }
  /**
   * 检查模型是否为ML模型（非线性模型）
   */
  isMLModel(modelId) {
    const metadata = this.getModelById(modelId);
    return metadata ? metadata.config.category !== "linear" : false;
  }
  /**
   * 根据分类获取模型
   */
  getModelsByCategory(category) {
    return Array.from(this.modelConfigs.values()).filter(
      (metadata) => metadata.config.category === category
    );
  }
  /**
   * 重新加载所有配置
   */
  async reload() {
    this.modelConfigs.clear();
    this.modelDisplayNameMap.clear();
    this.initialized = false;
    await this.initialize();
  }
  /**
   * 获取初始化状态
   */
  isInitialized() {
    return this.initialized;
  }
}
const nodeModelConfigManager = NodeModelConfigManager.getInstance();
const getNodeModelDisplayName = (modelId) => nodeModelConfigManager.getModelDisplayName(modelId);
class RabbitMQNotificationService {
  constructor(mainWindow) {
    this.connection = null;
    this.channel = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5e3;
    this.mainWindow = null;
    this.ML_MODEL_NOTIFICATION_QUEUE = "ml_model_notifications";
    this.mainWindow = mainWindow || null;
    this.initializeModelConfig();
  }
  // 初始化模型配置管理器
  async initializeModelConfig() {
    try {
      await nodeModelConfigManager.initialize();
      console.log("RabbitMQ: Model config manager initialized");
    } catch (error2) {
      console.error("RabbitMQ: Failed to initialize model config manager:", error2);
    }
  }
  // 获取模型显示名称（带降级处理）
  getModelDisplayName(modelType) {
    try {
      return getNodeModelDisplayName(modelType);
    } catch (error2) {
      console.warn("Failed to get model display name, using fallback:", error2);
      const defaultMap = {
        "DecisionTreeRegressor": "决策树",
        "RandomForestRegressor": "随机森林",
        "XGBoost": "XGBoost",
        "GradientBoostingRegressor": "梯度提升回归",
        "SVR": "支持向量机",
        "MLPRegressor": "人工神经网络"
      };
      return defaultMap[modelType] || modelType;
    }
  }
  // 设置主窗口引用
  setMainWindow(window2) {
    this.mainWindow = window2;
  }
  // 连接到 RabbitMQ
  async connect(url2 = "amqp://localhost") {
    try {
      console.log("Connecting to RabbitMQ...");
      this.connection = await amqp.connect(url2);
      this.channel = await this.connection.createChannel();
      this.connection.on("error", this.handleConnectionError.bind(this));
      this.connection.on("close", this.handleConnectionClose.bind(this));
      await this.setupQueues();
      await this.startNotificationListener();
      this.isConnected = true;
      this.reconnectAttempts = 0;
      console.log("RabbitMQ connected successfully");
      return true;
    } catch (error2) {
      console.error("Failed to connect to RabbitMQ:", error2);
      this.isConnected = false;
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        setTimeout(() => this.connect(url2), this.reconnectDelay);
      }
      return false;
    }
  }
  // 设置队列
  async setupQueues() {
    if (!this.channel) throw new Error("Channel not available");
    await this.channel.assertQueue(this.ML_MODEL_NOTIFICATION_QUEUE, {
      durable: true,
      arguments: {
        "x-message-ttl": 864e5
        // 24 hours TTL
      }
    });
    console.log("RabbitMQ notification queue setup completed");
  }
  // 开始监听通知
  async startNotificationListener() {
    if (!this.channel) return;
    await this.channel.consume(this.ML_MODEL_NOTIFICATION_QUEUE, (msg) => {
      var _a, _b;
      if (msg) {
        try {
          const notification = JSON.parse(msg.content.toString());
          this.handleModelNotification(notification);
          (_a = this.channel) == null ? void 0 : _a.ack(msg);
        } catch (error2) {
          console.error("Error processing ML model notification:", error2);
          (_b = this.channel) == null ? void 0 : _b.nack(msg, false, false);
        }
      }
    });
    console.log("ML model notification listener started");
  }
  // 处理模型完成通知
  handleModelNotification(notification) {
    console.log("Received ML model notification:", notification);
    this.showSystemNotification(notification);
    this.notifyFrontend("ml-model-notification", notification);
  }
  // 显示系统通知
  showSystemNotification(notification) {
    var _a;
    try {
      const modelName = this.getModelDisplayName(notification.modelType);
      let title;
      let body;
      if (notification.status === "completed") {
        title = `${modelName}模型构建完成`;
        body = notification.message || "模型训练已完成，点击查看结果";
        if ((_a = notification.result) == null ? void 0 : _a.accuracy) {
          body += `
准确率: ${(notification.result.accuracy * 100).toFixed(2)}%`;
        }
      } else {
        title = `${modelName}模型构建失败`;
        body = notification.error || notification.message || "模型训练过程中出现错误";
      }
      const systemNotification = new Notification({
        title,
        body,
        icon: this.getNotificationIcon(notification.status),
        urgency: notification.status === "failed" ? "critical" : "normal",
        timeoutType: "never",
        // 不自动消失
        actions: notification.status === "completed" ? [
          {
            type: "button",
            text: "查看结果"
          }
        ] : []
      });
      let notificationClosed = false;
      systemNotification.on("close", () => {
        console.log("Notification closed by user");
        notificationClosed = true;
      });
      systemNotification.on("click", () => {
        console.log("Notification clicked, closed status:", notificationClosed);
        setTimeout(() => {
          if (!notificationClosed) {
            console.log("Processing notification click");
            this.handleNotificationClick(notification);
          } else {
            console.log("Ignoring click - notification was closed");
          }
        }, 50);
      });
      systemNotification.on("action", (_event, index) => {
        if (index === 0) {
          console.log("Action button clicked");
          this.handleNotificationClick(notification);
        }
      });
      systemNotification.on("failed", (error2) => {
        console.error("Notification failed:", error2);
      });
      systemNotification.show();
    } catch (error2) {
      console.error("Error showing system notification:", error2);
    }
  }
  // 获取通知图标
  getNotificationIcon(_status) {
    return void 0;
  }
  // 处理通知点击
  handleNotificationClick(notification) {
    var _a;
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.focus();
      this.mainWindow.webContents.send("open-model-result", {
        taskId: notification.taskId,
        modelType: notification.modelType,
        result: notification.result,
        resultUrl: (_a = notification.result) == null ? void 0 : _a.resultUrl
      });
    }
  }
  // 通知前端
  notifyFrontend(event, data) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(event, data);
    }
  }
  // 测试通知功能（开发用）
  async testNotification() {
    const testNotification = {
      taskId: "test-" + Date.now(),
      modelType: "DecisionTree",
      status: "completed",
      message: "这是一个测试通知",
      result: {
        accuracy: 0.95,
        metrics: { precision: 0.94, recall: 0.96 }
      },
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
    this.handleModelNotification(testNotification);
  }
  // 处理连接错误
  handleConnectionError(error2) {
    console.error("RabbitMQ connection error:", error2);
    this.isConnected = false;
  }
  // 处理连接关闭
  handleConnectionClose() {
    console.log("RabbitMQ connection closed");
    this.isConnected = false;
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      setTimeout(() => this.connect(), this.reconnectDelay);
    }
  }
  // 断开连接
  async disconnect() {
    try {
      if (this.channel) {
        await this.channel.close();
        this.channel = null;
      }
      if (this.connection) {
        await this.connection.close();
        this.connection = null;
      }
      this.isConnected = false;
      console.log("RabbitMQ disconnected");
    } catch (error2) {
      console.error("Error disconnecting from RabbitMQ:", error2);
    }
  }
  // 检查连接状态
  isConnectionActive() {
    return this.isConnected && this.connection !== null && this.channel !== null;
  }
  // 获取连接信息
  getConnectionInfo() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts
    };
  }
}
const __filename = fileURLToPath$1(import.meta.url);
const __dirname = dirname(__filename);
process.env.DIST_ELECTRON = join(__dirname, "..");
process.env.DIST = join(process.env.DIST_ELECTRON, "../dist");
process.env.PUBLIC = process.env.VITE_DEV_SERVER_URL ? join(process.env.DIST_ELECTRON, "../public") : process.env.DIST;
const isDev = process.env["NODE_ENV"] === "development";
if (release().startsWith("6.1")) app.disableHardwareAcceleration();
if (process.platform === "win32") app.setAppUserModelId(app.getName());
if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}
let win = null;
let splashWin = null;
let rabbitMQService = null;
const preload = join(__dirname, "../preload/index.mjs");
const url = process.env.VITE_DEV_SERVER_URL;
const indexHtml = join(process.env.DIST, "index.html");
function createMenu(label = "进入全屏幕") {
  const menu = Menu.buildFromTemplate(
    appMenu(label)
  );
  Menu.setApplicationMenu(menu);
}
async function createMainWindow(initialRoute) {
  win = new BrowserWindow({
    width: 1024,
    height: 768,
    minWidth: 1024,
    minHeight: 768,
    title: "Main window",
    icon: join(process.env.PUBLIC, "favicon.ico"),
    webPreferences: {
      preload,
      nodeIntegration: false,
      // Recommended for security
      contextIsolation: true
      // Required for contextBridge
    }
  });
  const targetUrl = initialRoute ? `${url}#${initialRoute}` : url;
  const targetIndexHtml = initialRoute ? { pathname: indexHtml, hash: initialRoute } : indexHtml;
  if (process.env.VITE_DEV_SERVER_URL) {
    win.loadURL(targetUrl);
    win.webContents.openDevTools({ mode: "bottom" });
  } else {
    win.loadFile(typeof targetIndexHtml === "string" ? targetIndexHtml : targetIndexHtml.pathname, typeof targetIndexHtml === "string" ? {} : { hash: targetIndexHtml.hash });
  }
  createMenu();
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  win.webContents.setWindowOpenHandler(({ url: url2 }) => {
    const childWindow = new BrowserWindow({
      autoHideMenuBar: true,
      // 隐藏菜单栏，防止出现bug
      webPreferences: {
        preload,
        nodeIntegration: false,
        contextIsolation: true
      }
    });
    childWindow.loadURL(url2);
    return { action: "deny" };
  });
  win.on("enter-full-screen", () => {
    createMenu("退出全屏幕");
  });
  win.on("leave-full-screen", () => {
    createMenu();
  });
  const rabbitmqEnabled = process.env.VITE_RABBITMQ_ENABLED === "true" || process.env.NODE_ENV === "development";
  console.log("RabbitMQ环境变量检查:", {
    VITE_RABBITMQ_ENABLED: process.env.VITE_RABBITMQ_ENABLED,
    NODE_ENV: process.env.NODE_ENV,
    rabbitmqEnabled
  });
  if (rabbitmqEnabled) {
    console.log("RabbitMQ功能已启用，开始初始化...");
    initializeRabbitMQ();
  } else {
    console.log("RabbitMQ功能已禁用");
  }
}
async function createSplashWindow() {
  splashWin = new BrowserWindow({
    width: 600,
    // Smaller size for splash
    height: 400,
    // Smaller size for splash
    frame: false,
    // No window frame
    resizable: false,
    movable: true,
    // Or false if you want it perfectly centered and static
    center: true,
    title: "Loading...",
    // Optional, won't be visible
    icon: join(process.env.PUBLIC, "favicon.ico"),
    // Keep icon consistent
    webPreferences: {
      preload,
      nodeIntegration: false,
      // Recommended for security
      contextIsolation: true
      // Required for contextBridge
    }
  });
  const splashTargetUrl = `${url}#/start`;
  const splashIndexHtml = { pathname: indexHtml, hash: "/start" };
  if (process.env.VITE_DEV_SERVER_URL) {
    splashWin.loadURL(splashTargetUrl);
  } else {
    splashWin.loadFile(splashIndexHtml.pathname, { hash: splashIndexHtml.hash });
  }
}
app.whenReady().then(createSplashWindow);
app.on("window-all-closed", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    app.quit();
  }
});
app.on("second-instance", () => {
  if (win && !win.isDestroyed()) {
    if (win.isMinimized()) win.restore();
    win.focus();
  } else if (splashWin && !splashWin.isDestroyed()) {
    if (splashWin.isMinimized()) splashWin.restore();
    splashWin.focus();
  }
});
app.on("activate", () => {
  const allWindows = BrowserWindow.getAllWindows();
  if (allWindows.length === 0) {
    createSplashWindow();
  } else {
    if (win && !win.isDestroyed()) {
      if (win.isMinimized()) win.restore();
      win.focus();
    } else if (splashWin && !splashWin.isDestroyed()) {
      if (splashWin.isMinimized()) splashWin.restore();
      splashWin.focus();
    } else {
      allWindows[0].focus();
    }
  }
});
ipcMain.on("APP_READY_TO_SHOW_MAIN_WINDOW", (event, args = {}) => {
  if (splashWin) {
    splashWin.close();
    splashWin = null;
  }
  createMainWindow(args.targetRoute);
  if (args.openedFilePath) {
    console.log("Main process: Preparing to send file data:", {
      filePath: args.openedFilePath,
      targetRoute: args.targetRoute,
      singleFileMode: args.singleFileMode
    });
    const sendFileData = () => {
      var _a;
      console.log("Main process: Sending file data events");
      if ((_a = args.targetRoute) == null ? void 0 : _a.includes("/dataManagement/imandex")) {
        console.log("Main process: Sending excel-file-selected event");
        win == null ? void 0 : win.webContents.send("excel-file-selected", args.openedFilePath);
      } else {
        console.log("Main process: Sending workspace-file-selected event");
        win == null ? void 0 : win.webContents.send("workspace-file-selected", args.openedFilePath);
      }
      if (args.singleFileMode) {
        console.log("Main process: Sending set-single-file-mode event");
        win == null ? void 0 : win.webContents.send("set-single-file-mode", args.openedFilePath);
      }
    };
    win == null ? void 0 : win.webContents.once("did-finish-load", sendFileData);
    win == null ? void 0 : win.webContents.once("dom-ready", sendFileData);
    setTimeout(sendFileData, 1e3);
  }
});
const appMenu = (fullscreenLabel) => {
  const menuItems = [
    { label: "关于", role: "about" },
    { label: "开发者工具", role: "toggleDevTools" },
    { label: "强制刷新", role: "forceReload" }
    // Quit is moved to File menu
  ];
  if (!isDev) {
    const devToolsIndex = menuItems.findIndex((item) => item.role === "toggleDevTools");
    if (devToolsIndex > -1) menuItems.splice(devToolsIndex, 1);
    const forceReloadIndex = menuItems.findIndex((item) => item.role === "forceReload");
    if (forceReloadIndex > -1) menuItems.splice(forceReloadIndex, 1);
  }
  const template = [
    {
      label: app.name,
      submenu: menuItems
    },
    {
      label: "文件",
      submenu: [
        {
          label: "导入项目...",
          accelerator: "CmdOrCtrl+Shift+O",
          click: async () => {
            if (win && !win.isDestroyed()) {
              win.focus();
              win.webContents.send("menu-triggered-import-project");
            } else {
              const parentWindow = splashWin && !splashWin.isDestroyed() ? splashWin : void 0;
              const dialogOptions = { properties: ["openDirectory"] };
              const directoryPathResult = parentWindow ? await dialog.showOpenDialog(parentWindow, dialogOptions) : await dialog.showOpenDialog(dialogOptions);
              if (!directoryPathResult.canceled && directoryPathResult.filePaths.length > 0) {
                const projectPath = directoryPathResult.filePaths[0];
                if (splashWin && !splashWin.isDestroyed()) {
                  splashWin.close();
                  splashWin = null;
                }
                createMainWindow(`/workspace/${encodeURIComponent(projectPath)}`);
              }
            }
          }
        },
        {
          label: "打开文件...",
          accelerator: "CmdOrCtrl+O",
          click: async () => {
            if (win && !win.isDestroyed()) {
              win.focus();
              win.webContents.send("menu-triggered-open-file");
            } else {
              const parentWindow = splashWin && !splashWin.isDestroyed() ? splashWin : void 0;
              const dialogOptions = { properties: ["openFile"] };
              const filePathResult = parentWindow ? await dialog.showOpenDialog(parentWindow, dialogOptions) : await dialog.showOpenDialog(dialogOptions);
              if (!filePathResult.canceled && filePathResult.filePaths.length > 0) {
                const filePath = filePathResult.filePaths[0];
                if (splashWin && !splashWin.isDestroyed()) {
                  splashWin.close();
                  splashWin = null;
                }
                const isExcelFile = /\.(xlsx|xls|csv)$/i.test(filePath);
                if (isExcelFile) {
                  createMainWindow(`/dataManagement/imandex`);
                  const sendExcelFile = () => {
                    win == null ? void 0 : win.webContents.send("excel-file-selected", filePath);
                    win == null ? void 0 : win.webContents.send("set-single-file-mode", filePath);
                  };
                  win == null ? void 0 : win.webContents.once("did-finish-load", sendExcelFile);
                  win == null ? void 0 : win.webContents.once("dom-ready", sendExcelFile);
                  setTimeout(sendExcelFile, 1e3);
                } else {
                  const fileDir = filePath.substring(0, filePath.lastIndexOf("/") || filePath.lastIndexOf("\\"));
                  createMainWindow(`/workspace/${encodeURIComponent(fileDir)}`);
                  const sendWorkspaceFile = () => {
                    win == null ? void 0 : win.webContents.send("workspace-file-selected", filePath);
                    win == null ? void 0 : win.webContents.send("set-single-file-mode", filePath);
                  };
                  win == null ? void 0 : win.webContents.once("did-finish-load", sendWorkspaceFile);
                  win == null ? void 0 : win.webContents.once("dom-ready", sendWorkspaceFile);
                  setTimeout(sendWorkspaceFile, 1e3);
                }
              }
            }
          }
        },
        { type: "separator" },
        { label: "退出", role: "quit" }
      ]
    },
    {
      label: "编辑",
      submenu: [
        { label: "撤销", role: "undo" },
        { label: "重做", role: "redo" },
        { type: "separator" },
        { label: "剪切", role: "cut" },
        { label: "复制", role: "copy" },
        { label: "粘贴", role: "paste" },
        { label: "删除", role: "delete" },
        { label: "全选", role: "selectAll" }
      ]
    },
    {
      label: "显示",
      submenu: [
        { label: "加大", role: "zoomIn" },
        { label: "默认大小", role: "resetZoom" },
        { label: "缩小", role: "zoomOut" },
        { type: "separator" },
        {
          label: fullscreenLabel,
          role: "togglefullscreen"
        }
      ]
    }
  ];
  const appNameSubmenu = template[0].submenu;
  if (Array.isArray(appNameSubmenu)) {
    const quitItemIndex = appNameSubmenu.findIndex((item) => item.role === "quit");
    const fileSubMenu = template[1].submenu;
    if (quitItemIndex > -1 && Array.isArray(fileSubMenu) && fileSubMenu.find((item) => item.role === "quit")) {
      appNameSubmenu.splice(quitItemIndex, 1);
    }
  }
  return template;
};
ipcMain.handle("open-win", (_, arg) => {
  const childWindow = new BrowserWindow({
    webPreferences: {
      preload,
      nodeIntegration: false,
      // Then these should also be updated
      contextIsolation: true
    }
  });
  if (process.env.VITE_DEV_SERVER_URL) {
    childWindow.loadURL(`${url}#${arg}`);
  } else {
    childWindow.loadFile(indexHtml, { hash: arg });
  }
});
const require2 = createRequire(import.meta.url);
ipcMain.handle("dialog:openDirectory", async () => {
  const result = await dialog.showOpenDialog({
    properties: ["openDirectory"]
  });
  return result.filePaths[0];
});
ipcMain.handle("dialog:openFile", async () => {
  const result = await dialog.showOpenDialog({
    properties: ["openFile"]
    // You can add filters, e.g., for specific file types
    // filters: [
    //   { name: 'Text Files', extensions: ['txt', 'md'] },
    //   { name: 'All Files', extensions: ['*'] }
    // ]
  });
  if (result.canceled || result.filePaths.length === 0) {
    return null;
  }
  return result.filePaths[0];
});
ipcMain.handle("fs:readDirectory", async (_, dirPath) => {
  const files = await fs.promises.readdir(dirPath, { withFileTypes: true });
  return files.map((dirent) => ({
    name: dirent.name,
    isDirectory: dirent.isDirectory(),
    path: path.join(dirPath, dirent.name)
  }));
});
ipcMain.handle("fs:createDirectory", async (_, targetPath) => {
  await fs.promises.mkdir(targetPath, { recursive: true });
  return { success: true };
});
ipcMain.handle("fs:createFile", async (_, filePath) => {
  await fs.promises.writeFile(filePath, "");
  return { success: true };
});
ipcMain.handle("fs:deletePath", async (_, targetPath) => {
  const stats = await fs.promises.stat(targetPath);
  if (stats.isDirectory()) {
    await fs.promises.rmdir(targetPath, { recursive: true });
  } else {
    await fs.promises.unlink(targetPath);
  }
  return { success: true };
});
const getFileType = (filePath) => {
  const ext = path.extname(filePath).toLowerCase();
  const textExtensions = [".txt", ".md", ".json", ".xml", ".html", ".css", ".js", ".ts", ".vue", ".py", ".java", ".cpp", ".c", ".h", ".sql", ".log", ".ini", ".cfg", ".conf", ".yaml", ".yml"];
  const officeExtensions = [".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"];
  const pdfExtensions = [".pdf"];
  const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg", ".ico"];
  const archiveExtensions = [".zip", ".rar", ".7z", ".tar", ".gz"];
  const executableExtensions = [".exe", ".msi", ".dmg", ".app", ".deb", ".rpm"];
  const mediaExtensions = [".mp3", ".mp4", ".avi", ".mov", ".wav", ".flac"];
  if (textExtensions.includes(ext)) {
    return { type: ext, category: "text", supported: true };
  } else if (officeExtensions.includes(ext)) {
    return { type: ext, category: "office", supported: true };
  } else if (pdfExtensions.includes(ext)) {
    return { type: ext, category: "pdf", supported: true };
  } else if (imageExtensions.includes(ext)) {
    return { type: ext, category: "image", supported: true };
  } else if (archiveExtensions.includes(ext)) {
    return { type: ext, category: "archive", supported: false };
  } else if (executableExtensions.includes(ext)) {
    return { type: ext, category: "executable", supported: false };
  } else if (mediaExtensions.includes(ext)) {
    return { type: ext, category: "media", supported: false };
  } else {
    return { type: ext, category: "unknown", supported: false };
  }
};
ipcMain.handle("fs:readFile", async (_, filePath) => {
  try {
    const content = await fs.promises.readFile(filePath, "utf-8");
    return content;
  } catch (error2) {
    console.error("Error reading file:", error2);
    throw error2;
  }
});
ipcMain.handle("fs:readFileWithType", async (_, filePath) => {
  try {
    const fileInfo = getFileType(filePath);
    if (!fileInfo.supported) {
      return {
        success: false,
        fileInfo,
        error: `不支持的文件类型: ${fileInfo.type}`,
        message: getUnsupportedMessage(fileInfo)
      };
    }
    let content = "";
    let imageData = null;
    if (fileInfo.category === "text") {
      content = await fs.promises.readFile(filePath, "utf-8");
    } else if (fileInfo.category === "office") {
      if (fileInfo.type === ".docx") {
        content = await extractDocxText(filePath);
      } else if (fileInfo.type === ".doc") {
        content = "暂不支持 .doc 格式，请转换为 .docx 格式";
      } else {
        content = `不支持的Office 文档 (${fileInfo.type})，请使用Office工具进行编辑`;
      }
    } else if (fileInfo.category === "pdf") {
      content = "PDF 文档，暂不支持文本提取";
    } else if (fileInfo.category === "image") {
      const imageBuffer = await fs.promises.readFile(filePath);
      const base64Data = imageBuffer.toString("base64");
      const mimeType = getMimeType(fileInfo.type);
      imageData = `data:${mimeType};base64,${base64Data}`;
      content = "";
    }
    return {
      success: true,
      fileInfo,
      content,
      imageData
    };
  } catch (error2) {
    console.error("Error reading file with type:", error2);
    return {
      success: false,
      fileInfo: getFileType(filePath),
      error: (error2 == null ? void 0 : error2.message) || "Unknown error",
      message: "读取文件时发生错误"
    };
  }
});
const getMimeType = (extension) => {
  const mimeTypes = {
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".png": "image/png",
    ".gif": "image/gif",
    ".bmp": "image/bmp",
    ".svg": "image/svg+xml",
    ".ico": "image/x-icon",
    ".webp": "image/webp"
  };
  return mimeTypes[extension.toLowerCase()] || "image/jpeg";
};
const getUnsupportedMessage = (fileInfo) => {
  switch (fileInfo.category) {
    case "image":
      return "图片文件不支持文本编辑，请使用图片查看器打开";
    case "archive":
      return "压缩文件不支持直接编辑，请先解压缩";
    case "executable":
      return "可执行文件不支持编辑";
    case "media":
      return "音视频文件不支持文本编辑，请使用媒体播放器打开";
    default:
      return "不支持的文件类型，无法在文本编辑器中打开";
  }
};
const extractDocxText = async (filePath) => {
  try {
    try {
      const mammoth = require2("mammoth");
      const result = await mammoth.extractRawText({ path: filePath });
      return result.value || "无法提取文档内容";
    } catch (mammothError) {
      console.log("Mammoth not available, using fallback method");
      try {
        const AdmZip = require2("adm-zip");
        const zip = new AdmZip(filePath);
        const documentXml = zip.readAsText("word/document.xml");
        if (documentXml) {
          const textContent = documentXml.replace(/<[^>]*>/g, " ").replace(/\s+/g, " ").trim();
          return textContent || "文档内容为空";
        }
      } catch (zipError) {
        console.log("ZIP extraction failed:", (zipError == null ? void 0 : zipError.message) || "Unknown error");
      }
      return `Word 文档预览

文件路径: ${filePath}

注意：无法提取文档内容，请使用 Microsoft Word 或其他兼容软件打开此文件。

要完整支持 Word 文档，请安装 mammoth 库：npm install mammoth`;
    }
  } catch (error2) {
    console.error("Error extracting DOCX text:", error2);
    return `Word 文档预览

文件路径: ${filePath}

错误：无法读取文档内容 - ${(error2 == null ? void 0 : error2.message) || "Unknown error"}`;
  }
};
ipcMain.handle("fs:readFileBuffer", async (_, filePath) => {
  try {
    const buffer = await fs.promises.readFile(filePath);
    return buffer;
  } catch (error2) {
    console.error("Error reading file buffer:", error2);
    throw error2;
  }
});
ipcMain.handle("fs:writeFile", async (_, filePath, content) => {
  try {
    await fs.promises.writeFile(filePath, content, "utf-8");
    return { success: true };
  } catch (error2) {
    console.error("Error writing file:", error2);
    throw error2;
  }
});
ipcMain.on("workspace-file-selected", (event, filePath) => {
  if (win && !win.isDestroyed()) {
    win.webContents.send("workspace-file-selected", filePath);
  }
});
ipcMain.on("excel-file-selected", (event, filePath) => {
  if (win && !win.isDestroyed()) {
    win.webContents.send("excel-file-selected", filePath);
  }
});
ipcMain.on("app-quit", () => {
  app.quit();
});
async function initializeRabbitMQ() {
  try {
    if (!win) {
      throw new Error("Main window not available for RabbitMQ service");
    }
    rabbitMQService = new RabbitMQNotificationService(win);
    const host = process.env.VITE_RABBITMQ_HOST;
    const port = process.env.VITE_RABBITMQ_PORT;
    const username = process.env.VITE_RABBITMQ_USERNAME;
    const password = process.env.VITE_RABBITMQ_PASSWORD;
    const vhost = process.env.VITE_RABBITMQ_VHOST;
    const rabbitmqUrl = `amqp://${username}:${password}@${host}:${port}${vhost === "/" ? "" : "/" + vhost}`;
    console.log("Connecting to RabbitMQ at:", `amqp://${username}:***@${host}:${port}${vhost === "/" ? "" : "/" + vhost}`);
    const connected = await rabbitMQService.connect(rabbitmqUrl);
    if (connected) {
      console.log("RabbitMQ notification service initialized successfully");
      if (win && !win.isDestroyed()) {
        win.webContents.send("rabbitmq-connected", { connected: true });
      }
    } else {
      console.warn("Failed to connect to RabbitMQ, notifications may not work");
      if (win && !win.isDestroyed()) {
        win.webContents.send("rabbitmq-connected", {
          connected: false,
          error: "Failed to connect to RabbitMQ server"
        });
      }
    }
  } catch (error2) {
    console.error("Error initializing RabbitMQ notification service:", error2);
    if (win && !win.isDestroyed()) {
      win.webContents.send("rabbitmq-connected", {
        connected: false,
        error: error2.message || "Unknown error"
      });
    }
  }
}
ipcMain.handle("rabbitmq:get-connection-status", async () => {
  try {
    if (!rabbitMQService) {
      return {
        connected: false,
        error: "RabbitMQ notification service not initialized"
      };
    }
    const connectionInfo = rabbitMQService.getConnectionInfo();
    return {
      connected: rabbitMQService.isConnectionActive(),
      ...connectionInfo
    };
  } catch (error2) {
    console.error("Error getting connection status:", error2);
    return {
      connected: false,
      error: error2.message || "Unknown error"
    };
  }
});
ipcMain.handle("rabbitmq:test-notification", async () => {
  try {
    if (!rabbitMQService) {
      throw new Error("RabbitMQ notification service not available");
    }
    await rabbitMQService.testNotification();
    return {
      success: true,
      message: "测试通知已发送"
    };
  } catch (error2) {
    console.error("Error sending test notification:", error2);
    return {
      success: false,
      error: error2.message || "Unknown error"
    };
  }
});
app.on("before-quit", async () => {
  if (rabbitMQService) {
    console.log("Disconnecting from RabbitMQ...");
    await rabbitMQService.disconnect();
  }
});
//# sourceMappingURL=index.js.map
