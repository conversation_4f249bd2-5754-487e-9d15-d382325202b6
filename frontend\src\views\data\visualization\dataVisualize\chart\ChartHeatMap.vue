<template>
  <div style="width: 96%; height: 100%; display: flex; flex-direction: column">
    <el-card
      style="
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-self: center;
      "
      class="mt-4"
    >
      <div ref="chartRef" style="width: 100%; height: 100%; display: flex" />
      <!-- display: flex; flex-direction: column -->
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useDark, useECharts } from "@pureadmin/utils";
import { sampleCorrelation } from "simple-statistics";
import { exportChartInstance } from "@/utils/chartExport";

const { isDark } = useDark();
const theme = computed(() => (isDark.value ? "dark" : "default"));

const chartRef = ref();
const { setOptions, getInstance } = useECharts(chartRef, { theme });

const { data: data_table, header: header_table } = defineProps<{
  data: (string | number)[][];
  header: string[];
}>();

const cleanAndParseData = (data: (string | number)[][]): number[][] => {
  return data
    .filter(row =>
      row.every(
        val => val !== "null" && val !== null && val !== undefined && val !== ""
      )
    )
    .map(row => row.map(val => Number(val)).filter(val => !isNaN(val)));
};

const computeCorrelationMatrix = (
  data: number[][]
): [number, number, number][] => {
  // 转置矩阵，使每行代表原始数据的一列
  const transposed = data[0].map((_, colIndex) =>
    data.map(row => row[colIndex])
  );

  const matrixData: [number, number, number][] = [];

  for (let y = 0; y < transposed.length; y++) {
    for (let x = 0; x < transposed.length; x++) {
      try {
        const corr = sampleCorrelation(transposed[y], transposed[x]);
        matrixData.push([x, y, Number(corr.toFixed(2))]);
      } catch (e) {
        // 处理无法计算的情况（如数据不足或零方差）
        matrixData.push([x, y, 0]);
      }
    }
  }

  return matrixData;
};

const buildOptions = (data: [number, number, number][]) => ({
  tooltip: { position: "top" },
  grid: {
    // height: "60%",
    // top: "10%",
    left: "center",
    top: "middle",
    width: "50%",
    height: "70%",
    containLable: true
  },
  xAxis: {
    type: "category",
    data: header_table,
    axisLabel: {
      interval: 0, // 显示所有标签
      rotate: 45 // 调整倾斜角度
    },
    splitArea: { show: true }
  },
  yAxis: {
    type: "category",
    data: header_table,
    splitArea: { show: true }
  },
  visualMap: {
    min: -1,
    max: 1,
    calculable: true,
    orient: "vertical",
    left: "right",
    top: "middle", //保证在中间
    inRange: {
      color: [
        "#0b0920",
        "#541e4e",
        "#821e5a",
        "#b01759",
        "#da2946",
        "#f16445",
        "#f5966c",
        "#f7c7a8"
      ]
    }
  },
  series: [
    {
      name: "相关系数矩阵",
      type: "heatmap",
      data: data,
      label: {
        show: true
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: "rgba(0, 0, 0, 0.5)"
        }
      }
    }
  ]
});

// 暴露给父组件的方法-用于按钮点击时的下载
function exportChartImage(type: "png" | "svg" = "png") {
  const chart = getInstance();
  exportChartInstance(chart, type);
}
defineExpose({ exportChartImage });

onMounted(() => {
  // 先对数据进行清洗
  const cleanedData = cleanAndParseData(data_table);
  // 计算清洗后数据的相关性矩阵
  const heatmapData = computeCorrelationMatrix(cleanedData);

  //@ts-ignore
  setOptions(buildOptions(heatmapData));
});
</script>

<style lang="scss" scoped>
.el-card :deep(.el-card__body) {
  width: 100%;
  display: flex;
  flex: 1;
}
</style>
