<template>
  <div class="params-setting-container">
    <!-- 评估设置 -->
    <el-card shadow="never" class="param-card">
      <template #header>
        <span class="param-card-title">模型评估设置</span>
      </template>

      <!-- 划分训练测试集部分 -->
      <div class="param-section">
        <el-checkbox v-model="evaluationParams.splitDataset">划分训练/测试集</el-checkbox>
        <div v-if="evaluationParams.splitDataset" class="param-content">
          <div class="param-item">
            <span class="param-label">训练集比例：</span>
            <el-slider
              v-model="evaluationParams.trainRatio"
              :min="evaluationConfig.trainRatio?.min || 50"
              :max="evaluationConfig.trainRatio?.max || 90"
              :step="evaluationConfig.trainRatio?.step || 5"
              show-input
            />
          </div>
          <div class="param-item">
            <span class="param-label">随机种子：</span>
            <el-input-number
              v-model="evaluationParams.randomState"
              :min="evaluationConfig.randomState?.min || 0"
              :max="evaluationConfig.randomState?.max || 1000"
              controls-position="right"
            />
          </div>
        </div>
      </div>

      <!-- 交叉验证部分 -->
      <div class="param-section">
        <el-checkbox v-model="evaluationParams.useModelValidation">检验模型（交叉验证）</el-checkbox>
        <div v-if="evaluationParams.useModelValidation" class="param-content">
          <div class="param-item">
            <el-radio-group v-model="evaluationParams.validationType">
              <el-radio value="k-fold">k折交叉验证</el-radio>
              <el-radio value="leave-one-out">留一法验证</el-radio>
            </el-radio-group>
          </div>
          <div v-if="evaluationParams.validationType === 'k-fold'" class="param-item">
            <span class="param-label">k值：</span>
            <el-input-number
              v-model="evaluationParams.kFolds"
              :min="evaluationConfig.kFolds?.min || 2"
              :max="evaluationConfig.kFolds?.max || 10"
              controls-position="right"
            />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 模型参数设置 -->
    <el-card shadow="never" class="param-card">
      <template #header>
        <div class="param-card-header">
          <span class="param-card-title">模型参数设置</span>
          <el-switch
            v-model="useCustomParams"
            active-text="自定义参数"
            inactive-text="使用预设"
            @change="handleCustomParamsToggle"
          />
        </div>
      </template>

      <!-- 预设参数模式 -->
      <div v-if="!useCustomParams && modelConfig" class="preset-params">
        <div v-for="(paramConfig, key) in modelConfig.defaultParams" :key="key" class="param-item">
          <span class="param-label" :title="paramConfig.description">
            {{ paramConfig.displayName }}：
          </span>
          <div class="param-control">
            <!-- 数字输入 -->
            <el-input-number
              v-if="paramConfig.type === 'number'"
              v-model="algorithmParams[key]"
              :min="paramConfig.min"
              :max="paramConfig.max"
              :step="paramConfig.step || 0.001"
              :precision="getPrecision(paramConfig.step)"
              controls-position="right"
              :placeholder="paramConfig.nullable ? '留空表示自动' : ''"
              clearable
            />
            <!-- 布尔开关 -->
            <el-switch
              v-else-if="paramConfig.type === 'boolean'"
              v-model="algorithmParams[key]"
            />
            <!-- 选择器 -->
            <el-select
              v-else-if="paramConfig.type === 'select'"
              v-model="algorithmParams[key]"
              placeholder="请选择"
            >
              <el-option
                v-for="option in paramConfig.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <!-- 文本输入 -->
            <el-input
              v-else
              v-model="algorithmParams[key]"
              :placeholder="paramConfig.description"
            />
          </div>
        </div>
      </div>

      <!-- 自定义参数模式 -->
      <div v-else class="custom-params">
        <div class="param-section">
          <div class="param-header">
            <span>自定义模型参数</span>
            <el-button
              link
              size="small"
              @click="resetToPreset"
              :disabled="!modelConfig"
            >
              重置为预设
            </el-button>
          </div>
          <div class="param-content-input">
            <el-input
              v-model="customParamsText"
              type="textarea"
              :rows="8"
              :placeholder="getCustomParamsPlaceholder()"
              @blur="validateCustomParams"
              @input="clearCustomParamsError"
            />
            <div v-if="customParamsError" class="error-message">
              {{ customParamsError }}
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, computed } from "vue";
import { ElMessage } from "element-plus";
import { ModelMetaEvaluation, ModelAlgorithm } from "@/types/models";
import {
  loadModelParams,
  getDefaultParams,
  validateModelParams,
  type ModelParamConfig,
  type SupportedModel
} from "@/utils/modelParamsLoader";

const emit = defineEmits<{
  (e: "update:params", value: {
    algorithm: ModelAlgorithm,
    evaluation: ModelMetaEvaluation,
  }): void;
}>();

const props = defineProps<{
  algorithmName: string;
}>();

// 状态管理
const loading = ref(false);
const modelConfig = ref<ModelParamConfig | null>(null);
const evaluationConfig = ref<Record<string, any>>({});
const useCustomParams = ref(false);
const customParamsText = ref("");
const customParamsError = ref("");
const showPreview = ref(false);

// 算法参数
const algorithmParams = reactive<Record<string, any>>({});

// 评估参数
interface EvaluationParams {
  splitDataset: boolean;
  trainRatio: number;
  randomState: number;
  useModelValidation: boolean;
  validationType: "k-fold" | "leave-one-out";
  kFolds: number;
}

const evaluationParams = reactive<EvaluationParams>({
  splitDataset: false,
  trainRatio: 70,
  randomState: 42,
  useModelValidation: false,
  validationType: "k-fold",
  kFolds: 5,
});

// 计算属性
const finalAlgorithmParams = computed(() => {
  if (useCustomParams.value) {
    try {
      const customParams = JSON.parse(customParamsText.value || "{}");
      // 确保返回的是一个有效的对象
      if (typeof customParams === 'object' && customParams !== null && !Array.isArray(customParams)) {
        return customParams;
      }
      return {};
    } catch {
      return {};
    }
  }
  // 预设参数模式：过滤掉null值（如果参数配置允许nullable）
  const filteredParams: Record<string, any> = {};
  Object.entries(algorithmParams).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      filteredParams[key] = value;
    }
  });
  return filteredParams;
});

const finalEvaluationParams = computed((): ModelMetaEvaluation => {
  return {
    cv: evaluationParams.useModelValidation && evaluationParams.validationType === "k-fold"
      ? { k: evaluationParams.kFolds, randomState: evaluationParams.randomState }
      : undefined,
    loocv: evaluationParams.useModelValidation && evaluationParams.validationType === "leave-one-out",
    test: evaluationParams.splitDataset
      ? { size: 1 - evaluationParams.trainRatio / 100, randomState: evaluationParams.randomState }
      : undefined,
  };
});

// 方法
const loadModelConfiguration = async () => {
  if (!props.algorithmName) return;

  loading.value = true;
  try {
    // 加载模型配置
    const config = await loadModelParams(props.algorithmName as SupportedModel);
    modelConfig.value = config;
    evaluationConfig.value = config.evaluation;

    // 初始化评估参数
    Object.entries(config.evaluation).forEach(([key, configValue]) => {
      if (key in evaluationParams && typeof configValue === 'object' && 'value' in configValue) {
        (evaluationParams as any)[key] = configValue.value;
      }
    });

    // 初始化算法参数
    const defaultParams = await getDefaultParams(props.algorithmName as SupportedModel);
    Object.assign(algorithmParams, defaultParams);

    // 初始化自定义参数文本
    customParamsText.value = JSON.stringify(defaultParams, null, 2);

  } catch (error) {
    console.error("Failed to load model configuration:", error);
    ElMessage.error("加载模型配置失败");
  } finally {
    loading.value = false;
  }
};

const handleCustomParamsToggle = (value: boolean) => {
  if (value) {
    // 切换到自定义模式，同步当前参数到文本框
    customParamsText.value = JSON.stringify(algorithmParams, null, 2);
    customParamsError.value = "";
  } else {
    // 切换到预设模式，验证并应用自定义参数
    validateCustomParams();
  }
};

const validateCustomParams = async () => {
  if (!useCustomParams.value || !customParamsText.value.trim()) {
    customParamsError.value = "";
    return;
  }

  try {
    const params = JSON.parse(customParamsText.value);

    // 基本类型验证
    if (typeof params !== 'object' || params === null || Array.isArray(params)) {
      customParamsError.value = "参数必须是一个对象";
      return;
    }

    // 如果有模型配置，进行详细验证
    if (modelConfig.value) {
      const validation = await validateModelParams(props.algorithmName as SupportedModel, params);
      if (!validation.valid) {
        customParamsError.value = validation.errors.join("; ");
      } else {
        customParamsError.value = "";
      }
    } else {
      // 没有模型配置时，只做基本验证
      customParamsError.value = "";
    }
  } catch (error) {
    customParamsError.value = "JSON格式错误，请检查语法";
  }
};

const resetToPreset = async () => {
  if (!modelConfig.value) return;

  try {
    const defaultParams = await getDefaultParams(props.algorithmName as SupportedModel);
    customParamsText.value = JSON.stringify(defaultParams, null, 2);
    customParamsError.value = "";
    ElMessage.success("已重置为预设参数");
  } catch (error) {
    ElMessage.error("重置失败");
  }
};

const getPrecision = (step?: number): number => {
  if (!step) return 3;
  const stepStr = step.toString();
  const decimalIndex = stepStr.indexOf('.');
  return decimalIndex === -1 ? 0 : stepStr.length - decimalIndex - 1;
};

const getCustomParamsPlaceholder = (): string => {
  const modelName = props.algorithmName;
  const examples: Record<string, string> = {
    LinearRegression: `请输入JSON格式的参数，例如：
{
  "fit_intercept": true,
  "normalize": false,
  "copy_X": true,
  "n_jobs": null,
  "positive": false
}`,
    Ridge: `请输入JSON格式的参数，例如：
{
  "alpha": 1.0,
  "fit_intercept": true,
  "normalize": false,
  "solver": "auto",
  "max_iter": null,
  "tol": 0.001
}`,
    Lasso: `请输入JSON格式的参数，例如：
{
  "alpha": 1.0,
  "fit_intercept": true,
  "normalize": false,
  "max_iter": 1000,
  "tol": 0.0001,
  "selection": "cyclic"
}`,
    ElasticNet: `请输入JSON格式的参数，例如：
{
  "alpha": 1.0,
  "l1_ratio": 0.5,
  "fit_intercept": true,
  "normalize": false,
  "max_iter": 1000,
  "tol": 0.0001
}`
  };

  return examples[modelName] || `请输入JSON格式的参数，例如：
{
  "param1": "value1",
  "param2": 123,
  "param3": true
}`;
};

const clearCustomParamsError = () => {
  if (customParamsError.value) {
    customParamsError.value = "";
  }
};

// 监听参数变化并emit - 恢复原先的通信格式
watch(
  [finalAlgorithmParams, finalEvaluationParams],
  ([newAlgorithmParams, newEvaluationParams]) => {
    // 构建符合原先接口的算法参数结构
    const algorithmResult: ModelAlgorithm = {
      name: props.algorithmName,
      params: newAlgorithmParams, // 自定义参数直接放在params字段中
    };

    // 构建符合原先接口的评估参数结构
    const evaluationResult: ModelMetaEvaluation = {
      cv: newEvaluationParams.cv,
      loocv: newEvaluationParams.loocv,
      test: newEvaluationParams.test,
    };

    emit("update:params", {
      algorithm: algorithmResult,
      evaluation: evaluationResult
    });
  },
  { deep: true }
);

// 监听算法名称变化
watch(
  () => props.algorithmName,
  () => {
    loadModelConfiguration();
  },
  { immediate: true }
);

// 重置参数的方法
const resetParams = async () => {
  console.log("resetParams called for linear model");

  // 重置状态
  useCustomParams.value = false;
  customParamsText.value = "";
  customParamsError.value = "";
  showPreview.value = false;

  // 清空算法参数
  Object.keys(algorithmParams).forEach((key) => {
    delete algorithmParams[key];
  });

  // 重置评估参数为默认值
  Object.assign(evaluationParams, {
    splitDataset: false,
    trainRatio: 70,
    randomState: 42,
    useModelValidation: false,
    validationType: "k-fold",
    kFolds: 5,
  });

  // 清空模型配置，强制重新加载
  modelConfig.value = null;
  evaluationConfig.value = {};

  // 重新加载配置
  await loadModelConfiguration();

  console.log("resetParams completed for linear model");
};

// 强制重新加载配置的方法
const forceReloadConfig = async () => {
  console.log("forceReloadConfig called for linear model");

  // 完全清空所有状态
  useCustomParams.value = false;
  customParamsText.value = "";
  customParamsError.value = "";
  showPreview.value = false;

  // 清空所有参数
  Object.keys(algorithmParams).forEach((key) => {
    delete algorithmParams[key];
  });

  // 重置评估参数
  Object.assign(evaluationParams, {
    splitDataset: false,
    trainRatio: 70,
    randomState: 42,
    useModelValidation: false,
    validationType: "k-fold",
    kFolds: 5,
  });

  // 清空配置并重新加载
  modelConfig.value = null;
  evaluationConfig.value = {};

  // 重新加载配置
  await loadModelConfiguration();

  console.log("forceReloadConfig completed for linear model");
};

// 暴露重置方法给父组件
defineExpose({
  resetParams,
  forceReloadConfig,
});

// 生命周期
onMounted(() => {
  loadModelConfiguration();
});
</script>

<style scoped>
.params-setting-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 模型简介卡片 */
.model-intro-card {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border: 1px solid var(--el-color-primary-light-8);
  margin-bottom: 16px;
}

.model-intro-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.model-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.model-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-color-primary);
}

.expand-button {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-text-color-regular);
  transition: color 0.3s ease;
}

.expand-button:hover {
  color: var(--el-color-primary);
}

.expand-icon {
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* 基本描述 */
.model-basic-description {
  color: var(--el-text-color-regular);
  line-height: 1.6;
  font-size: 14px;
  margin-bottom: 0;
}

/* 详细介绍 */
.model-detailed-intro {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 使用建议 */
.model-tips {
  margin-bottom: 20px;
}

.tips-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tips-title::before {
  content: "💡";
  font-size: 16px;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 6px;
  border-left: 3px solid var(--el-color-primary-light-5);
}

.tip-icon {
  color: var(--el-color-primary);
  margin-top: 2px;
  flex-shrink: 0;
}

.tip-text {
  color: var(--el-text-color-regular);
  font-size: 13px;
  line-height: 1.5;
}

/* 参数说明 */
.params-intro {
  margin-top: 20px;
}

.params-intro-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.params-intro-title::before {
  content: "⚙️";
  font-size: 16px;
}

.params-intro-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
}

.param-intro-item {
  padding: 10px 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.param-name {
  display: block;
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 13px;
  margin-bottom: 4px;
}

.param-description {
  display: block;
  color: var(--el-text-color-regular);
  font-size: 12px;
  line-height: 1.4;
}

/* 参数卡片 */
.param-card {
  border: 1px solid var(--el-border-color-light);
}

.param-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.param-card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 参数区域 */
.param-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  margin-bottom: 16px;
}

.param-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-left: 32px;
  padding-top: 12px;
}

.param-item {
  display: flex;
  align-items: center;
  gap: 16px;
  min-height: 36px;
  margin-bottom: 16px;
}

.param-label {
  min-width: 140px;
  color: var(--el-text-color-regular);
  font-size: 14px;
  flex-shrink: 0;
  text-align: right;
  padding-right: 8px;
}

.param-control {
  flex: 1;
  max-width: 320px;
  display: flex;
  align-items: center;
}

/* 预设参数区域 */
.preset-params {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 20px;
}

.preset-params .param-item:last-child {
  margin-bottom: 0;
}

/* 自定义参数区域 */
.custom-params {
  padding: 20px;
}

.param-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.param-content-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.error-message {
  color: var(--el-color-danger);
  font-size: 12px;
  line-height: 1.4;
  padding: 4px 8px;
  background: var(--el-color-danger-light-9);
  border-radius: 4px;
  border-left: 3px solid var(--el-color-danger);
}

/* 深度样式 */
:deep(.el-textarea__inner) {
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 13px;
  line-height: 1.4;
}

:deep(.el-slider) {
  flex: 1;
  margin-right: 16px;
  min-width: 120px;
}

:deep(.el-slider__input) {
  width: 90px;
}

:deep(.el-slider .el-input-number) {
  width: 90px;
}

:deep(.el-input-number) {
  width: 140px;
}

:deep(.el-select) {
  width: 220px;
}

:deep(.el-switch) {
  margin-left: 0;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-switch__label) {
  font-size: 13px;
}

:deep(.el-alert) {
  padding: 8px 12px;
}

:deep(.el-alert__title) {
  font-size: 13px;
  line-height: 1.4;
}

/* 单选按钮组样式 */
:deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

:deep(.el-radio) {
  margin-right: 0;
}

/* 复选框样式 */
:deep(.el-checkbox) {
  margin-right: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .param-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 20px;
  }

  .param-label {
    min-width: auto;
    text-align: left;
    padding-right: 0;
    font-weight: 600;
  }

  .param-control {
    width: 100%;
    max-width: none;
  }

  .param-content {
    margin-left: 16px;
  }

  .model-intro-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .model-title-section {
    width: 100%;
  }

  .expand-button {
    align-self: flex-end;
  }

  .param-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .params-intro-list {
    grid-template-columns: 1fr;
  }

  .param-intro-item {
    padding: 8px 10px;
  }

  .tips-list {
    gap: 6px;
  }

  .tip-item {
    padding: 6px 10px;
  }

  .model-name {
    font-size: 16px;
  }

  :deep(.el-slider) {
    margin-right: 0;
    margin-bottom: 8px;
  }

  :deep(.el-input-number),
  :deep(.el-select) {
    width: 100%;
    max-width: 280px;
  }
}
</style>
