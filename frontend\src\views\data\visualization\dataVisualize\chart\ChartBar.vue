<template>
  <div style="width: 96%; height: 100vh">
    <el-card style="margin-top: 30px">
      <div ref="chartRef" style="width: 100%; height: 60vh" />

      <el-select
        v-model="selectedColumn"
        placeholder="请选择要分析的列"
        @change="updateChartData"
      >
        <el-option
          v-for="(col, index) in header_table"
          :key="index"
          :label="col"
          :value="index"
        />
      </el-select>

      <el-input-number
        v-model="numBins"
        :min="2"
        :max="20"
        label="区间数："
        @change="updateChartData"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useDark, useECharts } from "@pureadmin/utils";
import { exportChartInstance } from "@/utils/chartExport";

// 父组件传入的数据
const { data: data_table, header: header_table } = defineProps([
  "data",
  "header"
]);

// 用户选择的列索引
const selectedColumn = ref<number | null>(null);
// 用户选择的区间数（默认为5）
const numBins = ref(5);

// 兼容dark主题
const { isDark } = useDark();
let theme = computed(() => {
  return isDark.value ? "dark" : "default";
});

// 初始化ECharts
const chartRef = ref();
const { setOptions, getInstance } = useECharts(chartRef, { theme });

// 更新图表数据
const updateChartData = () => {
  if (selectedColumn.value !== null) {
    // 获取选中的列数据，并清理空格和过滤掉 'null' 字符串
    const selectedData = data_table
      .map(row => row[selectedColumn.value])
      .map(item => item?.trim())
      .filter(item => item !== "null" && item !== null && item !== "");

    // 处理数值型数据（可能是连续的也可能是离散的）
    const isNumeric = selectedData.every(
      item => !isNaN(Number(item)) && item !== "null" && item !== null
    );

    if (isNumeric) {
      // 转换为数值型
      const values = selectedData
        .map(item => Number(item))
        .filter(item => !isNaN(item)); // 过滤掉 NaN 和无效值

      if (values.length === 0) return; // 如果没有有效值，返回

      // 检查数据中的唯一值数量
      const uniqueValues = [...new Set(values)];

      // 如果唯一值数量较少（小于等于设定的阈值，例如10），或者少于区间数，则按离散值处理
      if (uniqueValues.length <= 10 || uniqueValues.length <= numBins.value) {
        // 按离散值处理，计算每个唯一值的频次
        const freqMap = values.reduce(
          (acc, curr) => {
            acc[curr] = (acc[curr] || 0) + 1;
            return acc;
          },
          {} as { [key: string]: number }
        );

        // 按数值大小排序键
        const sortedKeys = Object.keys(freqMap).sort(
          (a, b) => Number(a) - Number(b)
        );

        // 图表配置更新，按排序后的键显示
        setOptions({
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            }
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true
          },
          xAxis: [
            {
              type: "category",
              data: sortedKeys,
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis: [
            {
              type: "value"
            }
          ],
          series: [
            {
              name: "频次",
              type: "bar",
              barWidth: "60%",
              data: sortedKeys.map(key => freqMap[key])
            }
          ]
        });
      } else {
        // 处理为连续数据，需要分区间
        const min = Math.min(...values);
        const max = Math.max(...values);

        // 创建精确数量的区间
        const bins = Array.from({ length: numBins.value }, (_, i) => {
          const binStart = min + (max - min) * (i / numBins.value);
          const binEnd = min + (max - min) * ((i + 1) / numBins.value);
          return {
            range: `${binStart.toFixed(2)}-${binEnd.toFixed(2)}`,
            count: 0
          };
        });

        // 计算每个值所在的区间，确保最大值落在最后一个区间
        values.forEach(value => {
          // 特殊处理最大值，确保它落在最后一个区间
          if (value === max) {
            bins[numBins.value - 1].count++;
          } else {
            // 计算区间索引
            const binIndex = Math.min(
              Math.floor(((value - min) / (max - min)) * numBins.value),
              numBins.value - 1
            );
            bins[binIndex].count++;
          }
        });

        // 图表配置更新
        setOptions({
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            }
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true
          },
          xAxis: [
            {
              type: "category",
              data: bins.map(bin => bin.range),
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis: [
            {
              type: "value"
            }
          ],
          series: [
            {
              name: "频次",
              type: "bar",
              barWidth: "60%",
              data: bins.map(bin => bin.count)
            }
          ]
        });
      }
    } else {
      // 离散数据，直接按出现频次计算，排除 null 和空值
      const filteredData = selectedData.filter(
        item => item !== "null" && item !== null && item !== ""
      );

      const freqMap = filteredData.reduce(
        (acc, curr) => {
          acc[curr] = (acc[curr] || 0) + 1;
          return acc;
        },
        {} as { [key: string]: number }
      );

      // 图表配置更新
      setOptions({
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          }
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            data: Object.keys(freqMap),
            axisTick: {
              alignWithLabel: true
            }
          }
        ],
        yAxis: [
          {
            type: "value"
          }
        ],
        series: [
          {
            name: "频次",
            type: "bar",
            barWidth: "60%",
            data: Object.values(freqMap)
          }
        ]
      });
    }
  }
};

// 暴露给父组件的方法-用于按钮点击时的下载
function exportChartImage(type: "png" | "svg" = "png") {
  const chart = getInstance();
  exportChartInstance(chart, type);
}
defineExpose({ exportChartImage });

onMounted(() => {
  // 初始化时加载第一个列的数据
  if (header_table.length > 0) {
    selectedColumn.value = 0; // 默认选择第一列
    updateChartData(); // 更新图表
  }
});
</script>
